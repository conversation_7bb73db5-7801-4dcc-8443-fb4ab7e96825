DO $RATINGBOTINIT$
DECLARE tempId smallint;
DECLARE ratingBotWorkerId smallint; -- Id обработчика оценочного бота
DECLARE ratingBotStateId smallint; -- Id этапа 'Обработка оценочным ботом'
DECLARE ratingBotWaitStateId smallint; -- Id этапа 'Оценочный бот ожидает ответа от клиента'
DECLARE ratingBotWaitStateMaxDurationsId smallint; -- Id таймаута для этапа 'Оценочный бот ожидает ответа от клиента'
DECLARE ratingBotProcessStatusId smallint; -- Id статуса 'Обработка оценочным ботом'
DECLARE ratingBotWaitStatusId smallint; -- Id статуса 'Оценочный бот ожидает ответа от клиента'
DECLARE ratingBotStateWorkId smallint; -- Id работы на этапе 'Обработка оценочным ботом'

DECLARE ratingBotBaseScriptId smallint; -- Id базового скрипта оценочного бота
DECLARE ratingBotBaseScriptVersionId smallint; -- Id базового скрипта оценочного бота
DECLARE ratingBotBaseScriptInitStepId smallint; -- Id начального шага скрипта
DECLARE ratingBotBaseScriptFinishStepId smallint; -- Id конечного шага скрипта

BEGIN

-- Добавление кастома OperatorWorkCompleteCode в CustomAttributesMapForRequests
RAISE NOTICE '[RatingBotInit.CA] Start add OperatorWorkCompleteCode to CustomAttributesMapForRequests settings';
UPDATE "CRPM_CFG"."Settings"
SET "Configuration" = regexp_replace(
    "Configuration",
    '(<!--DateTime-->)',
    '<Mapping Code="OperatorWorkCompleteCode" Type="String" Index="47" />
    \1',
    'i'
)
WHERE "Title" = 'CustomAttributesMapForRequests' AND "Configuration" NOT LIKE '%<Mapping Code="OperatorWorkCompleteCode"%';
RAISE NOTICE '[RatingBotInit.CA] End updating CustomAttributesMapForRequests settings';
-------------------------------------------------------------------------------------------

-- Добавление обработчика RatingBot
RAISE NOTICE '[RatingBotInit.Worker] Start add RatingBot worker';
INSERT INTO "CRPM_CFG"."WorkExecutors"
("RequestStreamId", "ChannelId", "TypeCode", "IsEnabled", "Title", "Configuration", "RowVersion")
SELECT 1, NULL, 'MM.ChatBotWorker', true, 'Обработка оценочным ботом', '<?xml version="1.0" encoding="utf-16"?>
<ChatBotWorkerConfiguration ServicesConfigName="ServicesConfiguration" SendMessageServiceKey="SendMessage" ChatBotServiceKey="RatingBot">
  <ChatBotSystemNameQueueAttribute>Queue.RatingBotSystemName</ChatBotSystemNameQueueAttribute>
  <Channels>
    <ChatbotWorkerChatChannelConfiguration>
      <BotSenderName>Product bot</BotSenderName>
      <ChannelName>CHANNEL.CHAT</ChannelName>
      <ChannelId>21</ChannelId>
      <ButtonsAttributeName>TemplateKeyboard</ButtonsAttributeName>
    </ChatbotWorkerChatChannelConfiguration>
    <ChatbotWorkerViberChannelConfiguration>
      <BotSenderName>Product bot</BotSenderName>
      <ChannelName>CHANNEL.VIBER</ChannelName>
      <ChannelId>71</ChannelId>
    </ChatbotWorkerViberChannelConfiguration>
    <ChatbotWorkerTelegramChannelConfiguration>
      <BotSenderName>Product bot</BotSenderName>
      <ChannelName>CHANNEL.TELEGRAM</ChannelName>
      <ChannelId>91</ChannelId>
    </ChatbotWorkerTelegramChannelConfiguration>
    <ChatbotWorkerVkChannelConfiguration>
      <BotSenderName>Product bot</BotSenderName>
      <ChannelName>CHANNEL.VK</ChannelName>
      <ChannelId>81</ChannelId>
    </ChatbotWorkerVkChannelConfiguration>
    <ChatbotWorkerFacebookChannelConfiguration>
      <BotSenderName>Product bot</BotSenderName>
      <ChannelName>CHANNEL.FACEBOOK</ChannelName>
      <ChannelId>91</ChannelId>
    </ChatbotWorkerFacebookChannelConfiguration>
    <ChatbotWorkerMfmsWhatsappChannelConfiguration>
      <BotSenderName>Product bot</BotSenderName>
      <ChannelName>CHANNEL.MFMS.WHATSAPP</ChannelName>
      <ChannelId>101</ChannelId>
    </ChatbotWorkerMfmsWhatsappChannelConfiguration>
    <ChatbotWorkerMfmsiMessageChannelConfiguration>
      <BotSenderName>Product bot</BotSenderName>
      <ChannelName>CHANNEL.MFMS.IMESSAGE</ChannelName>
      <ChannelId>51</ChannelId>
    </ChatbotWorkerMfmsiMessageChannelConfiguration>
    <ChatbotWorkerInfobipWhatsappChannelConfiguration>
      <BotSenderName>Product bot</BotSenderName>
      <ChannelName>CHANNEL.INFOBIP.WHATSAPP</ChannelName>
      <ChannelId>111</ChannelId>
    </ChatbotWorkerInfobipWhatsappChannelConfiguration>
    <ChatbotWorkerSMSChannelConfiguration>
      <BotSenderName>Product bot</BotSenderName>
      <ChannelName>CHANNEL.SMS</ChannelName>
      <ChannelId>41</ChannelId>
    </ChatbotWorkerSMSChannelConfiguration>
    <ChatbotWorkerMFWhatsAppChannelConfiguration>
      <BotSenderName>Product bot</BotSenderName>
      <ChannelName>CHANNEL.MF.WHATSAPP</ChannelName>
      <ChannelId>141</ChannelId>
    </ChatbotWorkerMFWhatsAppChannelConfiguration>
  </Channels>
  <ThemeDictionaryId>8ebfc4a1-1768-11ea-80c4-00155d0a0e18</ThemeDictionaryId>
  <ChatBotSystems>
    <ChatBotSystem ChatBotServiceKey="RatingBot" Name="RatingBot">
      <ChatBotSenderType>Бот</ChatBotSenderType>
    </ChatBotSystem>
  </ChatBotSystems>
</ChatBotWorkerConfiguration>', 638670451529509966
WHERE NOT EXISTS(SELECT "Id" FROM "CRPM_CFG"."WorkExecutors" WHERE "TypeCode" = 'MM.ChatBotWorker' AND "Title" = 'Обработка оценочным ботом')
RETURNING "Id" INTO ratingBotWorkerId;
RAISE NOTICE '[RatingBotInit.Worker] End created RatingBot worker with Id: %', ratingBotWorkerId;
-------------------------------------------------------------------------------------------

-- Добавление обработчика RatingBot в менеджеры автоматических работ
RAISE NOTICE '[RatingBotInit.Worker] Start add RatingBot worker to automatic workers';
INSERT INTO "CRPM_CFG"."WorkExecutionConfigurations" ("WorkExecutionManagerId", "WorkExecutorId", "Count", "RowVersion")
SELECT 1, 33, 100, 638670432759568423
WHERE NOT EXISTS(SELECT "Id" FROM "CRPM_CFG"."WorkExecutionConfigurations" WHERE "WorkExecutorId" = 33 AND "WorkExecutionManagerId" = 1);
RAISE NOTICE '[RatingBotInit.Worker] End added RatingBot worker to automatic workers';
-------------------------------------------------------------------------------------------
-- Создание этапов для оценочного бота

-- Добавление этапа 'Обработка оценочным ботом'
RAISE NOTICE '[RatingBotInit.State] Start add RatingBot state';
INSERT INTO "CRPM_CFG"."RequestStates"
("Title", "SchemeId", "IsDefault", "IsTerminating", "MaxDurationId", "RowVersion", "Code", "DefaultTransitionOnSuccessId", "DefaultTransitionOnErrorId")
SELECT 'Обработка оценочным ботом', 1, false, false, NULL, 638676128209867600, 'RATINGBOT', (SELECT "Id" FROM "CRPM_CFG"."RequestStateStatuses" WHERE "Code"='CLOSED.CRASH'), (SELECT "Id" FROM "CRPM_CFG"."RequestStateStatuses" WHERE "Code"='CLOSED.CRASH')
WHERE NOT EXISTS(SELECT "Id" FROM "CRPM_CFG"."RequestStates" WHERE "Code" = 'RATINGBOT')
RETURNING "Id" INTO ratingBotStateId;
RAISE NOTICE '[RatingBotInit.State] End created RatingBot state with Id: %', ratingBotStateId;

-- Добавление таймаута для этапа 'Оценочный бот ожидает ответа от клиента'
RAISE NOTICE '[RatingBotInit.State] Start add timeout for wait RatingBot state';
INSERT INTO "CRPM_CFG"."MaxDurations" ("Duration", "TargetStatusId", "RowVersion") SELECT 900, 27, 638681292538227760
RETURNING "Id" INTO ratingBotWaitStateMaxDurationsId;
RAISE NOTICE '[RatingBotInit.State] End added timeout for wait RatingBot state with Id: %', ratingBotWaitStateMaxDurationsId;

-- Добавление этапа 'Оценочный бот ожидает ответа от клиента'
RAISE NOTICE '[RatingBotInit.State] Start add wait rating bot state';
INSERT INTO "CRPM_CFG"."RequestStates"
("Title", "SchemeId", "IsDefault", "IsTerminating", "MaxDurationId", "RowVersion", "Code", "DefaultTransitionOnSuccessId", "DefaultTransitionOnErrorId")
SELECT 'Оценочный бот ожидает ответа от клиента', 1, false, false, ratingBotWaitStateMaxDurationsId, 638670393877795091, 'RATINGBOT.WAIT', NULL, NULL
WHERE NOT EXISTS(SELECT "Id" FROM "CRPM_CFG"."RequestStates" WHERE "Code" = 'RATINGBOT.WAIT')
RETURNING "Id" INTO ratingBotWaitStateId;
RAISE NOTICE '[RatingBotInit.State] End created wait rating bot state with Id: %', ratingBotWaitStateId;
-------------------------------------------------------------------------------------------

-- Добавление статусов для этапа 'Обработка оценочным ботом'
-- Добавление статуса 'Обработка оценочным ботом' с кодом RATINGBOT.PROCESSING
RAISE NOTICE '[RatingBotInit.Status] Start add process rating bot status';
INSERT INTO "CRPM_CFG"."RequestStateStatuses"
("Title", "StateId", "RowVersion", "Code")
SELECT 'Обработка оценочным ботом', ratingBotStateId, 638670386918812808, 'RATINGBOT.PROCESSING'
WHERE NOT EXISTS(SELECT "Id" FROM "CRPM_CFG"."RequestStateStatuses" WHERE "Code" = 'RATINGBOT.PROCESSING')
RETURNING "Id" INTO ratingBotProcessStatusId;
RAISE NOTICE '[RatingBotInit.Status] End added process rating bot status with Id: %', ratingBotProcessStatusId;

-- Добавление статусов для этапа 'Оценочный бот ожидает ответа от клиента'
-- Добавление статуса 'Оценочный бот ожидает ответа от клиента' с кодом RATINGBOT.PROCESSING
RAISE NOTICE '[RatingBotInit.Status] Start add wait rating bot status';
INSERT INTO "CRPM_CFG"."RequestStateStatuses"
("Title", "StateId", "RowVersion", "Code")
SELECT 'Оценочный бот ожидает ответа от клиента', ratingBotWaitStateId, 638670394159898511, 'RATINGBOT.WAIT'
WHERE NOT EXISTS(SELECT "Id" FROM "CRPM_CFG"."RequestStateStatuses" WHERE "Code" = 'RATINGBOT.WAIT')
RETURNING "Id" INTO ratingBotWaitStatusId;
RAISE NOTICE '[RatingBotInit.Status] End added wait rating bot status with Id: %', ratingBotWaitStatusId;
-------------------------------------------------------------------------------------------

-- Конфигурирование этапа 'Обработка оценочным ботом'
RAISE NOTICE '[RatingBotInit.Status] Start configure RatingBot state';

IF ratingBotStateId IS NULL OR ratingBotStateId = 0 THEN
    SELECT "Id" INTO ratingBotStateId FROM "CRPM_CFG"."RequestStates" WHERE "Code" = 'RATINGBOT';
	RAISE NOTICE '[RatingBotInit.Status] ratingBotStateId already exist. Id: %', ratingBotStateId;
END IF;

IF ratingBotWaitStateId IS NULL OR ratingBotWaitStateId = 0 THEN
    SELECT "Id" INTO ratingBotWaitStateId FROM "CRPM_CFG"."RequestStates" WHERE "Code" = 'RATINGBOT.WAIT';
	RAISE NOTICE '[RatingBotInit.Status] ratingBotWaitStateId already exist. Id: %', ratingBotWaitStateId;
END IF;

IF ratingBotProcessStatusId IS NULL OR ratingBotProcessStatusId = 0 THEN
    SELECT "Id" INTO ratingBotProcessStatusId FROM "CRPM_CFG"."RequestStateStatuses" WHERE "Code" = 'RATINGBOT.PROCESSING';
	RAISE NOTICE '[RatingBotInit.Status] ratingBotProcessStatusId already exist. Id: %', ratingBotProcessStatusId;
END IF;

IF ratingBotWaitStatusId IS NULL OR ratingBotWaitStatusId = 0 THEN
    SELECT "Id" INTO ratingBotWaitStatusId FROM "CRPM_CFG"."RequestStateStatuses" WHERE "Code" = 'RATINGBOT.WAIT';
	RAISE NOTICE '[RatingBotInit.Status] ratingBotWaitStatusId already exist. Id: %', ratingBotWaitStatusId;
END IF;

IF ratingBotWorkerId IS NULL OR ratingBotWorkerId = 0 THEN
    SELECT "Id" INTO ratingBotWorkerId FROM "CRPM_CFG"."WorkExecutors" WHERE "TypeCode" = 'MM.ChatBotWorker' AND "Title" = 'Обработка оценочным ботом';
	RAISE NOTICE '[RatingBotInit.Status] ratingBotWorkerId already exist. Id: %', ratingBotWorkerId;
END IF;

-- Настройка переходов для этапа 'Обработка оценочным ботом'
RAISE NOTICE '[RatingBotInit.Status] Start configure transition states for RatingBot state';
INSERT INTO "CRPM_CFG"."AvailableTargetStates" ("StateId", "TargetStatusId", "RowVersion", "StatusId") SELECT ratingBotStateId, (SELECT "Id" FROM "CRPM_CFG"."RequestStateStatuses" WHERE "Code"='CRUSH'), 638676146138407551, ratingBotProcessStatusId
WHERE NOT EXISTS (SELECT "Id" FROM "CRPM_CFG"."AvailableTargetStates" WHERE "StateId" = ratingBotStateId AND "TargetStatusId" = (SELECT "Id" FROM "CRPM_CFG"."RequestStateStatuses" WHERE "Code"='CRUSH') AND "StatusId" = ratingBotProcessStatusId);

INSERT INTO "CRPM_CFG"."AvailableTargetStates" ("StateId", "TargetStatusId", "RowVersion", "StatusId") SELECT ratingBotStateId, (SELECT "Id" FROM "CRPM_CFG"."RequestStateStatuses" WHERE "Title"='Закрыто / Решено'), 638670407601455314, ratingBotProcessStatusId
WHERE NOT EXISTS (SELECT "Id" FROM "CRPM_CFG"."AvailableTargetStates" WHERE "StateId" = ratingBotStateId AND "TargetStatusId" = (SELECT "Id" FROM "CRPM_CFG"."RequestStateStatuses" WHERE "Title"='Закрыто / Решено') AND "StatusId" = ratingBotProcessStatusId);
RAISE NOTICE '[RatingBotInit.Status] End configure transition states for RatingBot state';

-- Добалвение работы для этапа 'Обработка оценочным ботом'
RAISE NOTICE '[RatingBotInit.StateWork] Start add work for RatingBot state';
INSERT INTO "CRPM_CFG"."RequestStateWorks"
("Title", "RequestStateId", "Order", "ProvideExecutorToRequest", "WorkExecutorId", "StatusOnProposedId", "StatusOnActiveId", "StatusOnPausedId", "StatusOnPostponedId", "StatusOnErrorRetryId", "ForceStatusChangeOnFailed", "ForceStatusChangeOnSucceeded", "StatusOnSucceededId", "StatusOnFailedId", "StatusOnRevokedId", "RowVersion", "GenerateEvents", "Enabled")
SELECT 'Обработка оценочным ботом', ratingBotStateId, 0, false, ratingBotWorkerId, ratingBotProcessStatusId, ratingBotProcessStatusId, NULL, NULL, NULL, NULL, NULL, NULL, (SELECT "Id" FROM "CRPM_CFG"."RequestStateStatuses" WHERE "Code"='CRUSH'), NULL, 638676147048998137, false, true
WHERE NOT EXISTS(SELECT "Id" FROM "CRPM_CFG"."RequestStateWorks" WHERE "RequestStateId" = ratingBotStateId AND "WorkExecutorId" = ratingBotWorkerId)
RETURNING "Id" INTO ratingBotStateWorkId;
RAISE NOTICE '[RatingBotInit.StateWork] End added work for RatingBot state with Id: %', ratingBotStateWorkId;

IF ratingBotStateWorkId IS NULL OR ratingBotStateWorkId = 0 THEN
    SELECT "Id" INTO ratingBotStateWorkId FROM "CRPM_CFG"."RequestStateWorks" WHERE "RequestStateId" = ratingBotStateId AND "WorkExecutorId" = ratingBotWorkerId;
END IF;

-- Настройка кодов результатов и кодов ошибок для работы на этапе 'Обработка оценочным ботом'
-- Коды результатов
RAISE NOTICE '[RatingBotInit.StateWork] Start configure work results for RatingBot state with Id: %', ratingBotStateWorkId;
INSERT INTO "CRPM_CFG"."WorkSuccessCodes" ("WorkId", "ForceStateChange", "Code", "TargetStatusId", "RowVersion")
SELECT ratingBotStateWorkId, NULL, 'AWPWORK.CLOSED_WITH_ANSWER', (SELECT "Id" FROM "CRPM_CFG"."RequestStateStatuses" WHERE "Title"='Закрыто / Решено'), 638670940853600455
WHERE NOT EXISTS(SELECT "Id" FROM "CRPM_CFG"."WorkSuccessCodes" WHERE "WorkId" = ratingBotStateWorkId AND "Code" = 'AWPWORK.CLOSED_WITH_ANSWER');
RAISE NOTICE '[RatingBotInit.StateWork] WorkSuccessCode AWPWORK.CLOSED_WITH_ANSWER added';

INSERT INTO "CRPM_CFG"."WorkSuccessCodes" ("WorkId", "ForceStateChange", "Code", "TargetStatusId", "RowVersion")
SELECT ratingBotStateWorkId, NULL, 'CHATBOT.CRASH', (SELECT "Id" FROM "CRPM_CFG"."RequestStateStatuses" WHERE "Code"='CLOSED.CRASH'), 638676144632164624
WHERE NOT EXISTS(SELECT "Id" FROM "CRPM_CFG"."WorkSuccessCodes" WHERE "WorkId" = ratingBotStateWorkId AND "Code" = 'CHATBOT.CRASH');
RAISE NOTICE '[RatingBotInit.StateWork] WorkSuccessCode CHATBOT.CRASH added';

INSERT INTO "CRPM_CFG"."WorkSuccessCodes" ("WorkId", "ForceStateChange", "Code", "TargetStatusId", "RowVersion")
SELECT ratingBotStateWorkId, NULL, 'CHATBOT.WAIT', ratingBotWaitStatusId, 638670394692514073
WHERE NOT EXISTS(SELECT "Id" FROM "CRPM_CFG"."WorkSuccessCodes" WHERE "WorkId" = ratingBotStateWorkId AND "Code" = 'CHATBOT.WAIT');
RAISE NOTICE '[RatingBotInit.StateWork] WorkSuccessCode CHATBOT.WAIT added';

-- Коды ошибок
INSERT INTO "CRPM_CFG"."WorkErrorCodes" ("RetryCount", "RetryDelay", "WorkId", "ForceStateChange", "Code", "TargetStatusId", "RowVersion")
SELECT 0, NULL, ratingBotStateWorkId, NULL, 'CHATBOT.ERROR.GENERIC', (SELECT "Id" FROM "CRPM_CFG"."RequestStateStatuses" WHERE "Code"='CLOSED.CRASH'), 638676145765596169
WHERE NOT EXISTS(SELECT "Id" FROM "CRPM_CFG"."WorkErrorCodes" WHERE "WorkId" = ratingBotStateWorkId AND "Code" = 'CHATBOT.ERROR.GENERIC');
RAISE NOTICE '[RatingBotInit.StateWork] WorkErrorCode CHATBOT.ERROR.GENERIC added';

INSERT INTO "CRPM_CFG"."WorkErrorCodes" ("RetryCount", "RetryDelay", "WorkId", "ForceStateChange", "Code", "TargetStatusId", "RowVersion")
SELECT 3, 2, ratingBotStateWorkId, NULL, 'CHATBOT.ERROR.INFRASTRUCTURE', (SELECT "Id" FROM "CRPM_CFG"."RequestStateStatuses" WHERE "Code"='CLOSED.CRASH'), 638676145765596169
WHERE NOT EXISTS(SELECT "Id" FROM "CRPM_CFG"."WorkErrorCodes" WHERE "WorkId" = ratingBotStateWorkId AND "Code" = 'CHATBOT.ERROR.INFRASTRUCTURE');
RAISE NOTICE '[RatingBotInit.StateWork] WorkErrorCode CHATBOT.ERROR.INFRASTRUCTURE added';
RAISE NOTICE '[RatingBotInit.StateWork] End configure work results for RatingBot state with Id: %', ratingBotStateWorkId;
-------------------------------------------------------------------------------------------

-- Конфигурирование этапа 'Оценочный бот ожидает ответа от клиента'
RAISE NOTICE '[RatingBotInit.Status] Start configure wait RatingBot state';

-- Настройки переходов для этапа 'Оценочный бот ожидает ответа от клиента'
RAISE NOTICE '[RatingBotInit.Status] Start configure transition states for wait RatingBot state';
INSERT INTO "CRPM_CFG"."AvailableTargetStates" ("StateId", "TargetStatusId", "RowVersion", "StatusId") SELECT ratingBotWaitStateId, (SELECT "Id" FROM "CRPM_CFG"."RequestStateStatuses" WHERE "Title"='Закрыто / Без оценки'), 638676150462535391, ratingBotWaitStatusId
WHERE NOT EXISTS(SELECT "Id" FROM "CRPM_CFG"."AvailableTargetStates" WHERE "StateId" = ratingBotWaitStateId AND "TargetStatusId" = (SELECT "Id" FROM "CRPM_CFG"."RequestStateStatuses" WHERE "Title"='Закрыто / Без оценки') AND "StatusId" = ratingBotWaitStatusId);

INSERT INTO "CRPM_CFG"."AvailableTargetStates" ("StateId", "TargetStatusId", "RowVersion", "StatusId") SELECT ratingBotWaitStateId, (SELECT "Id" FROM "CRPM_CFG"."RequestStateStatuses" WHERE "Title"='Закрыто / Решено'), 638676150342297593, ratingBotWaitStatusId
WHERE NOT EXISTS(SELECT "Id" FROM "CRPM_CFG"."AvailableTargetStates" WHERE "StateId" = ratingBotWaitStateId AND "TargetStatusId" = (SELECT "Id" FROM "CRPM_CFG"."RequestStateStatuses" WHERE "Title"='Закрыто / Решено') AND "StatusId" = ratingBotWaitStatusId);

INSERT INTO "CRPM_CFG"."AvailableTargetStates" ("StateId", "TargetStatusId", "RowVersion", "StatusId") SELECT ratingBotWaitStateId, (SELECT "Id" FROM "CRPM_CFG"."RequestStateStatuses" WHERE "Title"='Без ответа' AND "StateId"=(SELECT "Id" FROM "CRPM_CFG"."RequestStates" WHERE "Code" = 'CLOSED' AND "SchemeId" = 1)), 638676150181447663, ratingBotWaitStatusId
WHERE NOT EXISTS(SELECT "Id" FROM "CRPM_CFG"."AvailableTargetStates" WHERE "StateId" = ratingBotWaitStateId AND "TargetStatusId" = (SELECT "Id" FROM "CRPM_CFG"."RequestStateStatuses" WHERE "Title"='Без ответа' AND "StateId"=(SELECT "Id" FROM "CRPM_CFG"."RequestStates" WHERE "Code" = 'CLOSED' AND "SchemeId" = 1)) AND "StatusId" = ratingBotWaitStatusId);

INSERT INTO "CRPM_CFG"."AvailableTargetStates" ("StateId", "TargetStatusId", "RowVersion", "StatusId") SELECT ratingBotWaitStateId, (SELECT "Id" FROM "CRPM_CFG"."RequestStateStatuses" WHERE "Code"='CRUSH'), 638676150342297593, ratingBotWaitStatusId
WHERE NOT EXISTS(SELECT "Id" FROM "CRPM_CFG"."AvailableTargetStates" WHERE "StateId" = ratingBotWaitStateId AND "TargetStatusId" = (SELECT "Id" FROM "CRPM_CFG"."RequestStateStatuses" WHERE "Code"='CRUSH') AND "StatusId" = ratingBotWaitStatusId);

INSERT INTO "CRPM_CFG"."AvailableTargetStates" ("StateId", "TargetStatusId", "RowVersion", "StatusId") SELECT ratingBotWaitStateId, (SELECT "Id" FROM "CRPM_CFG"."RequestStateStatuses" WHERE "Code"='OPERATOR.WAIT'), 638670448585862125, ratingBotWaitStatusId
WHERE NOT EXISTS(SELECT "Id" FROM "CRPM_CFG"."AvailableTargetStates" WHERE "StateId" = ratingBotWaitStateId AND "TargetStatusId" = (SELECT "Id" FROM "CRPM_CFG"."RequestStateStatuses" WHERE "Code"='OPERATOR.WAIT') AND "StatusId" = ratingBotWaitStatusId);
RAISE NOTICE '[RatingBotInit.Status] End configure transition states for wait RatingBot state';

RAISE NOTICE '[RatingBotInit.Status] End configure wait RatingBot state';
-------------------------------------------------------------------------------------------

-- Изменение настройки кодов результатов для работы на этапе "Обработка оператором"
RAISE NOTICE '[RatingBotInit] Start update WorkSuccessCodes for OperatorWork state';
UPDATE "CRPM_CFG"."WorkSuccessCodes"
SET "TargetStatusId"=ratingBotProcessStatusId
WHERE
  "WorkId"=(SELECT "Id" FROM "CRPM_CFG"."RequestStateWorks" WHERE "RequestStateId"=(SELECT "Id" FROM "CRPM_CFG"."RequestStates" WHERE "Code"='OPERATOR.PROCESSING' AND "SchemeId"=1))
  AND "Code"='AWPWORK.CLOSED_WITH_ANSWER';
RAISE NOTICE '[RatingBotInit] End update WorkSuccessCodes for OperatorWork state';
-------------------------------------------------------------------------------------------

-- Добавление обработки кода результата 'CHATBOT.CRASH' в чат-боте для работа на этапе 'Обработка чат-ботом'
RAISE NOTICE '[RatingBotInit] Start add WorkSuccessCodes CHATBOT.CRASH handling for ChatBotWork state';

INSERT INTO "CRPM_CFG"."WorkSuccessCodes" ("WorkId", "ForceStateChange", "Code", "TargetStatusId", "RowVersion")
SELECT
  (SELECT "Id" FROM "CRPM_CFG"."RequestStateWorks" WHERE "RequestStateId"=(SELECT "Id" FROM "CRPM_CFG"."RequestStates" WHERE "Code"='CHATBOT' AND "SchemeId"=1)), -- Работа 'Обработка чат-ботом'
  NULL,
  'CHATBOT.CRASH',
  (SELECT "Id" FROM "CRPM_CFG"."RequestStateStatuses" WHERE "Code" = 'SEND.PVOO'),
  638676196478185621;

UPDATE "CRPM_CFG"."RequestStates"
SET
  "DefaultTransitionOnSuccessId"=(SELECT "Id" FROM "CRPM_CFG"."RequestStateStatuses" WHERE "Code" = 'SEND.PVOO'),
  "DefaultTransitionOnErrorId"=(SELECT "Id" FROM "CRPM_CFG"."RequestStateStatuses" WHERE "Code" = 'SEND.PVOO')
WHERE "Code"='CHATBOT' AND "SchemeId"=1;

RAISE NOTICE '[RatingBotInit] End add WorkSuccessCodes CHATBOT.CRASH handling for ChatBotWork state';
-------------------------------------------------------------------------------------------

-- Добавление обработчиков событий для оценочного бота
RAISE NOTICE '[RatingBotInit.Events] Start add RatingBot event handlers';

RAISE NOTICE '[RatingBotInit.Events] Start add RatingBot event handler for WorkStatusChanged. TypeCode: SaveCloseResultCodeEventHandler';
INSERT INTO "CRPM_CFG"."RequestEventHandlers"
("RowVersion", "Title", "Configuration", "TypeCode", "IsEnabled", "EventType", "StreamId", "SchemeId", "StateId", "StatusId", "BranchId", "CampaignId", "Channel", "QueueId")
SELECT 638676185683784973, '[RatingBot] Простановка CompleteResultCode', '<?xml version="1.0" encoding="utf-16"?>
<SaveCloseResultCodeEventHandlerConfig />', 'SaveCloseResultCodeEventHandler', true, 'WorkStatusChanged', 1, 1, (SELECT "Id" FROM "CRPM_CFG"."RequestStates" WHERE "Code"='OPERATOR.PROCESSING' AND "SchemeId"=1), NULL, NULL, NULL, NULL, NULL
WHERE NOT EXISTS(SELECT "Id" FROM "CRPM_CFG"."RequestEventHandlers" WHERE "TypeCode" = 'SaveCloseResultCodeEventHandler' AND "EventType" = 'WorkStatusChanged' AND "Title" ~ E'\\[RatingBot\\]');
RAISE NOTICE '[RatingBotInit.Events] End added RatingBot event handler for WorkStatusChanged. TypeCode: SaveCloseResultCodeEventHandler';

RAISE NOTICE '[RatingBotInit.Events] Start add RatingBot event handler for NewIncomingMessageRegistered. TypeCode: StateTransitionEventHandler';
INSERT INTO "CRPM_CFG"."RequestEventHandlers"
("RowVersion", "Title", "Configuration", "TypeCode", "IsEnabled", "EventType", "StreamId", "SchemeId", "StateId", "StatusId", "BranchId", "CampaignId", "Channel", "QueueId")
SELECT 638670807472844861, '[RatingBot] Оценочный бот ожидает ответа от клиента', '<?xml version="1.0" encoding="utf-16"?>
<Config>
  <TransitionsConfigurations>
    <TransitionConfiguration FromStatusCode="">
      <To StateCode="RATINGBOT" StatusCode="RATINGBOT.PROCESSING" />
    </TransitionConfiguration>
  </TransitionsConfigurations>
</Config>', 'StateTransitionEventHandler', true, 'NewIncomingMessageRegistered', NULL, NULL, ratingBotWaitStateId, NULL, NULL, NULL, NULL, NULL
WHERE NOT EXISTS(SELECT "Id" FROM "CRPM_CFG"."RequestEventHandlers" WHERE "TypeCode" = 'StateTransitionEventHandler' AND "EventType" = 'NewIncomingMessageRegistered' AND "Title" ~ E'\\[RatingBot\\]');
RAISE NOTICE '[RatingBotInit.Events] End added RatingBot event handler for NewIncomingMessageRegistered. TypeCode: StateTransitionEventHandler';

RAISE NOTICE '[RatingBotInit.Events] End add RatingBot event handlers';
-------------------------------------------------------------------------------------------

-- Удаление обработчиков связанных с запросом оценки и отправке уведомлений о получении оценки
RAISE NOTICE '[RatingBotInit.Events] Start delete handlers for rating request and notification';

DELETE FROM "CRPM_CFG"."RequestEventHandlers"
WHERE "Id"=(SELECT "Id" FROM "CRPM_CFG"."RequestEventHandlers" WHERE "EventType" = 'RequestClosed' AND "TypeCode"='InfoMessageEventHandler' AND "StateId" = (SELECT "Id" FROM "CRPM_CFG"."RequestStates" WHERE "Title"='Закрыто (Решено)' AND "SchemeId"=1))
RETURNING "Id" INTO tempId;
RAISE NOTICE '[RatingBotInit.Events] Event handler with EventType = RequestClosed and TypeCode = InfoMessageEventHandler deleted. Deleted id: %', tempId;

DELETE FROM "CRPM_CFG"."RequestEventHandlers"
WHERE "Id"=(SELECT "Id" FROM "CRPM_CFG"."RequestEventHandlers" WHERE "EventType" = 'NewScoreEvent' AND "TypeCode"='InfoMessageEventHandler' AND "SchemeId" = 1)
RETURNING "Id" INTO tempId;
RAISE NOTICE '[RatingBotInit.Events] Event handler with EventType = RequestClosed and TypeCode = InfoMessageEventHandler deleted. Deleted id: %', tempId;
-------------------------------------------------------------------------------------------

-- Отключение кастомов относящийся к обработчику автооценки и уведомлений о получении оценки
RAISE NOTICE '[RatingBotInit.Queue] Start delete custom attributes for rating request and notification';

DELETE FROM "CRPM_CFG"."CustomAttributes"
WHERE "Key" IN (
	'Queue.InfoAutoReplyWorker.ShouldSendScoreReply',
	'Queue.InfoAutoReplyWorker.ShouldSendScoreReply.Templates',
	'Queue.InfoAutoReplyWorker.ShouldSendScoreReceivedReply',
	'Queue.InfoAutoReplyWorker.ShouldSendScoreReceivedReply.Templates');

RAISE NOTICE '[RatingBotInit.Queue] End delete custom attributes for rating request and notification';
-------------------------------------------------------------------------------------------

-- Удаление настроек из приложения "настройки" связанных с запросом оценки и отправке уведомлений о получении оценки
RAISE NOTICE '[RatingBotInit] Start delete settings for rating request and notification';
UPDATE "AWP_INFRA"."AppInit_Control"
SET "ControlInitialization" = regexp_replace(
    "ControlInitialization",
    E'^\\s*<Attribute\\s+Name="(Отправка оценки|Отправка . получении оценки)"[^/>]*/>\\r?\\n',
    '',
    'gm'
)
WHERE "Control_Assembly" = 'Product.ClientComponents.HostedControls.Settings' AND "ControlInitialization" ~ E'<Attribute\\s+Name="(Отправка оценки|Отправка . получении оценки)"[^/>]*/>';
RAISE NOTICE '[RatingBotInit] End delete settings for rating request and notification';
-------------------------------------------------------------------------------------------

-- Добавление базового сценария для оценочного бота

IF NOT EXISTS( SELECT "Id" FROM "SCRIPTS"."Scripts" WHERE "Code" = 'BASE_RATING') THEN
	RAISE NOTICE '[RatingBotInit.Script] Start add base script for rating bot';

	INSERT INTO "SCRIPTS"."Scripts"
	(
	    "Name", "Description", "Code", "ActivateFrom", "ActivateTo", 
	    "CanBeAutomated", "Status", "LastStatusChangedBy", 
	    "TimeLastStatusChanged", "LastUpdateBy", "TimeLastUpdate", 
	    "CreatedBy", "TimeCreated", "OwnerSystemId"
	)
	SELECT 
	    'Обработка оценки', 
	    'Представляет пример базового сценария для оценочного бота', 
	    'BASE_RATING', 
	    NOW() AT TIME ZONE 'UTC', 
	    NULL, 
	    true, 
	    1, 
	    'System', 
	    NOW() AT TIME ZONE 'UTC', 
	    'System', 
	    NOW() AT TIME ZONE 'UTC', 
	    'System', 
	    NOW() AT TIME ZONE 'UTC', 
	    (SELECT "Id" FROM "SCRIPTS"."OwnerSystems" WHERE "Code"='rating')
	WHERE NOT EXISTS(
	    SELECT "Id" 
	    FROM "SCRIPTS"."Scripts" 
	    WHERE "Code" = 'BASE_RATING'
	)
	RETURNING "Id" INTO ratingBotBaseScriptId;
	RAISE NOTICE '[RatingBotInit.Script] End added base script with Id: %', ratingBotBaseScriptId;

	-- Добавление скрипта в версии
	RAISE NOTICE '[RatingBotInit.Script] Start add base script version';
	INSERT INTO "SCRIPTS"."ScriptVersions"
	("ScriptId", "Version", "Note", "CreatedBy", "TimeCreated", "Priority", "StartingStepId")
	SELECT ratingBotBaseScriptId, 0, NULL, 'System', NOW() AT TIME ZONE 'UTC', 1, ratingBotBaseScriptInitStepId
	RETURNING "Id" INTO ratingBotBaseScriptVersionId;
	RAISE NOTICE '[RatingBotInit.Script] End added base script version with Id: %', ratingBotBaseScriptVersionId;

	-- Добавление шагов в скрипт
	INSERT INTO "SCRIPTS"."ScriptSteps"
	("ScriptVersionsId", "Name", "Description", "Url", "AnswersType", "Code", "VariableId", "Discriminator", "NextStepId", "ScriptId", "AutomationServiceId", "NextFailStepId", "IsSkippable", "RequestAction", "RequestThemeCode", "IsBackButtonAvailable")
	SELECT ratingBotBaseScriptVersionId, 'Шаг оценки', 'Уважаемый, клиент!

	Ваше обращение решено. Пожалуйста, оцените качество обработки обращения.', NULL, NULL, 'step-2024-11-25T12:49:48.096Z', NULL, 'RatingStep', NULL, NULL, NULL, NULL, false, NULL, NULL, false
	RETURNING "Id" INTO ratingBotBaseScriptInitStepId;
	RAISE NOTICE '[RatingBotInit.Script] Added init step (RatingStep) to base script. Id: %', ratingBotBaseScriptInitStepId;

	INSERT INTO "SCRIPTS"."ScriptSteps"
	("ScriptVersionsId", "Name", "Description", "Url", "AnswersType", "Code", "VariableId", "Discriminator", "NextStepId", "ScriptId", "AutomationServiceId", "NextFailStepId", "IsSkippable", "RequestAction", "RequestThemeCode", "IsBackButtonAvailable")
	SELECT ratingBotBaseScriptVersionId, 'Уведомление об оценке', 'Оценка принята. Спасибо за отзыв!', NULL, 0, 'step-2024-11-25T12:50:05.330Z', NULL, 'ScriptStep', NULL, NULL, NULL, NULL, false, NULL, NULL, false
	RETURNING "Id" INTO ratingBotBaseScriptFinishStepId;
	RAISE NOTICE '[RatingBotInit.Script] Added final step (ScriptStep) to base script. Id: %', ratingBotBaseScriptFinishStepId;

	-- Обновляем StartingStepId, т.к. циклическая зависимость между ScriptVersions и ScriptSteps
	UPDATE "SCRIPTS"."ScriptVersions"
	SET "StartingStepId" = ratingBotBaseScriptInitStepId
	WHERE "Id" = ratingBotBaseScriptVersionId;

	-- Добавление в шагов в таблицу дизайна
	RAISE NOTICE '[RatingBotInit.Script] Start add steps to designer';
	INSERT INTO "SCRIPTS"."StepDesignerProps" ("StepId", "CoordX", "CoordY") VALUES(ratingBotBaseScriptInitStepId, 333, 262);
	INSERT INTO "SCRIPTS"."StepDesignerProps" ("StepId", "CoordX", "CoordY") VALUES(ratingBotBaseScriptFinishStepId, 636, 264);
	RAISE NOTICE '[RatingBotInit.Script] End add steps to designer';

	-- Добавление ответов на начальный шаг
	RAISE NOTICE '[RatingBotInit.Script] Start add answers to init step';
	INSERT INTO "SCRIPTS"."StepAnswers" ("ScriptStepId", "Value", "ValueCode", "NextStepId", "Order") VALUES(ratingBotBaseScriptInitStepId, '1', NULL, ratingBotBaseScriptFinishStepId, 4);
	INSERT INTO "SCRIPTS"."StepAnswers" ("ScriptStepId", "Value", "ValueCode", "NextStepId", "Order") VALUES(ratingBotBaseScriptInitStepId, '2', NULL, ratingBotBaseScriptFinishStepId, 3);
	INSERT INTO "SCRIPTS"."StepAnswers" ("ScriptStepId", "Value", "ValueCode", "NextStepId", "Order") VALUES(ratingBotBaseScriptInitStepId, '3', NULL, ratingBotBaseScriptFinishStepId, 2);
	INSERT INTO "SCRIPTS"."StepAnswers" ("ScriptStepId", "Value", "ValueCode", "NextStepId", "Order") VALUES(ratingBotBaseScriptInitStepId, '4', NULL, ratingBotBaseScriptFinishStepId, 1);
	INSERT INTO "SCRIPTS"."StepAnswers" ("ScriptStepId", "Value", "ValueCode", "NextStepId", "Order") VALUES(ratingBotBaseScriptInitStepId, '5', NULL, ratingBotBaseScriptFinishStepId, 0);
	RAISE NOTICE '[RatingBotInit.Script] End add answers to init step';
ELSE
	RAISE NOTICE '[RatingBotInit.Script] Base script for rating bot already exist';
	SELECT "Id" INTO ratingBotBaseScriptId FROM "SCRIPTS"."Scripts" WHERE "Code" = 'BASE_RATING';
END IF;
-------------------------------------------------------------------------------------------

-- Установка кастомов очереди для очерени по умолчанию
RAISE NOTICE '[RatingBotInit.Queue] Start add rating queue custom attributes for default queue';

INSERT INTO "CRPM_CFG"."CustomAttributes" ("Id", "Key", "Value", "CampaignId", "QueueId", "RowVersion")
SELECT uuid_generate_v4(), 'Queue.RatingBotId', ratingBotBaseScriptId, NULL, 1, 638670836692036989
WHERE NOT EXISTS(SELECT "Id" FROM "CRPM_CFG"."CustomAttributes" WHERE "Key" = 'Queue.RatingBotId' AND "QueueId" = 1);

INSERT INTO "CRPM_CFG"."CustomAttributes" ("Id", "Key", "Value", "CampaignId", "QueueId", "RowVersion")
SELECT uuid_generate_v4(), 'Queue.RatingBotSystemName', 'RatingBot', NULL, 1, 638670836692036989
WHERE NOT EXISTS(SELECT "Id" FROM "CRPM_CFG"."CustomAttributes" WHERE "Key" = 'Queue.RatingBotSystemName' AND "QueueId" = 1);

RAISE NOTICE '[RatingBotInit.Queue] End add rating queue custom attributes for default queue';
-------------------------------------------------------------------------------------------

-- Простановка флага "генерировать события" в работе на этапе "Обработка оператором"
RAISE NOTICE '[RatingBotInit.OperatorWork] Start enable generate events flag for OperatorWork state';
UPDATE "CRPM_CFG"."RequestStateWorks"
SET "GenerateEvents"=true
WHERE
  "RequestStateId"= (SELECT "Id" FROM "CRPM_CFG"."RequestStates" WHERE "Code"='OPERATOR.PROCESSING' AND "SchemeId"=1)
  AND "WorkExecutorId" = (SELECT "Id" FROM "CRPM_CFG"."WorkExecutors" WHERE "TypeCode" = 'Multimedia.AwpWorker');
RAISE NOTICE '[RatingBotInit.OperatorWork] End enable generate events flag for OperatorWork state';
-------------------------------------------------------------------------------------------

END $RATINGBOTINIT$;
