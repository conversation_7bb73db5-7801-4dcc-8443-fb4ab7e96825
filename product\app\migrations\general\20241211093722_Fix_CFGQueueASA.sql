DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "CALC"."__CalcContextMigrations" WHERE "MigrationId" = '20241211093722_Fix_CFGQueueASA') THEN
                    -- View: CALC.CFG_Queue_ASA

    -- DROP VIEW "CALC"."CFG_Queue_ASA";
    CREATE OR REPLACE VIEW "CALC"."CFG_Queue_ASA"
    AS
    SELECT
        "Id",
        "Title",
        "Description",
        (
            SELECT MAX(NULLIF(TRIM(a."Value"), '')::double precision)
            FROM "CRPM_CFG"."CustomAttributes" a
            WHERE a."QueueId" = q."Id"
              AND a."Key" = 'Queue.ThresholdValues.AlarmASA'::text
        ) AS "AlarmASA",
        (
            SELECT MAX(NULLIF(TRIM(a."Value"), '')::double precision)
            FROM "CRPM_CFG"."CustomAttributes" a
            WHERE a."QueueId" = q."Id"
              AND a."Key" = 'Queue.ThresholdValues.WarningASA'::text
        ) AS "WarningASA",
        (
            SELECT MAX(NULLIF(TRIM(a."Value"), '')::double precision)
            FROM "CRPM_CFG"."CustomAttributes" a
            WHERE a."QueueId" = q."Id"
              AND a."Key" = 'Queue.ThresholdValues.AlarmAHT'::text
        ) AS "AlarmAHT",
        (
            SELECT MAX(NULLIF(TRIM(a."Value"), '')::double precision)
            FROM "CRPM_CFG"."CustomAttributes" a
            WHERE a."QueueId" = q."Id"
              AND a."Key" = 'Queue.ThresholdValues.WarningAHT'::text
        ) AS "WarningAHT",
        (
            SELECT MAX(NULLIF(TRIM(a."Value"), '')::double precision)
            FROM "CRPM_CFG"."CustomAttributes" a
            WHERE a."QueueId" = q."Id"
              AND a."Key" = 'Queue.ThresholdValues.AlarmACSI'::text
        ) AS "AlarmACSI",
        (
            SELECT MAX(NULLIF(TRIM(a."Value"), '')::double precision)
            FROM "CRPM_CFG"."CustomAttributes" a
            WHERE a."QueueId" = q."Id"
              AND a."Key" = 'Queue.ThresholdValues.WarningACSI'::text
        ) AS "WarningACSI",
        (
            SELECT MAX(NULLIF(TRIM(a."Value"), '')::double precision)
            FROM "CRPM_CFG"."CustomAttributes" a
            WHERE a."QueueId" = q."Id"
              AND a."Key" = 'Queue.ThresholdValues.AlarmClientResponseTimeout'::text
        ) AS "AlarmART",
        (
            SELECT MAX(NULLIF(TRIM(a."Value"), '')::double precision)
            FROM "CRPM_CFG"."CustomAttributes" a
            WHERE a."QueueId" = q."Id"
              AND a."Key" = 'Queue.ThresholdValues.WarningClientResponseTimeout'::text
        ) AS "WarningART",
        (
            SELECT MAX(NULLIF(TRIM(a."Value"), '')::double precision)
            FROM "CRPM_CFG"."CustomAttributes" a
            WHERE a."QueueId" = q."Id"
              AND a."Key" = 'Queue.ThresholdValues.MaxQueueLength'::text
        ) AS "MaxQueueLength",
        (
            SELECT MAX(NULLIF(TRIM(a."Value"), '')::double precision)
            FROM "CRPM_CFG"."CustomAttributes" a
            WHERE a."QueueId" = q."Id"
              AND a."Key" = 'Queue.ThresholdValues.AcwTime'::text
        ) AS "AlarmACW",
        (
            SELECT MAX(NULLIF(TRIM(a."Value"), '')::double precision)
            FROM "CRPM_CFG"."CustomAttributes" a
            WHERE a."QueueId" = q."Id"
              AND a."Key" = 'Queue.ThresholdValues.WarningAcwTime'::text
        ) AS "WarningACW",
        (
            SELECT MAX(NULLIF(TRIM(a."Value"), '')::double precision)
            FROM "CRPM_CFG"."CustomAttributes" a
            WHERE a."QueueId" = q."Id"
              AND a."Key" = 'Queue.ThresholdValues.AlarmQWT'::text
        ) AS "AlarmQWT",
        (
            SELECT MAX(NULLIF(TRIM(a."Value"), '')::double precision)
            FROM "CRPM_CFG"."CustomAttributes" a
            WHERE a."QueueId" = q."Id"
              AND a."Key" = 'Queue.ThresholdValues.WarningQWT'::text
        ) AS "WarningQWT",
        (
            SELECT MAX(NULLIF(TRIM(a."Value"), '')::double precision)
            FROM "CRPM_CFG"."CustomAttributes" a
            WHERE a."QueueId" = q."Id"
              AND a."Key" = 'Queue.ThresholdValues.AlarmCP'::text
        ) AS "AlarmTST",
        (
            SELECT MAX(NULLIF(TRIM(a."Value"), '')::double precision)
            FROM "CRPM_CFG"."CustomAttributes" a
            WHERE a."QueueId" = q."Id"
              AND a."Key" = 'Queue.ThresholdValues.WarningCP'::text
        ) AS "WarningTST",
        (
            SELECT MAX(NULLIF(TRIM(a."Value"), '')::double precision)
            FROM "CRPM_CFG"."CustomAttributes" a
            WHERE a."QueueId" = q."Id"
              AND a."Key" = 'Queue.ThresholdValues.AlarmSessionInactivityTimer'::text
        ) AS "AlarmSIT",
        (
            SELECT MAX(NULLIF(TRIM(a."Value"), '')::double precision)
            FROM "CRPM_CFG"."CustomAttributes" a
            WHERE a."QueueId" = q."Id"
              AND a."Key" = 'Queue.ThresholdValues.WarningSessionInactivityTimer'::text
        ) AS "WarningSIT"
    FROM
        "CRPM_CFG"."RequestQueues" q
    WHERE
        "IsDeleted" = false;

    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "CALC"."__CalcContextMigrations" WHERE "MigrationId" = '20241211093722_Fix_CFGQueueASA') THEN
    INSERT INTO "CALC"."__CalcContextMigrations" ("MigrationId", "ProductVersion")
    VALUES ('20241211093722_Fix_CFGQueueASA', '8.0.2');
    END IF;
END $EF$;
