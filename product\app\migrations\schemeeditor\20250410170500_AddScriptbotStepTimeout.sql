DO $SCRIPTBOTSTEPTIMOUTINIT$

DECLARE botWorkerId smallint; -- Id обработчика Обработка таймаута кнопочным ботом
DECLARE botWorkerAutomationWorkId smallint; -- Id обработчика Обработка таймаута кнопочным ботом
DECLARE workExecutionManagerId smallint; -- Id Обработка таймаута кнопочным ботом в списке менеджера автоматических работ
DECLARE botWaitStateId smallint; -- Id этапа Сценарный бот ожидает ответа от клиента
DECLARE botStateWorkId smallint; -- Id работы на этапе
DECLARE scriptBotStatusId smallint; -- Id статуса этапа Обработка сценарным ботом SCRIPTBOT.PROCESSING

BEGIN

SELECT "Id" FROM "CRPM_CFG"."WorkExecutionManagers" into workExecutionManagerId WHERE "TypeCode" = 'ServiceWorkExecutionManager' LIMIT 1;
SELECT "Id" FROM "CRPM_CFG"."RequestStateStatuses" into scriptBotStatusId WHERE "Code" = 'SCRIPTBOT.PROCESSING' LIMIT 1;
SELECT "Id" FROM "CRPM_CFG"."RequestStates" into botWaitStateId WHERE "Code" = 'SCRIPTBOT.WAIT' LIMIT 1;

-- Добавление обработчика
RAISE NOTICE '[ScriptbotStepTimeout.Worker] Start add worker';
SELECT "Id" INTO botWorkerId FROM "CRPM_CFG"."WorkExecutors" WHERE "TypeCode" = 'MM.ChatBotDelayWorker' AND "Title" = 'Обработка таймаута кнопочным ботом' LIMIT 1;

IF botWorkerId IS NULL THEN
	INSERT INTO "CRPM_CFG"."WorkExecutors"
	("RequestStreamId", "ChannelId", "TypeCode", "IsEnabled", "Title", "Configuration", "RowVersion")
	SELECT
	1,
	NULL,
	'MM.ChatBotDelayWorker',
	true,
	'Обработка таймаута кнопочным ботом', format('<?xml version="1.0" encoding="utf-16"?>
<ChatBotDelayWorkerConfiguration>
<TimeoutStatusId>%s</TimeoutStatusId>
</ChatBotDelayWorkerConfiguration>', scriptBotStatusId),
	638791026900972952
	RETURNING "Id" INTO botWorkerId;
END IF;

RAISE NOTICE '[ScriptbotStepTimeout.Worker] worker has been added with Id: %', botWorkerId;
-------------------------------------------------------------------------------------------

-- Добавление обработчика в менеджеры автоматических работ
RAISE NOTICE '[ScriptbotStepTimeout.WorkExCfg] Start add worker to automatic workers';

SELECT "Id" FROM "CRPM_CFG"."WorkExecutionConfigurations" into botWorkerAutomationWorkId WHERE "WorkExecutionManagerId" = workExecutionManagerId AND "WorkExecutorId" = botWorkerId  LIMIT 1;

IF botWorkerAutomationWorkId IS NULL THEN
	INSERT INTO "CRPM_CFG"."WorkExecutionConfigurations" ("WorkExecutionManagerId", "WorkExecutorId", "Count", "RowVersion")
	SELECT workExecutionManagerId, botWorkerId, 100, 638791027536486727
	RETURNING "Id" INTO botWorkerAutomationWorkId;
END IF;

RAISE NOTICE '[ScriptbotStepTimeout.WorkExCfg] worker has been added into automatic workers with id: %', botWorkerAutomationWorkId;
-------------------------------------------------------------------------------------------

-- Добавление работы для этапа 'Сценарный бот ожидает ответа от клиента'
RAISE NOTICE '[ScriptbotStepTimeout.StateWork] Start add work for ScriptbotStepTimeout state';

SELECT "Id" FROM "CRPM_CFG"."RequestStateWorks" into botStateWorkId WHERE "RequestStateId" = botWaitStateId AND "WorkExecutorId" = botWorkerId  LIMIT 1;

IF botStateWorkId IS NULL THEN
	INSERT INTO "CRPM_CFG"."RequestStateWorks"
	("Title", "RequestStateId", "Order", "ProvideExecutorToRequest", "WorkExecutorId", "StatusOnProposedId", "StatusOnActiveId", "StatusOnPausedId", "StatusOnPostponedId", "StatusOnErrorRetryId", "ForceStatusChangeOnFailed", "ForceStatusChangeOnSucceeded", "StatusOnSucceededId", "StatusOnFailedId", "StatusOnRevokedId", "RowVersion", "GenerateEvents", "Enabled")
	SELECT
	'Обработка таймаута кнопочным ботом',  --Title
	botWaitStateId, --RequestStateId
	0, --Order
	false, --ProvideExecutorToRequest
	botWorkerId, --WorkExecutorId
	NULL, --StatusOnProposedId
	NULL, --StatusOnActiveId
	NULL, --StatusOnPausedId
	NULL, --StatusOnPostponedId
	NULL, --StatusOnErrorRetryId
	NULL, --ForceStatusChangeOnFailed
	NULL, --ForceStatusChangeOnSucceeded
	NULL, --StatusOnSucceededId
	NULL, --StatusOnFailedId
	NULL, --StatusOnRevokedId
	638791268784264031, --RowVersion
	false, --GenerateEvents
	true--Enabled
	WHERE NOT EXISTS(SELECT "Id" FROM "CRPM_CFG"."RequestStateWorks" WHERE "RequestStateId" = botWaitStateId AND "WorkExecutorId" = botWorkerId)
	RETURNING "Id" INTO botStateWorkId;
END IF;
RAISE NOTICE '[ScriptbotStepTimeout.StateWork] Work has been added for ScriptbotStepTimeout state with Id: %', botStateWorkId;
-------------------------------------------------------------------------------------------

END $SCRIPTBOTSTEPTIMOUTINIT$;