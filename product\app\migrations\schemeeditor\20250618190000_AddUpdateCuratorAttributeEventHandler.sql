INSERT INTO "CRPM_CFG"."RequestEventHandlers" ("RowVersion", "Title", "Configuration", "TypeCode", "IsEnabled", "EventType", "StreamId", "SchemeId", "StateId", "StatusId")
SELECT 638858480966790039, 'Запись куратора для оператора, закрывшего  обращение',
'<?xml version="1.0" encoding="utf-16"?>
<UpdateCuratorAttributeEventHandlerConfig />',
'UpdateCuratorAttributeEventHandler', false, 'RequestClosed', 1, NULL, NULL, NULL
WHERE NOT EXISTS(
        SELECT "Id" FROM "CRPM_CFG"."RequestEventHandlers" WHERE "TypeCode"='UpdateCuratorAttributeEventHandler' AND "EventType"='RequestClosed'
    );

DO $do$
DECLARE
    current_config XML;
    new_index INT;
BEGIN
    -- Шаг 1: Получаем текущее значение "Configuration"
    SELECT "Configuration"::xml INTO current_config
    FROM "CRPM_CFG"."Settings"
    WHERE "Title" = 'CustomAttributesMapForRequests';

    -- Шаг 2: Проверяем, существует ли уже тег с Code="CuratorId"
    IF NOT (xpath_exists('//Mapping[@Code="CuratorId"]', current_config)) THEN
        -- Шаг 3: Находим максимальное значение Index для тегов с Type="String"
        SELECT COALESCE(MAX(value::int), 0) + 1 INTO new_index
        FROM (
            SELECT unnest(xpath('//Mapping[@Type="Guid"]/@Index', current_config))::text AS value
        ) subquery;

        -- Шаг 3: Добавляем новый тег перед <!--Long-->
        current_config := regexp_replace(
            current_config::text,
            '<!--Long-->',
            '<Mapping Code="CuratorId" Type="Guid" Index="' || new_index || '" />' || E'\n    <!--Long-->'
        )::xml;

        -- Шаг 4: Обновляем запись в таблице
        UPDATE "CRPM_CFG"."Settings"
        SET "Configuration" = current_config
        WHERE "Title" = 'CustomAttributesMapForRequests';
    END IF;
END $do$;
