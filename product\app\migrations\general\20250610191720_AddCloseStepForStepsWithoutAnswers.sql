-- Добавляет шаги с продолжением закрытия обращения для шагов без вариантов ответа
DO $EF$
DECLARE
step_rec RECORD;
    new_step_id INTEGER;
BEGIN
FOR step_rec IN
SELECT
    scriptStep."Id" AS original_step_id,
    scriptStep."ScriptVersionsId",
    script."Name" AS script_name
FROM "SCRIPTS"."ScriptSteps" scriptStep
         JOIN "SCRIPTS"."Scripts" script ON script."Id" = scriptStep."ScriptVersionsId"
WHERE script."OwnerSystemId" = 3 AND scriptStep."AnswersType" = 0 AND scriptStep."NextStepId" IS NULL
GROUP BY script."Name", scriptStep."ScriptVersionsId", scriptStep."Id"
    LOOP
INSERT INTO "SCRIPTS"."ScriptSteps" (
    "ScriptVersionsId", "Name", "Description", "Url", "AnswersType",
    "Code", "VariableId", "Discriminator", "NextStepId", "ScriptId",
    "AutomationServiceId", "NextFailStepId", "IsSkippable", "RequestAction", "RequestThemeCode",
    "IsBackButtonAvailable", "Limit", "LimitStepId", "Timeout", "TimeoutStepId")
VALUES(step_rec."ScriptVersionsId", 'Продолжить закрытие обращения', '', NULL, NULL, NOW(),  NULL, 'ProductActionStep', NULL, NULL, NULL, NULL, false, 'continue', NULL, false, NULL, NULL, NULL, NULL)
    RETURNING "Id" INTO new_step_id;

UPDATE "SCRIPTS"."ScriptSteps" SET "NextStepId" = new_step_id WHERE "Id" = step_rec."original_step_id";

RAISE NOTICE 'Added new step with ID: %, for ScriptVersionsId: %', new_step_id, step_rec."ScriptVersionsId";
END LOOP;
END $EF$;