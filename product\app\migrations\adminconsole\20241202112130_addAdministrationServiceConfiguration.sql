INSERT INTO "AWP_INFRA"."Configurations" ("Id", "Name", "Value", "Description", "LastChangeTime")
SELECT uuid_generate_v4()::uuid, 'AdministrationServiceConfiguration', '{
  "RoutingAttributes": [
    {
      "Code": "Channel",
      "DisplayName": "Канал",
      "IsCustom": false,
      "Field": "ValueString",
      "DataType": "String",
      "RightPartType": "List",
      "RuleType": "Request"
    },
    {
      "Code": "Originator",
      "DisplayName": "Отправитель",
      "IsCustom": false,
      "Field": "ValueString",
      "DataType": "String",
      "RightPartType": "String",
      "RuleType": "Request"
    },
    {
      "Code": "Source",
      "DisplayName": "Место контакта",
      "IsCustom": false,
      "Field": "ValueString",
      "DataType": "String",
      "RightPartType": "String",
      "RuleType": "Request"
    }
  ],
  "DistributionRules": [
    {
      "Name": "Случайный порядок",
      "Description": "Случайный порядок исполнителей",
      "Type": "CRP.ExecutorShuffler",
      "IsDefault": true
    },
    {
      "Name": "Наиболее простаивающий",
      "Description": "Распределение на наиболее простаивающего",
      "Type": "Product.MostIdleSelector"
    },
    {
      "Name": "Наименее занятый",
      "Description": "Распределение на наименее занятого",
      "Type": "Product.LeastBusySelector"
    },
    {
      "Name": "Наименьшее кол-во обращений в работе",
      "Description": "Распределение на оператора с наименьшим количеством обращений в работе",
      "Type": "Product.LeastRequestsInWorkSelector"
    }
  ],
  "AutoHandlers": [
    {
      "Name": "Бот не понял сообщение клиента",
      "IsEnabledValueAttributeCode": "Queue.BotDontUnderstandClientMessage",
      "AutoReplyTemplatesAttributeCode": "Queue.BotDontUnderstandClientMessage.Templates",
      "WorkerType": "default"
    },
    {
      "Name": "Взятие в работу",
      "IsEnabledValueAttributeCode": "Queue.RequestInWorkAutoReplyWorker.ShouldSendAutoReply",
      "AutoReplyTemplatesAttributeCode": "Queue.RequestInWorkAutoReplyWorker.ShouldSendAutoReply.Templates",
      "WorkerType": "default"
    },
    {
      "Name": "Возврат из отложенных",
      "IsEnabledValueAttributeCode": "Queue.PostponeReturned",
      "AutoReplyTemplatesAttributeCode": "Queue.PostponeReturned.Templates",
      "WorkerType": "default"
    },
    {
      "Name": "Информационное сообщение",
      "IsEnabledValueAttributeCode": "Queue.InfoAutoReplyWorker.ShouldSendAutoReply",
      "AutoReplyTemplatesAttributeCode": "Queue.InfoAutoReplyWorker.ShouldSendAutoReply.Templates",
      "WorkerType": "default"
    },
    {
      "Name": "Отправка сообщения при не ответе клиента",
      "IsEnabledValueAttributeCode": "Queue.InfoAutoReplyWorker.ShouldSendWithoutAnswerAutoReply",
      "AutoReplyTemplatesAttributeCode": "Queue.InfoAutoReplyWorker.ShouldSendWithoutAnswerAutoReply.Templates",
      "WorkerType": "default"
    },
    {
      "Name": "Перевод в другую очередь",
      "IsEnabledValueAttributeCode": "Queue.RedirectToQueue.ShouldSendAutoReply",
      "AutoReplyTemplatesAttributeCode": "Queue.RedirectToQueue.ShouldSendAutoReply.Templates",
      "WorkerType": "default"
    },
    {
      "Name": "Перевод на оператора",
      "IsEnabledValueAttributeCode": "Queue.RedirectToOperator.ShouldSendAutoReply",
      "AutoReplyTemplatesAttributeCode": "Queue.RedirectToOperator.ShouldSendAutoReply.Templates",
      "WorkerType": "default"
    },
    {
      "Name": "Перевод на супервизора",
      "IsEnabledValueAttributeCode": "Queue.RedirectToSupervisor.ShouldSendAutoReply",
      "AutoReplyTemplatesAttributeCode": "Queue.RedirectToSupervisor.ShouldSendAutoReply.Templates",
      "WorkerType": "default"
    },
    {
      "Name": "Превышение SLA (распределение)",
      "IsEnabledValueAttributeCode": "Queue.EscalatedAutoReplyWorker.ShouldSendAutoReply",
      "AutoReplyTemplatesAttributeCode": "Queue.EscalatedAutoReplyWorker.ShouldSendAutoReply.Templates",
      "WorkerType": "default"
    },
    {
      "Name": "Превышение SLA (решение)",
      "IsEnabledValueAttributeCode": "Queue.EscalatedDecisionAutoReplyWorker.ShouldSendAutoReply",
      "AutoReplyTemplatesAttributeCode": "Queue.EscalatedDecisionAutoReplyWorker.ShouldSendAutoReply.Templates",
      "WorkerType": "default"
    },
    {
      "Name": "Приветствие",
      "IsEnabledValueAttributeCode": "Queue.GreetingAutoReplyWorker.ShouldSendAutoReply",
      "AutoReplyTemplatesAttributeCode": "Queue.GreetingAutoReplyWorker.ShouldSendAutoReply.Templates",
      "WorkerType": "default"
    },
    {
      "Name": "Прогнозное время ожидания ответа",
      "IsEnabledValueAttributeCode": "Queue.SendWaitingTimeWorker.ShouldSendAutoReply",
      "AutoReplyTemplatesAttributeCode": "Queue.SendWaitingTimeWorker.ShouldSendAutoReply.Templates",
      "WorkerType": "Pvoo"
    },
    {
      "Name": "Пустой текст",
      "IsEnabledValueAttributeCode": "Queue.EmptyTextAutoReplyWorker.ShouldSendAutoReply",
      "AutoReplyTemplatesAttributeCode": "Queue.EmptyTextAutoReplyWorker.ShouldSendAutoReply.Templates",
      "WorkerType": "default"
    },
    {
      "Name": "Спам",
      "IsEnabledValueAttributeCode": "Queue.SpamAutoReplyWorker.ShouldSendAutoReply",
      "AutoReplyTemplatesAttributeCode": "Queue.SpamAutoReplyWorker.ShouldSendAutoReply.Templates",
      "WorkerType": "default"
    },
    {
      "Name": "Черный список",
      "IsEnabledValueAttributeCode": "Queue.BlackListAutoReplyWorker.ShouldSendAutoReply",
      "AutoReplyTemplatesAttributeCode": "Queue.BlackListAutoReplyWorker.ShouldSendAutoReply.Templates",
      "WorkerType": "default"
    }
  ],
  "ExternalChatBotSettings": [
    {
      "DisplayName": "Отключен",
      "IsDefault": true
    }
  ],
  "ThresholdValues": [
    {
      "DisplayName": "Average Handle Time",
      "IsInteger": true,
      "WarningCustomAttributeName": "Queue.ThresholdValues.WarningAHT",
      "AlarmCustomAttributeName": "Queue.ThresholdValues.AlarmAHT",
      "MeasurementCustomAttributeName": "Queue.ThresholdValues.MeasureAHT",
      "WarningMinValue": 0,
      "WarningMaxValue": 0,
      "WarningDefaultValue": 180,
      "AlarmMinValue": 0,
      "AlarmMaxValue": 0,
      "AlarmDefaultValue": 250,
      "IsAlarmMustMoreWarning": true
    },
    {
      "DisplayName": "Average Customer Satisfaction Index (ACSI)",
      "IsInteger": false,
      "WarningCustomAttributeName": "Queue.ThresholdValues.WarningACSI",
      "AlarmCustomAttributeName": "Queue.ThresholdValues.AlarmACSI",
      "MeasurementCustomAttributeName": "Queue.ThresholdValues.MeasureACSI",
      "WarningMinValue": 1,
      "WarningMaxValue": 5,
      "WarningDefaultValue": 4.9,
      "AlarmMinValue": 1,
      "AlarmMaxValue": 5.0,
      "AlarmDefaultValue": 4.8,
      "IsAlarmMustMoreWarning": true
    },
    {
      "DisplayName": "Average Speed of Answer (ASA)",
      "IsInteger": true,
      "WarningCustomAttributeName": "Queue.ThresholdValues.WarningASA",
      "AlarmCustomAttributeName": "Queue.ThresholdValues.AlarmASA",
      "MeasurementCustomAttributeName": "Queue.ThresholdValues.MeasureASA",
      "DestinationAttributeName": "Queue.TransferCallDestination",
      "WarningMinValue": 0,
      "WarningMaxValue": null,
      "WarningDefaultValue": 30,
      "AlarmMinValue": 0,
      "AlarmMaxValue": null,
      "AlarmDefaultValue": 60,
      "IsAlarmMustMoreWarning": true
    },
    {
      "DisplayName": "Average Response Time (ART)",
      "IsInteger": true,
      "WarningCustomAttributeName": "Queue.ThresholdValues.WarningClientResponseTimeout",
      "AlarmCustomAttributeName": "Queue.ThresholdValues.AlarmClientResponseTimeout",
      "MeasurementCustomAttributeName": "Queue.ThresholdValues.MeasureART",
      "WarningMinValue": 0,
      "WarningMaxValue": null,
      "WarningDefaultValue": 30,
      "AlarmMinValue": 0,
      "AlarmMaxValue": null,
      "AlarmDefaultValue": 90,
      "IsAlarmMustMoreWarning": true
    },
    {
      "DisplayName": "Target Solution Time (TST)",
      "IsInteger": true,
      "WarningCustomAttributeName": "Queue.ThresholdValues.WarningCP",
      "AlarmCustomAttributeName": "Queue.ThresholdValues.AlarmCP",
      "MeasurementCustomAttributeName": "Queue.ThresholdValues.MeasureCP",
      "WarningMinValue": null,
      "WarningMaxValue": null,
      "WarningDefaultValue": 60,
      "AlarmMinValue": null,
      "AlarmMaxValue": null,
      "AlarmDefaultValue": 120,
      "IsAlarmMustMoreWarning": true
    },
    {
      "DisplayName": "After Call Work (ACW)",
      "IsInteger": true,
      "AlarmCustomAttributeName": "Queue.ThresholdValues.AcwTime",
      "MeasurementCustomAttributeName": "Queue.ThresholdValues.MeasureACW",
      "WarningMinValue": null,
      "WarningMaxValue": null,
      "WarningDefaultValue": null,
      "AlarmMinValue": null,
      "AlarmMaxValue": null,
      "AlarmDefaultValue": 60,
      "IsAlarmMustMoreWarning": false
    },
    {
      "DisplayName": "Максимальная длина очереди",
      "IsInteger": true,
      "AlarmCustomAttributeName": "Queue.ThresholdValues.MaxQueueLength",
      "WarningMinValue": null,
      "WarningMaxValue": null,
      "WarningDefaultValue": null,
      "AlarmMinValue": 1,
      "AlarmMaxValue": null,
      "AlarmDefaultValue": 30,
      "IsAlarmMustMoreWarning": false
    }
  ]
}', 'Конфигурация WEB АРМ Бизнес Администратора', NOW()
    WHERE
NOT EXISTS (
  SELECT "Id" FROM "AWP_INFRA"."Configurations"
  WHERE "Name"='AdministrationServiceConfiguration'
);