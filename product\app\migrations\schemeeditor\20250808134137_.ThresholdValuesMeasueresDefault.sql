DO $MIGRATION$
BEGIN

RAISE NOTICE 'Start migration ThresholdValuesMeasueresDefault';

MERGE INTO "CRPM_CFG"."CustomAttributes" AS ca
USING (
    VALUES
      ('Queue.ThresholdValues.MeasureACW', 'Sec', 638785793065423616::bigint),
      ('Queue.ThresholdValues.MeasureAHT', 'Sec', 638785793064890922::bigint),
      ('Queue.ThresholdValues.MeasureASA', 'Sec', 638785793065075973::bigint),
      ('Queue.ThresholdValues.MeasureART', 'Sec', 638785793065205145::bigint),
      ('Queue.ThresholdValues.MeasureCP',  'Sec', 638785793065321324::bigint),
      ('Queue.ThresholdValues.MeasureAlarmSessionInactivityTimer', 'Sec', 638785793064747268::bigint)
) AS s("Key","Value","RowVersion")
ON ca."QueueId" = 1 AND ca."Key" = s."Key"
WHEN MATCHED AND (ca."Value" IS NULL OR ca."Value" ILIKE 'none') THEN
  UPDATE SET
    "Value" = s."Value",
    "RowVersion" = COALESCE(ca."RowVersion", s."RowVersion")
WHEN NOT MATCHED THEN
  INSERT ("Id","Key","Value","CampaignId","QueueId","RowVersion")
  VALUES (
    gen_random_uuid(),
    s."Key",
    s."Value",
    NULL,
    1, -- Очередь по умолчаию всего имеет id = 1
    s."RowVersion"
  );

RAISE NOTICE 'End migration ThresholdValuesMeasueresDefault';

END $MIGRATION$;
