DO $MIG$
BEGIN

    RAISE NOTICE '[20250806010445_AVELANA-1682] Start replace \r\n to \n in TEXT_TEMPLATES.TEXT_TEMPLATE_CONTENTS';
    UPDATE "TEXT_TEMPLATES"."TEXT_TEMPLATE_CONTENTS"
    SET "VALUE" = REPLACE("VALUE", E'\r\n', E'\n')
    WHERE "VALUE" LIKE E'%\r\n%';

    RAISE NOTICE '[20250806010445_AVELANA-1682] Start replace \r\n to \n in TEXT_TEMPLATES.TEXT_TEMPLATE_CONTENTS.PLAIN_VALUE';
    UPDATE "TEXT_TEMPLATES"."TEXT_TEMPLATE_CONTENTS"
    SET "PLAIN_VALUE" = REPLACE("PLAIN_VALUE", E'\r\n', E'\n')
    WHERE "PLAIN_VALUE" LIKE E'%\r\n%';

    RAISE NOTICE '[20250806010445_AVELANA-1682] Finish';

END $MIG$;
