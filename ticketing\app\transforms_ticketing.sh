#!/bin/bash

echo "executing ticketing transforms file..."

. /.config.sh || exit 1
. /.config-ticketing.sh || exit 1

if [ -z ${LBDNS} ]; then
  APPDNS=${HOSTNAMELOCAL}.${FULLDOMAIN}
else
  APPDNS=${LBDNS}
fi

cd /app
inject_variables_sed=$(mktemp inject_variables_sed.XXXXXXXXXX)
configs_folder=./.
transforms=(
"s#{transform:DbServer.Ticketing}#${DBSERVER}#g"
"s#{transform:DbUser.Ticketing}#${DBUSER}#g"
"s#{transform:DbPassword.Ticketing}#${DBPASSWORD}#g"
"s#{transform:DbName.Ticketing}#${DBNAMETICKECTING}#g"
"s#{transform:kcUrl}#${KCDNS}#g"
"s#{transform:kcRealm}#${KCREALM}#g"
"s#{transform:AppHostname}#${APPDNS}#g"
"s#{transform:Ticketing.Hooks.Email.FromAddress}#${TICKETINGEMAILADDRESS}#g"
"s#{transform:Ticketing.Hooks.Email.Password}#${TICKETINGEMAILPASSWORD}#g"
)
for transform in ${transforms[@]}; 
    do
        echo "$transform" >> $inject_variables_sed
    done
find $configs_folder \( -type f -name '*.json' -o -name '*.sh' -o -name '*.config' -o -name '*.sql' -o -name '*.conf' -o -name '*.service' \) -exec sed -i -f $inject_variables_sed {} \;
rm -rf $inject_variables_sed
