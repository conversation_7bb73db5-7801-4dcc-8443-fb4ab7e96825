#!/bin/bash

#includes
. /.config.sh || exit 1
. /.config-ticketing.sh || exit 1

shopt -s nocasematch
echo -e "[$(date '+%d.%m.%Y %T.%3N')] Restoring databases" && tput sgr0

#Export database password
export PGPASSWORD=${DBPASSWORD}

#Database creation
echo -e "[$(date '+%d.%m.%Y %T.%3N')] Creating database ${DBNAMETICKETING} by user ${DBUSER}..." && tput sgr0
psql -h ${DBSERVER} -p ${DBSERVERPORT} -U ${DBUSER} -d postgres -c "CREATE DATABASE \"${DBNAMETICKETING}\";" || exit 1
echo -e "[$(date '+%d.%m.%Y %T.%3N')] Creating database ${DBNAMETICKETING} complete" && tput sgr0

echo -e "[$(date '+%d.%m.%Y %T.%3N')] Restoring database ${DBNAMETICKETING}..." && tput sgr0
psql -h ${DBSERVER} -p ${DBSERVERPORT} -U ${DBUSER} -d ${DBNAMETICKETING} -f /backups/ticketing.sql || exit 1
echo -e "[$(date '+%d.%m.%Y %T.%3N')] Restoring database ${DBNAMETICKETING} complete" && tput sgr0

echo -e "[$(date '+%d.%m.%Y %T.%3N')] Applying databases changes" && tput sgr0

#Apply scripts
cd /app/db_scripts

echo -e "[$(date '+%d.%m.%Y %T.%3N')] Apply sql-script EnableTicketingForWebArm..." && tput sgr0
psql -h ${DBSERVER} -p ${DBSERVERPORT} -U ${DBUSER} -d "${DBNAMEPRODUCT}" -f EnableTicketingForWebArm.sql || exit 1
echo -e "[$(date '+%d.%m.%Y %T.%3N')] Apply sql-script EnableTicketingForWebArm complete" && tput sgr0

echo -e "[$(date '+%d.%m.%Y %T.%3N')] Apply sql-script AddGridColumns..." && tput sgr0
psql -h ${DBSERVER} -p ${DBSERVERPORT} -U ${DBUSER} -d "${DBNAMEPRODUCT}" -f AddGridColumns.sql || exit 1
echo -e "[$(date '+%d.%m.%Y %T.%3N')] Apply sql-script AddGridColumns complete" && tput sgr0

echo -e "[$(date '+%d.%m.%Y %T.%3N')] Apply sql-script ticketing_email_mapping..." && tput sgr0
psql -h ${DBSERVER} -p ${DBSERVERPORT} -U ${DBUSER} -d "${DBNAMEPRODUCT}" -f ticketing_email_mapping.sql || exit 1
echo -e "[$(date '+%d.%m.%Y %T.%3N')] Apply sql-script ticketing_email_mapping complete" && tput sgr0

echo -e "[$(date '+%d.%m.%Y %T.%3N')] Apply sql-script Transform_TicketingValues..." && tput sgr0
psql -h ${DBSERVER} -p ${DBSERVERPORT} -U ${DBUSER} -d "${DBNAMETICKETING}" -f Transform_TicketingValues.sql || exit 1
echo -e "[$(date '+%d.%m.%Y %T.%3N')] Apply sql-script Transform_TicketingValues complete" && tput sgr0

echo -e "[$(date '+%d.%m.%Y %T.%3N')] Applying database changes completed" && tput sgr0
echo -e " Инициализация базы данных успешно выполнена" && tput sgr0
exit 1