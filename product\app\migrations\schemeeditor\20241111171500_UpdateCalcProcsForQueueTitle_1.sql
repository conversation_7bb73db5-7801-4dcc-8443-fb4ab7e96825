CREATE OR REPLACE PROCEDURE "CALC"."P_INSERT_SINGLE_BULK_REQUESTSVIEW"(
	IN req_id bigint)
LANGUAGE 'plpgsql'
AS $BODY$

         DECLARE
                BEGIN 
        	        BEGIN
        		        INSERT INTO "CALC"."BULK_REQUESTSVIEW"
        		        (
        			        SELECT 
        				        r."Id",
        				        r."ExternalId",
        				        r."TimeCreated",
        				        r."TimeRegistered",
        				        null as "TimeLastStateChanged",
        				        coalesce(iof."FIO",cast('Система' as varchar(200))) AS "StatusModifiedBy",
        				        r."TimeClosed",
        				        r."Title",
        				        NULL AS "Description",
        				        r."BranchId",
        				        r."Priority",
        				        r."State",
        				        r."Status",
        				        ass."AssignedTo" AS "AssignedToId",
        				        r."ExecutorId",
        				        r."SchemaId",
        				        r."Source",
        				        r."Channel",
        				        COALESCE(r."QueueId", -1) AS "QueueId",
        				        (
        					        SELECT 
        						        rq."Title"
        					        FROM "CRPM_CFG"."RequestQueues" rq
        					        WHERE 
        						        rq."Id" = r."QueueId"
        					        LIMIT 1
        				        )  AS "QueueTitle",
        				        NULL AS "PlainText",
        				        r."Originator",
        				        r."ParentId" as "ParentId",
        				        COALESCE(rss."Title", rs."Title") AS "StatusName",
        				        sa."Name" AS "ServiceAreaName",
        				        sa."UtcOffset",
        				        CASE
        					        WHEN r."State" = 4 THEN 0
        					        ELSE
        					        (
        						        EXTRACT(day from r."TimeRegistered" - now())*24*60*60 +
        						        EXTRACT(hour from r."TimeRegistered" - now())*60*60 +
        						        EXTRACT(minute from r."TimeRegistered" - now())*60
        					        )
        				        END AS "LifeTime",
        				        ass.assto_name AS "AssignedToName",
        				        exec_.exec_name AS "ExecutorName",
        				        (
        					        SELECT COUNT(1)
        					        FROM 
        						        "UCMM_DATA"."MESSAGE_EXTERNAL_SYSTEM_LINK" esl
        					        JOIN "UCMM_DATA"."ATTACHMENTS" a
        					        ON 
        						        esl."MESSAGE_ID" = a."MessageId"
        					        WHERE 
        						        a."Is_INLINED" = FALSE
        						        AND 
        						        esl."EXTERNAL_ENTITY_ID" = r."Id"
        				        ) AS "TotalAttachments",
        				        (
        					        SELECT 
        						        COUNT(1)
        					        FROM "UCMM_DATA"."MESSAGE_EXTERNAL_SYSTEM_LINK" esl
        					        JOIN "UCMM_DATA"."MESSAGE" m
        					        ON 
        						        esl."MESSAGE_ID" = m."ID"
        					        WHERE 
        						        m."DIRECTION" = 1
        						        AND
        							        m."TypeCode" != 'Note'
        						        AND 
        						        esl."EXTERNAL_ENTITY_ID" = r."Id"
        					        ) AS "TotalIncomingMessages",
        					        (
        						        SELECT 
        							        COUNT(1)
        						        FROM "UCMM_DATA"."MESSAGE_EXTERNAL_SYSTEM_LINK" esl
        						        JOIN "UCMM_DATA"."MESSAGE" m
        						        ON 
        							        esl."MESSAGE_ID" = m."ID"
        						        WHERE 
        							        m."DIRECTION" = 2
        						        AND
        							        m."TypeCode" != 'Note'
        						        AND 
        							        esl."EXTERNAL_ENTITY_ID" = r."Id"
        					        ) AS "TotalOutgoingMessages",
        					        h."TimeOperatorLastDistribution"  AS "AssignedToLastTime",
        					        COALESCE(ca_str."Value15", '') AS "ForwardNote",
        					        COALESCE(ca_str."Value12", '') AS "CustomerDatabaseType",
        					        COALESCE(ca_str."Value6", '') AS "MSISDN",
        					        ------------
        					        h."HT",
        					        h."SA",
        					        (ca_bool."Value3"::int) as lost_sms_flag,
        					        ca_str."Value5" as "RegionName",
        					        cp."ContactPersonID",
        					        cp."ContactPersonName",
        					        CASE
        						        WHEN r."TimeClosed" IS NOT NULL THEN NULL
        						        ELSE postpone."PostponeTime"
        					        END AS "PostponeTime",
        					        r."TimeClosed" IS NOT NULL AS "Closed",
        					        COALESCE(
        						        (
        							        SELECT
        								        AVG("ScoreResult")
        							        FROM "CALC"."V_EVALUATIONS"
        							        WHERE
        								        ("Context"->>'requestId')::bigint = r."Id"
        								        AND "PublishedAt" IS NOT NULL
        							        GROUP BY r."Id"
        						        ), ca_long."Value2")::numeric(3,1) AS "SupervisorScore",
        					        ca_str."Value35" as "StringValue35",
        					        ca_str."Value36" as "StringValue36",
        					        ca_str."Value37" as "StringValue37",
        					        ca_str."Value38" as "StringValue38",
        					        ca_str."Value39" as "StringValue39",
        					        ca_str."Value40" as "StringValue40",
        					        ca_str."Value41" as "StringValue41",
        					        ca_str."Value42" as "StringValue42",
        					        ca_str."Value43" as "StringValue43",
        					        ca_str."Value44" as "StringValue44",
        					        ca_str."Value45" as "StringValue45",
        					        ca_str."Value46" as "StringValue46",
        					        ca_str."Value47" as "StringValue47",
        					        ca_str."Value48" as "StringValue48",
        					        ca_str."Value49" as "StringValue49",
        					        ca_str."Value50" as "StringValue50",
        					        ca_datetime."Value30" as "TargetResponseTime",
        					        rlci."STATUS_LAST_CHANGE_DATE_TIME" as "StatusTime",
        					        iof."LOGIN" as "StatusModifiedByLogin",
        					        COALESCE(ca_long."Value3", 0) "RequestType",
        					        case  when ca_datetime."Value7" IS NULL then 0 else 1 end as "RequestOverdue",
        					        r."RepeatedRequestId",
        			                ca_datetime."Value9" as "TargetDecisionTime",
        					        case when ca_datetime."Value8" IS NULL then 0 else 1 end as "RequestDecisionOverdue",
        					        ---------------

        					        -- ticketing
        					        ca_long."Value5" as "TicketsTotalCount",
        					        ca_long."Value6" as "TicketsRequiredProcessingCount",
        					        ca_long."Value7" as "TicketsProcessingRequiredAnswerReceivedCount",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value10" = dv."ID") as "GuidValueCode10",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value11" = dv."ID") as "GuidValueCode11",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value12" = dv."ID") as "GuidValueCode12",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value13" = dv."ID") as "GuidValueCode13",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value14" = dv."ID") as "GuidValueCode14",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value15" = dv."ID") as "GuidValueCode15",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value16" = dv."ID") as "GuidValueCode16",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value17" = dv."ID") as "GuidValueCode17",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value18" = dv."ID") as "GuidValueCode18",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value19" = dv."ID") as "GuidValueCode19",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value20" = dv."ID") as "GuidValueCode20",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value21" = dv."ID") as "GuidValueCode21",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value22" = dv."ID") as "GuidValueCode22",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value23" = dv."ID") as "GuidValueCode23",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value24" = dv."ID") as "GuidValueCode24",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value25" = dv."ID") as "GuidValueCode25",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value26" = dv."ID") as "GuidValueCode26",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value27" = dv."ID") as "GuidValueCode27",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value28" = dv."ID") as "GuidValueCode28",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value29" = dv."ID") as "GuidValueCode29",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value30" = dv."ID") as "GuidValueCode30",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value31" = dv."ID") as "GuidValueCode31",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value32" = dv."ID") as "GuidValueCode32",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value33" = dv."ID") as "GuidValueCode33",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value34" = dv."ID") as "GuidValueCode34",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value35" = dv."ID") as "GuidValueCode35",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value36" = dv."ID") as "GuidValueCode36",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value37" = dv."ID") as "GuidValueCode37",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value38" = dv."ID") as "GuidValueCode38",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value39" = dv."ID") as "GuidValueCode39",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value40" = dv."ID") as "GuidValueCode40",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value41" = dv."ID") as "GuidValueCode41",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value42" = dv."ID") as "GuidValueCode42",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value43" = dv."ID") as "GuidValueCode43",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value44" = dv."ID") as "GuidValueCode44",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value45" = dv."ID") as "GuidValueCode45",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value46" = dv."ID") as "GuidValueCode46",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value47" = dv."ID") as "GuidValueCode47",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value48" = dv."ID") as "GuidValueCode48",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value49" = dv."ID") as "GuidValueCode49",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value50" = dv."ID") as "GuidValueCode50",

        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value10" = dv."ID") as "GuidValueName10",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value11" = dv."ID") as "GuidValueName11",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value12" = dv."ID") as "GuidValueName12",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value13" = dv."ID") as "GuidValueName13",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value14" = dv."ID") as "GuidValueName14",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value15" = dv."ID") as "GuidValueName15",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value16" = dv."ID") as "GuidValueName16",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value17" = dv."ID") as "GuidValueName17",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value18" = dv."ID") as "GuidValueName18",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value19" = dv."ID") as "GuidValueName19",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value20" = dv."ID") as "GuidValueName20",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value21" = dv."ID") as "GuidValueName21",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value22" = dv."ID") as "GuidValueName22",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value23" = dv."ID") as "GuidValueName23",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value24" = dv."ID") as "GuidValueName24",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value25" = dv."ID") as "GuidValueName25",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value26" = dv."ID") as "GuidValueName26",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value27" = dv."ID") as "GuidValueName27",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value28" = dv."ID") as "GuidValueName28",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value29" = dv."ID") as "GuidValueName29",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value30" = dv."ID") as "GuidValueName30",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value31" = dv."ID") as "GuidValueName31",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value32" = dv."ID") as "GuidValueName32",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value33" = dv."ID") as "GuidValueName33",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value34" = dv."ID") as "GuidValueName34",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value35" = dv."ID") as "GuidValueName35",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value36" = dv."ID") as "GuidValueName36",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value37" = dv."ID") as "GuidValueName37",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value38" = dv."ID") as "GuidValueName38",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value39" = dv."ID") as "GuidValueName39",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value40" = dv."ID") as "GuidValueName40",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value41" = dv."ID") as "GuidValueName41",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value42" = dv."ID") as "GuidValueName42",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value43" = dv."ID") as "GuidValueName43",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value44" = dv."ID") as "GuidValueName44",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value45" = dv."ID") as "GuidValueName45",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value46" = dv."ID") as "GuidValueName46",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value47" = dv."ID") as "GuidValueName47",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value48" = dv."ID") as "GuidValueName48",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value49" = dv."ID") as "GuidValueName49",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value50" = dv."ID") as "GuidValueName50",
        					        cp_ca_str."Value2" as "ClientId",
        					        ca_str."Value51" as "StringValue51",
        					        ca_str."Value52" as "StringValue52",
        					        ca_str."Value53" as "StringValue53",
        					        ca_str."Value54" as "StringValue54",
        					        ca_str."Value55" as "StringValue55",
        					        ca_str."Value56" as "StringValue56",
        					        ca_str."Value57" as "StringValue57",
        					        ca_str."Value58" as "StringValue58",
        					        ca_str."Value59" as "StringValue59",
        					        ca_str."Value60" as "StringValue60",
        					        ca_str."Value61" as "StringValue61",
        					        ca_str."Value62" as "StringValue62",
        					        ca_str."Value63" as "StringValue63",
        					        ca_str."Value64" as "StringValue64",
        					        ca_str."Value65" as "StringValue65",
        					        ca_str."Value66" as "StringValue66",
        					        ca_str."Value67" as "StringValue67",
        					        ca_str."Value68" as "StringValue68",
        					        ca_str."Value69" as "StringValue69",
        					        ca_str."Value70" as "StringValue70",
        					        ca_str."Value71" as "StringValue71",
        					        ca_str."Value72" as "StringValue72",
        					        ca_str."Value73" as "StringValue73",
        					        ca_str."Value74" as "StringValue74",
        					        ca_str."Value75" as "StringValue75",
        					        ca_str."Value76" as "StringValue76",
        					        ca_str."Value77" as "StringValue77",
        					        ca_str."Value78" as "StringValue78",
        					        ca_str."Value79" as "StringValue79",
        					        ca_str."Value80" as "StringValue80",
        					        ca_str."Value81" as "StringValue81",
        					        ca_str."Value82" as "StringValue82",
        					        ca_str."Value83" as "StringValue83",
        					        ca_str."Value84" as "StringValue84",
        					        ca_str."Value85" as "StringValue85",
        					        ca_str."Value86" as "StringValue86",
        					        ca_str."Value87" as "StringValue87",
        					        ca_str."Value88" as "StringValue88",
        					        ca_str."Value89" as "StringValue89",
        					        ca_str."Value90" as "StringValue90",
        					        ca_str."Value91" as "StringValue91",
        					        ca_str."Value92" as "StringValue92",
        					        ca_str."Value93" as "StringValue93",
        					        ca_str."Value94" as "StringValue94",
        					        ca_str."Value95" as "StringValue95",
        					        ca_str."Value96" as "StringValue96",
        					        ca_str."Value97" as "StringValue97",
        					        ca_str."Value98" as "StringValue98",
        					        ca_str."Value99" as "StringValue99",
        					        ca_str."Value100" as "StringValue100",

        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value51" = dv."ID") as "GuidValueCode51",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value52" = dv."ID") as "GuidValueCode52",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value53" = dv."ID") as "GuidValueCode53",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value54" = dv."ID") as "GuidValueCode54",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value55" = dv."ID") as "GuidValueCode55",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value56" = dv."ID") as "GuidValueCode56",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value57" = dv."ID") as "GuidValueCode57",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value58" = dv."ID") as "GuidValueCode58",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value59" = dv."ID") as "GuidValueCode59",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value60" = dv."ID") as "GuidValueCode60",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value61" = dv."ID") as "GuidValueCode61",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value62" = dv."ID") as "GuidValueCode62",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value63" = dv."ID") as "GuidValueCode63",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value64" = dv."ID") as "GuidValueCode64",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value65" = dv."ID") as "GuidValueCode65",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value66" = dv."ID") as "GuidValueCode66",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value67" = dv."ID") as "GuidValueCode67",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value68" = dv."ID") as "GuidValueCode68",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value69" = dv."ID") as "GuidValueCode69",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value70" = dv."ID") as "GuidValueCode70",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value71" = dv."ID") as "GuidValueCode71",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value72" = dv."ID") as "GuidValueCode72",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value73" = dv."ID") as "GuidValueCode73",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value74" = dv."ID") as "GuidValueCode74",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value75" = dv."ID") as "GuidValueCode75",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value76" = dv."ID") as "GuidValueCode76",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value77" = dv."ID") as "GuidValueCode77",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value78" = dv."ID") as "GuidValueCode78",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value79" = dv."ID") as "GuidValueCode79",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value80" = dv."ID") as "GuidValueCode80",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value81" = dv."ID") as "GuidValueCode81",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value82" = dv."ID") as "GuidValueCode82",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value83" = dv."ID") as "GuidValueCode83",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value84" = dv."ID") as "GuidValueCode84",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value85" = dv."ID") as "GuidValueCode85",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value86" = dv."ID") as "GuidValueCode86",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value87" = dv."ID") as "GuidValueCode87",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value88" = dv."ID") as "GuidValueCode88",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value89" = dv."ID") as "GuidValueCode89",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value90" = dv."ID") as "GuidValueCode90",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value91" = dv."ID") as "GuidValueCode91",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value92" = dv."ID") as "GuidValueCode92",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value93" = dv."ID") as "GuidValueCode93",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value94" = dv."ID") as "GuidValueCode94",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value95" = dv."ID") as "GuidValueCode95",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value96" = dv."ID") as "GuidValueCode96",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value97" = dv."ID") as "GuidValueCode97",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value98" = dv."ID") as "GuidValueCode98",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value99" = dv."ID") as "GuidValueCode99",
        					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value100" = dv."ID") as "GuidValueCode100",

        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value51" = dv."ID") as "GuidValueName51",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value52" = dv."ID") as "GuidValueName52",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value53" = dv."ID") as "GuidValueName53",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value54" = dv."ID") as "GuidValueName54",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value55" = dv."ID") as "GuidValueName55",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value56" = dv."ID") as "GuidValueName56",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value57" = dv."ID") as "GuidValueName57",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value58" = dv."ID") as "GuidValueName58",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value59" = dv."ID") as "GuidValueName59",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value60" = dv."ID") as "GuidValueName60",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value61" = dv."ID") as "GuidValueName61",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value62" = dv."ID") as "GuidValueName62",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value63" = dv."ID") as "GuidValueName63",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value64" = dv."ID") as "GuidValueName64",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value65" = dv."ID") as "GuidValueName65",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value66" = dv."ID") as "GuidValueName66",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value67" = dv."ID") as "GuidValueName67",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value68" = dv."ID") as "GuidValueName68",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value69" = dv."ID") as "GuidValueName69",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value70" = dv."ID") as "GuidValueName70",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value71" = dv."ID") as "GuidValueName71",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value72" = dv."ID") as "GuidValueName72",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value73" = dv."ID") as "GuidValueName73",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value74" = dv."ID") as "GuidValueName74",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value75" = dv."ID") as "GuidValueName75",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value76" = dv."ID") as "GuidValueName76",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value77" = dv."ID") as "GuidValueName77",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value78" = dv."ID") as "GuidValueName78",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value79" = dv."ID") as "GuidValueName79",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value80" = dv."ID") as "GuidValueName80",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value81" = dv."ID") as "GuidValueName81",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value82" = dv."ID") as "GuidValueName82",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value83" = dv."ID") as "GuidValueName83",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value84" = dv."ID") as "GuidValueName84",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value85" = dv."ID") as "GuidValueName85",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value86" = dv."ID") as "GuidValueName86",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value87" = dv."ID") as "GuidValueName87",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value88" = dv."ID") as "GuidValueName88",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value89" = dv."ID") as "GuidValueName89",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value90" = dv."ID") as "GuidValueName90",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value91" = dv."ID") as "GuidValueName91",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value92" = dv."ID") as "GuidValueName92",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value93" = dv."ID") as "GuidValueName93",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value94" = dv."ID") as "GuidValueName94",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value95" = dv."ID") as "GuidValueName95",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value96" = dv."ID") as "GuidValueName96",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value97" = dv."ID") as "GuidValueName97",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value98" = dv."ID") as "GuidValueName98",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value99" = dv."ID") as "GuidValueName99",
        					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value100" = dv."ID") as "GuidValueName100",

        					        ca_datetime."Value1" as "DateTimeValue1",
        					        ca_datetime."Value2" as "DateTimeValue2",
        					        ca_datetime."Value3" as "DateTimeValue3",
        					        ca_datetime."Value4" as "DateTimeValue4",
        					        ca_datetime."Value5" as "DateTimeValue5",
        					        ca_datetime."Value6" as "DateTimeValue6",
        					        ca_datetime."Value7" as "DateTimeValue7",
        					        ca_datetime."Value8" as "DateTimeValue8",
        					        ca_datetime."Value9" as "DateTimeValue9",
        					        ca_datetime."Value10" as "DateTimeValue10",
        					        ca_datetime."Value11" as "DateTimeValue11",
        					        ca_datetime."Value12" as "DateTimeValue12",
        					        ca_datetime."Value13" as "DateTimeValue13",
        					        ca_datetime."Value14" as "DateTimeValue14",
        					        ca_datetime."Value15" as "DateTimeValue15",
        					        ca_datetime."Value16" as "DateTimeValue16",
        					        ca_datetime."Value17" as "DateTimeValue17",
        					        ca_datetime."Value18" as "DateTimeValue18",
        					        ca_datetime."Value19" as "DateTimeValue19",
        					        ca_datetime."Value20" as "DateTimeValue20",
        					        ca_datetime."Value21" as "DateTimeValue21",
        					        ca_datetime."Value22" as "DateTimeValue22",
        					        ca_datetime."Value23" as "DateTimeValue23",
        					        ca_datetime."Value24" as "DateTimeValue24",
        					        ca_datetime."Value25" as "DateTimeValue25",
        					        ca_datetime."Value26" as "DateTimeValue26",
        					        ca_datetime."Value27" as "DateTimeValue27",
        					        ca_datetime."Value28" as "DateTimeValue28",
        					        ca_datetime."Value29" as "DateTimeValue29",
        					        ca_datetime."Value30" as "DateTimeValue30",
        					        ca_datetime."Value31" as "DateTimeValue31",
        					        ca_datetime."Value32" as "DateTimeValue32",
        					        ca_datetime."Value33" as "DateTimeValue33",
        					        ca_datetime."Value34" as "DateTimeValue34",
        					        ca_datetime."Value35" as "DateTimeValue35",
        					        ca_datetime."Value36" as "DateTimeValue36",
        					        ca_datetime."Value37" as "DateTimeValue37",
        					        ca_datetime."Value38" as "DateTimeValue38",
        					        ca_datetime."Value39" as "DateTimeValue39",
        					        ca_datetime."Value40" as "DateTimeValue40",
        					        ca_datetime."Value41" as "DateTimeValue41",
        					        ca_datetime."Value42" as "DateTimeValue42",
        					        ca_datetime."Value43" as "DateTimeValue43",
        					        ca_datetime."Value44" as "DateTimeValue44",
        					        ca_datetime."Value45" as "DateTimeValue45",
        					        ca_datetime."Value46" as "DateTimeValue46",
        					        ca_datetime."Value47" as "DateTimeValue47",
        					        ca_datetime."Value48" as "DateTimeValue48",
        					        ca_datetime."Value49" as "DateTimeValue49",
        					        ca_datetime."Value50" as "DateTimeValue50",
        					        ca_datetime."Value51" as "DateTimeValue51",
        					        ca_datetime."Value52" as "DateTimeValue52",
        					        ca_datetime."Value53" as "DateTimeValue53",
        					        ca_datetime."Value54" as "DateTimeValue54",
        					        ca_datetime."Value55" as "DateTimeValue55",
        					        ca_datetime."Value56" as "DateTimeValue56",
        					        ca_datetime."Value57" as "DateTimeValue57",
        					        ca_datetime."Value58" as "DateTimeValue58",
        					        ca_datetime."Value59" as "DateTimeValue59",
        					        ca_datetime."Value60" as "DateTimeValue60",
        					        ca_datetime."Value61" as "DateTimeValue61",
        					        ca_datetime."Value62" as "DateTimeValue62",
        					        ca_datetime."Value63" as "DateTimeValue63",
        					        ca_datetime."Value64" as "DateTimeValue64",
        					        ca_datetime."Value65" as "DateTimeValue65",
        					        ca_datetime."Value66" as "DateTimeValue66",
        					        ca_datetime."Value67" as "DateTimeValue67",
        					        ca_datetime."Value68" as "DateTimeValue68",
        					        ca_datetime."Value69" as "DateTimeValue69",
        					        ca_datetime."Value70" as "DateTimeValue70",
        					        ca_datetime."Value71" as "DateTimeValue71",
        					        ca_datetime."Value72" as "DateTimeValue72",
        					        ca_datetime."Value73" as "DateTimeValue73",
        					        ca_datetime."Value74" as "DateTimeValue74",
        					        ca_datetime."Value75" as "DateTimeValue75",
        					        ca_datetime."Value76" as "DateTimeValue76",
        					        ca_datetime."Value77" as "DateTimeValue77",
        					        ca_datetime."Value78" as "DateTimeValue78",
        					        ca_datetime."Value79" as "DateTimeValue79",
        					        ca_datetime."Value80" as "DateTimeValue80",
        					        ca_datetime."Value81" as "DateTimeValue81",
        					        ca_datetime."Value82" as "DateTimeValue82",
        					        ca_datetime."Value83" as "DateTimeValue83",
        					        ca_datetime."Value84" as "DateTimeValue84",
        					        ca_datetime."Value85" as "DateTimeValue85",
        					        ca_datetime."Value86" as "DateTimeValue86",
        					        ca_datetime."Value87" as "DateTimeValue87",
        					        ca_datetime."Value88" as "DateTimeValue88",
        					        ca_datetime."Value89" as "DateTimeValue89",
        					        ca_datetime."Value90" as "DateTimeValue90",
        					        ca_datetime."Value91" as "DateTimeValue91",
        					        ca_datetime."Value92" as "DateTimeValue92",
        					        ca_datetime."Value93" as "DateTimeValue93",
        					        ca_datetime."Value94" as "DateTimeValue94",
        					        ca_datetime."Value95" as "DateTimeValue95",
        					        ca_datetime."Value96" as "DateTimeValue96",
        					        ca_datetime."Value97" as "DateTimeValue97",
        					        ca_datetime."Value98" as "DateTimeValue98",
        					        ca_datetime."Value99" as "DateTimeValue99",
        					        ca_datetime."Value100" as "DateTimeValue100",

        					        ca_long."Value1" as "LongValue1",
        					        ca_long."Value2" as "LongValue2",
        					        ca_long."Value3" as "LongValue3",
        					        ca_long."Value4" as "LongValue4",
        					        ca_long."Value5" as "LongValue5",
        					        ca_long."Value6" as "LongValue6",
        					        ca_long."Value7" as "LongValue7",
        					        ca_long."Value8" as "LongValue8",
        					        ca_long."Value9" as "LongValue9",
        					        ca_long."Value10" as "LongValue10",
        					        ca_long."Value11" as "LongValue11",
        					        ca_long."Value12" as "LongValue12",
        					        ca_long."Value13" as "LongValue13",
        					        ca_long."Value14" as "LongValue14",
        					        ca_long."Value15" as "LongValue15",
        					        ca_long."Value16" as "LongValue16",
        					        ca_long."Value17" as "LongValue17",
        					        ca_long."Value18" as "LongValue18",
        					        ca_long."Value19" as "LongValue19",
        					        ca_long."Value20" as "LongValue20",
        					        ca_long."Value21" as "LongValue21",
        					        ca_long."Value22" as "LongValue22",
        					        ca_long."Value23" as "LongValue23",
        					        ca_long."Value24" as "LongValue24",
        					        ca_long."Value25" as "LongValue25",
        					        ca_long."Value26" as "LongValue26",
        					        ca_long."Value27" as "LongValue27",
        					        ca_long."Value28" as "LongValue28",
        					        ca_long."Value29" as "LongValue29",
        					        ca_long."Value30" as "LongValue30",
        					        ca_long."Value31" as "LongValue31",
        					        ca_long."Value32" as "LongValue32",
        					        ca_long."Value33" as "LongValue33",
        					        ca_long."Value34" as "LongValue34",
        					        ca_long."Value35" as "LongValue35",
        					        ca_long."Value36" as "LongValue36",
        					        ca_long."Value37" as "LongValue37",
        					        ca_long."Value38" as "LongValue38",
        					        ca_long."Value39" as "LongValue39",
        					        ca_long."Value40" as "LongValue40",
        					        ca_long."Value41" as "LongValue41",
        					        ca_long."Value42" as "LongValue42",
        					        ca_long."Value43" as "LongValue43",
        					        ca_long."Value44" as "LongValue44",
        					        ca_long."Value45" as "LongValue45",
        					        ca_long."Value46" as "LongValue46",
        					        ca_long."Value47" as "LongValue47",
        					        ca_long."Value48" as "LongValue48",
        					        ca_long."Value49" as "LongValue49",
        					        ca_long."Value50" as "LongValue50",
        					        ca_long."Value51" as "LongValue51",
        					        ca_long."Value52" as "LongValue52",
        					        ca_long."Value53" as "LongValue53",
        					        ca_long."Value54" as "LongValue54",
        					        ca_long."Value55" as "LongValue55",
        					        ca_long."Value56" as "LongValue56",
        					        ca_long."Value57" as "LongValue57",
        					        ca_long."Value58" as "LongValue58",
        					        ca_long."Value59" as "LongValue59",
        					        ca_long."Value60" as "LongValue60",
        					        ca_long."Value61" as "LongValue61",
        					        ca_long."Value62" as "LongValue62",
        					        ca_long."Value63" as "LongValue63",
        					        ca_long."Value64" as "LongValue64",
        					        ca_long."Value65" as "LongValue65",
        					        ca_long."Value66" as "LongValue66",
        					        ca_long."Value67" as "LongValue67",
        					        ca_long."Value68" as "LongValue68",
        					        ca_long."Value69" as "LongValue69",
        					        ca_long."Value70" as "LongValue70",
        					        ca_long."Value71" as "LongValue71",
        					        ca_long."Value72" as "LongValue72",
        					        ca_long."Value73" as "LongValue73",
        					        ca_long."Value74" as "LongValue74",
        					        ca_long."Value75" as "LongValue75",
        					        ca_long."Value76" as "LongValue76",
        					        ca_long."Value77" as "LongValue77",
        					        ca_long."Value78" as "LongValue78",
        					        ca_long."Value79" as "LongValue79",
        					        ca_long."Value80" as "LongValue80",
        					        ca_long."Value81" as "LongValue81",
        					        ca_long."Value82" as "LongValue82",
        					        ca_long."Value83" as "LongValue83",
        					        ca_long."Value84" as "LongValue84",
        					        ca_long."Value85" as "LongValue85",
        					        ca_long."Value86" as "LongValue86",
        					        ca_long."Value87" as "LongValue87",
        					        ca_long."Value88" as "LongValue88",
        					        ca_long."Value89" as "LongValue89",
        					        ca_long."Value90" as "LongValue90",
        					        ca_long."Value91" as "LongValue91",
        					        ca_long."Value92" as "LongValue92",
        					        ca_long."Value93" as "LongValue93",
        					        ca_long."Value94" as "LongValue94",
        					        ca_long."Value95" as "LongValue95",
        					        ca_long."Value96" as "LongValue96",
        					        ca_long."Value97" as "LongValue97",
        					        ca_long."Value98" as "LongValue98",
        					        ca_long."Value99" as "LongValue99",
        					        ca_long."Value100" as "LongValue100",
    				                operatorgroup."Name",
    				                operatorgroup."Id",
    				                cp."ContactPersonOrganization",
    				                cp."ContactPersonNameOrOrganization",
    				                cp."ContactPersonType"
        			        FROM "CRPM_DATA"."Requests" r
        			        LEFT JOIN 
        			        (
        				        SELECT  
        					        ass."AssignedTo", 
        					        ass."RequestId", 
        					        OP.assto_name
        				        FROM "CRPM_DATA"."Assignments" ass
        				        join 
        				        (
        					        SELECT 
        						        "ID" as "Id", 
        						        op."FIO" as assto_name
        					        FROM "CALC"."INFRA_Operators_FIO" op
        				        ) OP 
        				        ON 
        					        op."Id" = ass."AssignedTo"
        				        WHERE 
        					        ass."RequestId" = req_id
        						        --order by ass."RequestId"
        			        ) ass 
        			        on  
        				        ass."RequestId" = r."Id"
        			        LEFT JOIN "CRPM_DATA"."REQUESTS_LAST_CHANGES_INFO" rlci 
        				        on 
        					        rlci."REQUEST_ID"=r."Id"
        			        LEFT JOIN "CALC"."INFRA_Operators_FIO" iof 
        				        on 
        					        lower(rlci."STATUS_LAST_CHANGED_BY") = lower(iof."LOGIN")
        			        LEFT JOIN "CALC"."CFG_RequestStates" rs 
        				        ON 
        					        rs."Id" = r."State"
        			        LEFT JOIN "CALC"."CFG_RequestStateStatuses" rss 
        				        ON 
        					        rss."Id" = r."Status"
        			        LEFT JOIN "CALC"."INFRA_ServiceAreas" sa 
        				        ON 
        					        sa."Id" = r."BranchId"
        			        LEFT JOIN "CRPM_DATA"."REQUEST_CA_STRING" ca_str 
        				        on 
        					        ca_str."Id" = r."Id"
        			        LEFT JOIN "CRPM_DATA"."REQUEST_CA_LONG" ca_long 
        				        on 
        					        ca_long."Id" = r."Id"
        			        LEFT JOIN "CRPM_DATA"."REQUEST_CA_BOOL" ca_bool 
        				        on 
        					        ca_bool."Id" = r."Id"
        			        LEFT JOIN "CRPM_DATA"."REQUEST_CA_DATETIME" ca_datetime 
        				        on 
        					        ca_datetime."Id" = r."Id"
        			        LEFT JOIN "CRPM_DATA"."REQUEST_CA_GUID" ca_guid 
        				        on 
        					        ca_guid."Id"  = r."Id"
        			        LEFT JOIN
        			        (
        				        SELECT  
        					        "ParentId", 
        					        COUNT(1) "ChildCount"
        				        FROM "CRPM_DATA"."Requests"
        				        WHERE 
        					        "ParentId" IS NOT NULL
        				        GROUP BY 
        					        "ParentId"
        				        --ORDER BY "ParentId"
        			        ) c 
        			        ON 
        				        c."ParentId" = r."Id"
        			        LEFT JOIN 
        			        (
        				        SELECT
        					        CASE
        						        WHEN op3."ID" IS NULL THEN '???????'
        						        ELSE op3."FIO"
        					        END as exec_name,
        					        op3."ID" as "Id"
        				        FROM "CALC"."INFRA_Operators_FIO" op3
        			        ) exec_ 
        			        ON 
        				        exec_."Id"=r."ExecutorId"

        			        LEFT JOIN "KPI"."REQUEST_KPI" h  
        			        ON 
        				        h."Id" = r."Id"
        			        LEFT JOIN 
        			        (
        				        SELECT cp_in."ID" as "ContactPersonID", 
    						    (
    						        CONCAT_WS(' ', 
    						        	COALESCE(NULLIF(TRIM(cp_in."LAST_NAME"), ''), ''), 
    						        	COALESCE(NULLIF(TRIM(cp_in."FIRST_NAME"), ''), ''), 
    						        	COALESCE(TRIM(cp_in."MIDDLE_NAME"), ''))
    						    ) AS "ContactPersonName",
    							cp_in."NAME" AS "ContactPersonOrganization",
    							CASE 
    						    	WHEN cpcalong."Value1" = 0	-- Физическое лицо
    						    		THEN CONCAT_WS(' ', 
    						        	COALESCE(NULLIF(TRIM(cp_in."LAST_NAME"), ''), ''), 
    						        	COALESCE(NULLIF(TRIM(cp_in."FIRST_NAME"), ''), ''), 
    						        	COALESCE(TRIM(cp_in."MIDDLE_NAME"), ''))
    						        WHEN cpcalong."Value1" = 1	-- Юридическое лицо
    						        	THEN cp_in."NAME"
    						        WHEN cpcalong."Value1" = 2	-- ИП
    						        	THEN cpcastring."Value8" -- Кастом с названием ИП
    							END AS "ContactPersonNameOrOrganization",
    							cpcalong."Value1" AS "ContactPersonType"
    						from "UCMM_DATA"."CONTACT_PERSON" cp_in
    						JOIN "UCMM_DATA"."CONTACT_PERSON_CA_LONG" cpcalong ON cpcalong."ID" = cp_in."ID"
    						JOIN "UCMM_DATA"."CONTACT_PERSON_CA_STRING" cpcastring ON cpcastring."ID" = cp_in."ID"
        			        ) cp 
        			        on 
        				        r."OriginatorId"=cp."ContactPersonID"
        			        LEFT JOIN "UCMM_DATA"."CONTACT_PERSON_CA_STRING" cp_ca_str  
        			        on 
        				        cp_ca_str."ID" = r."OriginatorId"
        			        LEFT JOIN 
        			        (
        				        SELECT 
        					        w."RequestId", 
        					        wse."ExpirationDate" AS "PostponeTime"
        				        FROM "CRPM_DATA"."Works" w
        				        INNER JOIN "CRPM_DATA"."WorkStateExpirations" wse ON w."Id" = wse."WorkId"
        				        WHERE w."State" = 1
        			        ) postpone 
        			        ON 
        				        postpone."RequestId" = r."Id"
        			        LEFT JOIN
    		                (
    			                SELECT operatorgroup."Name", operatorgroup."Id", rq."Id" as "QueueId"
    			                FROM 
    				                "CRPM_CFG"."CustomAttributes" ca
    				                JOIN "CRPM_CFG"."RequestQueues" rq ON rq."Id" = ca."QueueId"
    				                JOIN "AWP_INFRA"."OperatorGroups" operatorgroup ON operatorgroup."Id"::character varying::text = ca."Value"::text
    			                WHERE 
    				                ca."Key" = 'Division'::text
    		                ) operatorgroup on operatorgroup."QueueId" = r."QueueId"
        			        WHERE 
        				        r."Id" = req_id	
        		        );
        	        EXCEPTION WHEN OTHERS THEN
        		        CALL "CALC"."P_WRITE_LOG"(SQLERRM || ', RequestId = ' || req_id, 'Error', 'P_INSERT_SINGLE_BULK_REQUESTSVIEW');
        	        END;
        	        --COMMIT;		
                END;
                
$BODY$;
