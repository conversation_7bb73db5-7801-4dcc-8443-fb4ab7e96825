DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "CALC"."__CalcContextMigrations" WHERE "MigrationId" = '20250124124531_fix_ASSI_calculating') THEN
    CREATE OR REPLACE FUNCTION "CALC"."F_QUEUES_KPI_INFO_V2"(_startdate timestamp without time zone, _enddate timestamp without time zone)
     RETURNS TABLE("ID" smallint, "TITLE" character varying, "QUEUE_TIME_DELETED" timestamp without time zone, "TOTAL_REQUESTS" bigint, "NEW_REQUESTS" bigint, "REMAINDER" bigint, "AUTOPROCESSING" bigint, "PENDING_OPERATOR_RESPONSE" bigint, "WAITING" bigint, "CLOSED_REQUESTS" bigint, "CLOSED_REQUESTS_BY_OPERATOR" bigint, "CLOSED_REQUESTS_BY_SUPERVISOR" bigint, "CLOSED_REQUESTS_BY_BOT" bigint, "CLOSED_REQUESTS_BY_SYSTEM" bigint, "PROCESSING_BY_OPERATOR" bigint, "CLOSED_REQUESTS_BY_BOT_PERCENT" numeric, "LOST" bigint, "LOST_PERCENT" numeric, "ESCALATED_BY_DISTRIBUTION" bigint, "ESCALATED_BY_DECISION" bigint, "ADT" numeric, "MAX_DT" numeric, "ACW" numeric, "CW_COUNT" bigint, "CW_SUM" bigint, "ASA" numeric, "ASA_ALARM" numeric, "ASA_WARNING" numeric, "SA_COUNT" bigint, "SA_SUM" bigint, "SA_MIN" numeric, "SA_MAX" numeric, "AHT" numeric, "AHT_ALARM" numeric, "AHT_WARNING" numeric, "HT_COUNT" bigint, "HT_SUM" bigint, "HT_MIN" numeric, "HT_MAX" numeric, "ART" numeric, "ART_ALARM" numeric, "ART_WARNING" numeric, "ART_SUM" numeric, "ART_COUNT" numeric, "A1RT" numeric, "A1RT_SUM" numeric, "A1RT_COUNT" bigint, "A1RT_MAX" numeric, "ACSI" numeric, "ACSI_ALARM" numeric, "ACSI_WARNING" numeric, "CSI_COUNT" bigint, "CSI_SUM" bigint, "CSI_GROUP_COUNT" bigint, "CSI_TOTAL_COUNT" bigint, "ASSI" numeric, "SSI_GROUP_COUNT" bigint, "SSI_COUNT" bigint, "SSI_SUM" bigint, "CSAT" numeric, "CSAT_COUNT" bigint, "CDSAT" numeric, "CDSAT_COUNT" bigint, "REDIRECTED" bigint, "REDIRECTED_PERCENT" numeric, "SL" numeric, "SL_PERCENT" numeric, "NOW" timestamp with time zone, "MAX_QUEUE_LENGTH" numeric, "CLOSED_REQUESTS_BY_OPERATOR_LAST_HOUR" bigint, "ASA_LAST_HOUR" numeric, "SA_COUNT_LAST_HOUR" bigint, "SA_SUM_LAST_HOUR" bigint, "LOST_LAST_HOUR" numeric, "PENDING_FIRST_OPERATOR_RESPONSE" bigint, "CURRENT_MAX_SA" bigint, "ACW_ALARM" numeric, "ACW_WARNING" numeric, "QWT_ALARM" numeric, "QWT_WARNING" numeric, "TST_ALARM" numeric, "TST_WARNING" numeric, "SIT_ALARM" numeric, "SIT_WARNING" numeric, "DIVISION_NAME" text, "DIVISION_ID" uuid, "WT_COUNT" bigint, "WT_SUM" bigint, "REQUEST_LIFETIME_COUNT" bigint, "REQUEST_LIFETIME_SUM" bigint)
     LANGUAGE sql
     STABLE
    AS $function$
    WITH queuesnew as (SELECT rq."Id",
                              rq."Title",
                              rq."Description",
                              rq."TimeDeleted"
                       FROM "CRPM_CFG"."RequestQueues" rq
                                LEFT JOIN "CRPM_CFG"."CustomAttributes" ca ON rq."Id" = ca."QueueId" AND ca."Key" = 'Queue.IsService'
                       WHERE
                           LOWER(ca."Value") = 'false' OR ca."Value" IS NULL),
         redirected_info AS (SELECT "REQUEST_EVENT"."SourceQueueId",
                                    count(
                                            DISTINCT CASE
                                                         WHEN "REQUEST_EVENT"."IsRedirected" = true
                                                             THEN "REQUEST_EVENT"."RequestId"
                                                         ELSE NULL::bigint
                                        END
                                    ) AS "REDIRECTED_COUNT"
                             FROM "KPI"."REQUEST_EVENT"
                             WHERE "REQUEST_EVENT"."EventTime" BETWEEN (_STARTDATE) AND (_ENDDATE)
                             GROUP BY "REQUEST_EVENT"."SourceQueueId"),
         requests_filtered_opened AS (SELECT r."Id", r."QueueId", r."TimeClosed", r."Status", r."State", r."SchemaId"
                                      FROM "CRPM_DATA"."Requests" r
                                      WHERE r."TimeClosed" IS NULL and r."SchemaId" <> 3),

         requests_filtered_closed AS (SELECT r."Id", r."QueueId", r."TimeClosed", r."Status", r."State", r."SchemaId"
                                      FROM "CRPM_DATA"."Requests" r
                                      WHERE r."TimeClosed" BETWEEN _STARTDATE AND _ENDDATE and r."SchemaId" <> 3),

         requests_un AS (SELECT *
                         FROM requests_filtered_opened
                         UNION ALL
                         SELECT *
                         FROM requests_filtered_closed),

         kpi_request_cte AS (SELECT *
                             FROM "KPI"."REQUEST_KPI" rkpi
                             WHERE rkpi."TimeClosed" IS NULL
                             UNION ALL
                             SELECT *
                             FROM "KPI"."REQUEST_KPI" rkpi
                             WHERE rkpi."TimeClosed" BETWEEN (_STARTDATE) AND (_ENDDATE)),

         art_req_kpi AS (SELECT *
                         FROM "KPI"."OPERATOR_RESPONSE" ore
                         WHERE ore."ReactionStartDate" BETWEEN (_STARTDATE) AND (_ENDDATE)),
         acw_kpi_info AS (SELECT *
                          FROM "KPI"."OPERATOR_CALL_STATE" ocs
                          WHERE ocs."TimeCallAccepted" BETWEEN (_STARTDATE) AND (_ENDDATE)),

         operator_root_group AS (
             SELECT operatorgroup."Name", operatorgroup."Id", rq."Id" as "QueueId"
             FROM
                 "CRPM_CFG"."CustomAttributes" ca
                     JOIN "CRPM_CFG"."RequestQueues" rq ON rq."Id" = ca."QueueId"
                     JOIN "AWP_INFRA"."OperatorGroups" operatorgroup ON operatorgroup."Id"::character varying::text = ca."Value"::text
             WHERE
                 ca."Key" = 'Division'::text),

         res AS (SELECT queues."Id"                                                                        AS "ID",
                        queues."Title"                                                                     AS "TITLE",
                        queues."TimeDeleted"                                                               as "QUEUE_TIME_DELETED",
                        COALESCE(rbq."TOTAL_REQUESTS", 0::bigint)                                          AS "TOTAL_REQUESTS",
                        COALESCE(rbq."NEW_REQUESTS", 0::bigint)                                            AS "NEW_REQUESTS",
                        COALESCE(rbq."REMAINDER", 0::bigint)                                               AS "REMAINDER",
                        COALESCE(rbq."AUTOPROCESSING", 0::bigint)                                          AS "AUTOPROCESSING",
                        COALESCE(rbq."PENDING_OPERATOR_RESPONSE", 0::bigint)                               AS "PENDING_OPERATOR_RESPONSE",
                        COALESCE(rbq."WAITING", 0::bigint)                                                 AS "WAITING",
                        COALESCE(rbq."CLOSED_REQUESTS", 0::bigint)                                         AS "CLOSED_REQUESTS",
                        COALESCE(rbq."CLOSED_REQUESTS_BY_OPERATOR", 0::bigint)                             AS "CLOSED_REQUESTS_BY_OPERATOR",
                        COALESCE(rbq."CLOSED_REQUESTS_BY_OPERATOR_LAST_HOUR", 0::bigint)                   AS "CLOSED_REQUESTS_BY_OPERATOR_LAST_HOUR",
                        COALESCE(rbq."CLOSED_REQUESTS_BY_SUPERVISOR", 0::bigint)                           AS "CLOSED_REQUESTS_BY_SUPERVISOR",
                        COALESCE(rbq."CLOSED_BY_BOT", 0::bigint)                                           AS "CLOSED_REQUESTS_BY_BOT",
                        COALESCE(rbq."CLOSED_BY_SYSTEM", 0::bigint)                                        AS "CLOSED_REQUESTS_BY_SYSTEM",
                        COALESCE(rbq."PROCESSING_BY_OPERATOR", 0::bigint)                                  AS "PROCESSING_BY_OPERATOR",
                        COALESCE(
                                rbq."CLOSED_BY_BOT"::numeric / NULLIF(rbq."CLOSED_REQUESTS"::double precision,
                                                                      0::double precision)::numeric(10, 2) *
                                100::numeric,
                                0::numeric(10, 2)
                        )::numeric(10, 2)                                                              AS "CLOSED_REQUESTS_BY_BOT_PERCENT",
                        COALESCE(rbq."LOST", 0::bigint)                                                    AS "LOST",
                        COALESCE(rbq."LOST_LAST_HOUR"::numeric, 0::numeric)                                AS "LOST_LAST_HOUR",
                        COALESCE(rbq."LOST_PERCENT"::numeric, 0::numeric)                                  AS "LOST_PERCENT",
                        COALESCE(rbq."AverageInQueueTime", 0::numeric)                                     AS "ADT",
                        COALESCE(rbq."MaxInQueueTime", 0::numeric)                                         AS "MAX_DT",
                        COALESCE(rbq."ACW"::numeric(10, 2), 0::numeric)                                    AS "ACW",
                        COALESCE(rbq."Count_CW", 0::bigint)                                                AS "CW_COUNT",
                        COALESCE(rbq."Sum_CW", 0::bigint)                                                  AS "CW_SUM",
                        COALESCE(rbq."ASA"::numeric(10, 2), 0::numeric)                                    AS "ASA",
                        COALESCE(rbq."ASA_LAST_HOUR"::numeric(10, 2), 0::numeric)                          AS "ASA_LAST_HOUR",
                        COALESCE(rbq."Count_SA", 0::bigint)                                                AS "SA_COUNT",
                        COALESCE(rbq."Sum_SA", 0::bigint)                                                  AS "SA_SUM",
                        COALESCE(rbq."SA_SUM_LAST_HOUR", 0::bigint)                                        AS "SA_SUM_LAST_HOUR",
                        COALESCE(rbq."SA_COUNT_LAST_HOUR", 0::bigint)                                      AS "SA_COUNT_LAST_HOUR",
                        COALESCE(rbq."MIN_SA", 0::bigint::numeric)                                         AS "SA_MIN",
                        COALESCE(rbq."MAX_SA", 0::bigint::numeric)                                         AS "SA_MAX",
                        COALESCE(rbq."AHT"::numeric(10, 2), 0::numeric)                                    AS "AHT",
                        COALESCE(rbq."Count_HT", 0::bigint)                                                AS "HT_COUNT",
                        COALESCE(rbq."Sum_HT", 0::bigint)                                                  AS "HT_SUM",
                        COALESCE(rbq."MIN_HT", 0::bigint::numeric)                                         AS "HT_MIN",
                        COALESCE(rbq."MAX_HT", 0::bigint::numeric)                                         AS "HT_MAX",
                        COALESCE(art_req."ART"::numeric(15, 2), 0::numeric)                                AS "ART",
                        COALESCE(art_req."sumRT", 0::bigint::numeric)                                      AS "ART_SUM",
                        COALESCE(art_req."countRT", 0::bigint::numeric)                                    AS "ART_COUNT",
                        COALESCE(art_req."A1RT"::numeric(15, 2), 0::numeric)                               AS "A1RT",
                        COALESCE(art_req."sum1RT", 0::bigint::numeric)                                     AS "A1RT_SUM",
                        COALESCE(art_req."count1RT", 0::bigint)                                            AS "A1RT_COUNT",
                        COALESCE(art_req."MAX_1RT", 0::bigint::numeric)                                    AS "A1RT_MAX",
                        COALESCE(rbq."ACSI"::numeric(10, 2), 0::numeric)                                   AS "ACSI",
                        COALESCE(rbq."Count_CSI"::bigint, 0::bigint)                                       AS "CSI_COUNT",
                        COALESCE(rbq."Sum_CSI"::bigint, 0::bigint)                                         AS "CSI_SUM",
                        COALESCE(rbq."CSI_GROUP_COUNT", 0::bigint)                                         AS "CSI_GROUP_COUNT",
                        COALESCE(rbq."TOTAL_CSI_COUNT", 0::bigint)                                         AS "CSI_TOTAL_COUNT",
                        COALESCE(rbq."ASSI"::numeric(10, 2), 0::numeric)                                   AS "ASSI",
                        COALESCE(rbq."SSI_GROUP_COUNT", 0)::bigint                                         AS "SSI_GROUP_COUNT",
                        COALESCE(rbq."Count_SSI", 0)::bigint                                               AS "SSI_COUNT",
                        COALESCE(rbq."Sum_SSI", 0)::bigint                                                 AS "SSI_SUM",
                        COALESCE(rbq."CSAT"::numeric(10, 2), 0::numeric)                                   AS "CSAT",
                        COALESCE(rbq."Count_CSAT", 0::bigint)                                              AS "CSAT_COUNT",
                        COALESCE(rbq."CDSAT"::numeric(10, 2), 0::numeric)                                  AS "CDSAT",
                        COALESCE(rbq."Count_CDSAT", 0::bigint)                                             AS "CDSAT_COUNT",
                        COALESCE(
                                (SELECT ri."REDIRECTED_COUNT"
                                 FROM redirected_info ri
                                 WHERE ri."SourceQueueId" = queues."Id"),
                                0::bigint
                        )                                                                                  AS "REDIRECTED",
                        COALESCE(rbq."Count_RequestLifeTime", 0::bigint)                                   AS "REQUEST_LIFETIME_COUNT",
                        COALESCE(rbq."Sum_RequestLifeTime", 0::bigint)                                     AS "REQUEST_LIFETIME_SUM",
                        COALESCE(rbq."Count_WT", 0::bigint)                                                AS "WT_COUNT",
                        COALESCE(rbq."Sum_WT", 0::bigint)                                                  AS "WT_SUM",
                        (
                            CASE
                                WHEN COALESCE(
                                             (SELECT ri."REDIRECTED_COUNT"
                                              FROM redirected_info ri
                                              WHERE ri."SourceQueueId" = queues."Id"), 0::bigint) = 0 THEN 0::numeric(10, 0)
                                ELSE (
                                    CASE
                                        WHEN rbq."TOTAL_REQUESTS" IS NULL
                                            OR rbq."TOTAL_REQUESTS" = 0 THEN 100::numeric(10, 0)
                                        ELSE (
                                            CASE
                                                WHEN (
                                                         (SELECT ri."REDIRECTED_COUNT"
                                                          FROM redirected_info ri
                                                          WHERE ri."SourceQueueId" = queues."Id")::numeric(10, 2) /
                                                         rbq."TOTAL_REQUESTS"::numeric(10, 2)
                                                         ) > 1 THEN 100::numeric(10, 0)
                                                ELSE (
                                                    100::numeric(10, 2) * (SELECT ri."REDIRECTED_COUNT"
                                                                           FROM redirected_info ri
                                                                           WHERE ri."SourceQueueId" = queues."Id")::numeric(10, 2) /
                                                    rbq."TOTAL_REQUESTS"::numeric(10, 2)
                                                    )::numeric(10, 0)
                                                END
                                            )::numeric(10, 0)
                                        END
                                    )::numeric(10, 0)
                                END
                            )::numeric(10, 0)                                                              AS "REDIRECTED_PERCENT",
                        COALESCE(rbq."SL"::numeric(10, 2), 0::numeric)                                     AS "SL",
                        COALESCE(
                                rbq."SL"::numeric /
                                NULLIF(rbq."TOTAL_REQUESTS"::double precision, 0::double precision)::numeric(10, 2) *
                                100::numeric,
                                0::numeric(10, 2)
                        )::numeric(10, 0)                                                              AS "SL_PERCENT",
                        timezone('UTC'::text, now()::timestamp with time zone)::timestamp with time zone AS "NOW",
                        COALESCE(rbq."ESCALATED_BY_DISTRIBUTION", 0::bigint)                               AS "ESCALATED_BY_DISTRIBUTION",
                        COALESCE(rbq."ESCALATED_BY_DECISION", 0::bigint)                                   AS "ESCALATED_BY_DECISION",
                        COALESCE(rbq."PENDING_FIRST_OPERATOR_RESPONSE", 0::bigint)                         AS "PENDING_FIRST_OPERATOR_RESPONSE",
                        COALESCE(rbq."CURRENT_MAX_SA"::bigint, 0::bigint)                                  AS "CURRENT_MAX_SA",
                        alarms_queue."ACW_ALARM"::numeric,
                        alarms_queue."ACW_WARNING"::numeric,

                        alarms_queue."ART_ALARM"::numeric,
                        alarms_queue."ART_WARNING"::numeric,

                        alarms_queue."ACSI_ALARM"::numeric,
                        alarms_queue."ACSI_WARNING"::numeric,

                        alarms_queue."ASA_ALARM"::numeric,
                        alarms_queue."ASA_WARNING"::numeric,

                        alarms_queue."AHT_ALARM"::numeric,
                        alarms_queue."AHT_WARNING"::numeric,

                        alarms_queue."QWT_ALARM"::numeric,
                        alarms_queue."QWT_WARNING"::numeric,

                        alarms_queue."TST_ALARM"::numeric,
                        alarms_queue."TST_WARNING"::numeric,

                        alarms_queue."SIT_ALARM"::numeric,
                        alarms_queue."SIT_WARNING"::numeric,
                        alarms_queue."MAX_QUEUE_LENGTH"::numeric,

                        operator_root_group."Name" AS "DIVISION_NAME",
                        operator_root_group."Id" AS "DIVISION_ID"
                 FROM queuesnew AS queues
                          LEFT JOIN LATERAL (
                     SELECT count(1) AS     "TOTAL_REQUESTS",
                            count(1) FILTER (
                                WHERE
                                r_kpi."TimeRegistered" >=
                                _STARTDATE
                                )    AS     "NEW_REQUESTS",
                            count(1) FILTER (
                                WHERE
                                r_kpi."TimeRegistered" < _STARTDATE
                                )    AS     "REMAINDER",
                            count(1) FILTER (
                                WHERE
                                r."TimeClosed" IS NOT NULL
                                )    AS     "CLOSED_REQUESTS",
                            count(1) FILTER (
                                WHERE
                                r."TimeClosed" IS NOT NULL AND
                                r_kpi."ClosedByUserType" = 1 -- Operator
                                )    AS     "CLOSED_REQUESTS_BY_OPERATOR",
                            count(1) FILTER (
                                WHERE
                                r."TimeClosed" IS NOT NULL
                                    AND r_kpi."ClosedByUserType" = 1 /*Operator*/
                                    AND r_kpi."TimeClosed" >=
                                        _ENDDATE -
                                        '1 hour '::interval
                                ) AS "CLOSED_REQUESTS_BY_OPERATOR_LAST_HOUR",
                            count(1) FILTER (
                                WHERE
                                r."TimeClosed" IS NOT NULL AND
                                r_kpi."ClosedByUserType" = 2 -- Supervisor
                                ) AS "CLOSED_REQUESTS_BY_SUPERVISOR",
                            count(1) FILTER (
                                WHERE
                                r."TimeClosed" IS NOT NULL
                                    AND
                                (
                                    (
                                        r_kpi."ClosedByUserType" = 4 -- System
                                            OR r_kpi."ClosedByUserType" = 0 -- Unknown
                                        )
                                        OR
                                    (
                                        r_kpi."ClosedByUserType" IS NULL
                                            AND r_kpi."TimeBotWorkEnded" IS NULL
                                        )
                                    )
                                ) AS "CLOSED_BY_SYSTEM",
                            count(1) FILTER (
                                WHERE
                                r."TimeClosed" IS NOT NULL
                                    AND
                                (
                                    r_kpi."ClosedByUserType" = 3 -- Bot
                                        OR
                                    (
                                        r_kpi."ClosedByUserType" IS NULL
                                            AND r_kpi."TimeBotWorkEnded" IS NOT NULL
                                        )
                                    )
                                ) AS "CLOSED_BY_BOT",
                            count(1) FILTER (
                                WHERE r."Status" IN (
                                    (SELECT rss."Id"
                                     FROM "CRPM_CFG"."RequestStateStatuses" rss
                                     WHERE rss."Code"::text IN (
                                                                'OPERATOR.WAIT'::text,
                                                                'CLAIM.OPERATOR.WAIT'::text)))
                                ) AS "PENDING_OPERATOR_RESPONSE",
                            count(1) FILTER (
                                WHERE r."Status" IN (
                                    (SELECT rss."Id"
                                     FROM "CRPM_CFG"."RequestStateStatuses" rss
                                     WHERE rss."Code"::text IN (
                                                                'OPERATOR.WAIT'::text,
                                                                'CLAIM.OPERATOR.WAIT'::text))) AND r_kpi."SA" = 0
                                ) AS "PENDING_FIRST_OPERATOR_RESPONSE",
                            avg(r_kpi."HT") FILTER ( WHERE r_kpi."HT" <> 0 ) AS "AHT",
                            count(r_kpi."HT") FILTER ( WHERE r_kpi."HT" <> 0 ) AS "Count_HT",
                            sum(r_kpi."HT") AS "Sum_HT",
                            round(
                                    COALESCE(
                                            min(
                                                    CASE
                                                        WHEN r_kpi."HT" <> 0 THEN r_kpi."HT"
                                                        ELSE NULL::integer
                                                        END
                                            )::numeric(10, 0),
                                            0::numeric(10, 0)
                                    )
                            ) AS "MIN_HT",
                            round(max(r_kpi."HT")::numeric(10, 0)) AS "MAX_HT",
                            avg(r_kpi."SA") FILTER (WHERE r."TimeClosed" IS NOT NULL AND
                                                          (r_kpi."TimeOperatorFirstResponse" IS NULL OR
                                                           r_kpi."TimeOperatorFirstResponse" BETWEEN _STARTDATE AND _ENDDATE)) AS "ASA",
                            avg(r_kpi."SA") FILTER (
                                WHERE r_kpi."TimeRegistered" >=
                                      _ENDDATE -
                                      '1 hour'::interval AND (r."TimeClosed" IS NOT NULL AND (r_kpi."TimeOperatorFirstResponse" IS NULL OR r_kpi."TimeOperatorFirstResponse" BETWEEN _STARTDATE AND _ENDDATE))) AS "ASA_LAST_HOUR",
                            sum(r_kpi."SA")  FILTER (
                                WHERE r_kpi."TimeRegistered" >= _ENDDATE - '1 hour'::interval AND (r."TimeClosed" IS NOT NULL AND (r_kpi."TimeOperatorFirstResponse" IS NULL OR r_kpi."TimeOperatorFirstResponse" BETWEEN _STARTDATE AND _ENDDATE))
                                ) AS "SA_SUM_LAST_HOUR",
                            count(r_kpi."SA")  FILTER (
                                WHERE r_kpi."TimeRegistered" >= _ENDDATE - '1 hour'::interval AND (r."TimeClosed" IS NOT NULL AND (r_kpi."TimeOperatorFirstResponse" IS NULL OR r_kpi."TimeOperatorFirstResponse" BETWEEN _STARTDATE AND _ENDDATE))
                                ) AS "SA_COUNT_LAST_HOUR",
                            count(r_kpi."SA") FILTER (WHERE r."TimeClosed" IS NOT NULL AND (r_kpi."TimeOperatorFirstResponse" IS NULL OR r_kpi."TimeOperatorFirstResponse" BETWEEN _STARTDATE AND _ENDDATE)) AS "Count_SA",
                            sum(r_kpi."SA") FILTER (WHERE r."TimeClosed" IS NOT NULL AND (r_kpi."TimeOperatorFirstResponse" IS NULL OR r_kpi."TimeOperatorFirstResponse" BETWEEN _STARTDATE AND _ENDDATE)) AS "Sum_SA",
                            round((min(CASE WHEN r_kpi."SA" <> 0 THEN r_kpi."SA" ELSE NULL::integer END) FILTER (WHERE r."TimeClosed" IS NOT NULL AND (r_kpi."TimeOperatorFirstResponse" IS NULL OR r_kpi."TimeOperatorFirstResponse" BETWEEN _STARTDATE AND _ENDDATE))) ::double precision)::numeric(10, 0) AS "MIN_SA",
                            round((max(r_kpi."SA") FILTER (WHERE r."TimeClosed" IS NOT NULL AND (r_kpi."TimeOperatorFirstResponse" IS NULL OR r_kpi."TimeOperatorFirstResponse" BETWEEN _STARTDATE AND _ENDDATE)))::double precision)::numeric(10, 0) AS "MAX_SA",
                            MAX( -- Считаем только "тикающий SA". Для отсальных возвращаем 0
                                    CASE
                                        WHEN
                                            r_kpi."TimeOperatorFirstResponse" IS NULL
                                                AND r."TimeClosed" IS NULL
                                                AND r_kpi."TimeCreated" IS NOT NULL
                                            THEN
                                            CASE
                                                WHEN r."Status" IN (SELECT rss."Id" FROM "CRPM_CFG"."RequestStateStatuses" rss WHERE rss."Code"::text IN ('CHATBOT'::text, 'CHATBOT.WAIT'::text, 'AUTOPROCESSING'::text)) THEN 0
                                                WHEN r_kpi."TimeBotWorkEnded" IS NOT NULL THEN date_part('epoch'::text, timezone('utc'::text, now())::timestamp with time zone - r_kpi."TimeBotWorkEnded"::timestamp with time zone)
                                                ELSE
                                                    date_part('epoch'::text, timezone('utc'::text, now())::timestamp with time zone - r_kpi."TimeCreated"::timestamp with time zone)
                                                END
                                        ELSE 0
                                        END
                            )::bigint AS "CURRENT_MAX_SA",
                            avg( -- Глянуть
                                    date_part(
                                            'epoch'::text,
                                            COALESCE(
                                                    r_kpi."TimeOperatorFirstDistribution"::timestamp with time zone,
                                                    CASE
                                                        WHEN r."TimeClosed" IS NULL THEN timezone('utc'::text, now())::timestamp with time zone
                                                        ELSE r."TimeClosed"::timestamp with time zone
                                                        END
                                            ) - COALESCE(
                                                    r_kpi."TimeBotWorkEnded"::timestamp with time zone,
                                                    r_kpi."TimeCreated"::timestamp with time zone
                                                )
                                    )
                            )::numeric(20, 0) AS "AverageInQueueTime",
                            max( -- Глянуть
                                    date_part(
                                            'epoch'::text,
                                            COALESCE(
                                                    r_kpi."TimeOperatorFirstDistribution"::timestamp with time zone,
                                                    CASE
                                                        WHEN r."TimeClosed" IS NULL THEN timezone('utc'::text, now())::timestamp with time zone
                                                        ELSE r."TimeClosed"::timestamp with time zone
                                                        END
                                            ) - COALESCE(
                                                    r_kpi."TimeBotWorkEnded"::timestamp with time zone,
                                                    r_kpi."TimeCreated"::timestamp with time zone
                                                )
                                    )
                            )::numeric(20, 0) AS "MaxInQueueTime",
                            count(r_kpi."TimeLost") AS "LOST",
                            count(r_kpi."TimeLost") FILTER (
                                WHERE r_kpi."TimeLost" >= _ENDDATE - '1 hour'::interval
                                ) AS "LOST_LAST_HOUR",
                            count(r_kpi."TimeLost")::double precision / NULLIF(
                                    count(r_kpi."Id")::double precision,
                                    0::double precision
                                                                        ) * 100::double precision::numeric(10, 0) AS "LOST_PERCENT",
                            count(1) FILTER (
                                WHERE r."Status" IN (
                                    SELECT rss."Id"
                                    FROM "CRPM_CFG"."RequestStateStatuses" rss
                                    WHERE rss."Code"::text = ANY (
                                        ARRAY ['OPERATOR.WORK'::character varying::text, 'OPERATOR.PAUSE'::character varying::text,'CLAIM.OPERATOR.WORK'::character varying::text,'CLAIM.OPERATOR.PAUSE'::character varying::text ]
                                        ))
                                ) AS "PROCESSING_BY_OPERATOR",
                            count(1) FILTER (
                                WHERE r."Status" IN (
                                    SELECT rss."Id"
                                    FROM "CRPM_CFG"."RequestStateStatuses" rss
                                    WHERE rss."Code"::text = ANY (
                                        ARRAY ['OPERATOR.POSTPONE'::character varying::text, 'DIVISION.WAIT'::character varying::text, 'OPERATOR.WAIT.CLIENT'::character varying::text, 'SUPERVISOR'::character varying::text]
                                        ))
                                ) AS "WAITING",
                            count(1) FILTER (
                                WHERE r."State" IS NULL OR r."Status" IN
                                                           (
                                                               SELECT rss."Id"
                                                               FROM "CRPM_CFG"."RequestStateStatuses" rss
                                                               WHERE rss."Code"::text =ANY (
                                                                   ARRAY [
                                                                       'AUTOPROCESSING'::character varying::text,
                                                                       'THEMES.WAIT'::character varying::text,
                                                                       'PVOO.SEND'::character varying::text,
                                                                       'REROUTING'::character varying::text,
                                                                       'CHATBOT'::character varying::text,
                                                                       'CHATBOT.WAIT'::character varying::text,
                                                                       'THEMES'::character varying::text,
                                                                       'SCRIPTBOT.WAIT'::character varying::text,
                                                                       'SCRIPTBOT.PROCESSING'::character varying::text]
                                                                   )
                                                           )
                                ) AS "AUTOPROCESSING",
                            COUNT(1) FILTER (
                                WHERE
                                r_kpi."SA" IS NOT NULL
                                    AND r_kpi."SA"::double precision <= (SELECT queueasa."AlarmASA" FROM "CALC"."CFG_Queue_ASA" AS queueasa WHERE queueasa."Id" = r."QueueId")
                                ) AS "SL",
                            COUNT(1) FILTER (
                                WHERE
                                bulk_requests_view."RequestOverdue" = 1
                                ) AS "ESCALATED_BY_DISTRIBUTION",
                            COUNT(1) FILTER (
                                WHERE
                                bulk_requests_view."RequestDecisionOverdue" = 1
                                ) AS "ESCALATED_BY_DECISION",
                            AVG(ocs."ACW") AS "ACW",
                            COUNT(ocs."ACW") AS "Count_CW",
                            SUM(ocs."ACW") AS "Sum_CW",
                            AVG(rkpi."Score") AS "ACSI",
                            SUM(rkpi."Score"::bigint) AS "Sum_CSI",
                            COUNT(rkpi."Score"::bigint) AS "Count_CSI",
                            COUNT(
                                    DISTINCT CASE
                                                 WHEN rkpi."Score" IS NULL THEN NULL::bigint
                                                 ELSE rkpi."Score"
                                END
                            ) AS "CSI_GROUP_COUNT",
                            COALESCE(COUNT(rkpi."Score"), 0::bigint) AS "TOTAL_CSI_COUNT",
                            COALESCE(
                                    COUNT(
                                            CASE
                                                WHEN rkpi."Score" > 4 THEN rkpi."Score"
                                                ELSE NULL::smallint
                                                END
                                    )::numeric(10, 2) / NULLIF(COUNT(rkpi."Score"), 0)::numeric,
                                    0::numeric
                            ) * 100::numeric(10, 2) AS "CSAT",
                            COUNT(
                                    CASE
                                        WHEN rkpi."Score" > 4 THEN rkpi."Score"
                                        ELSE NULL::smallint
                                        END
                            ) AS "Count_CSAT",
                            COALESCE(
                                    COUNT(
                                            CASE
                                                WHEN rkpi."Score" < 4 THEN rkpi."Score"
                                                ELSE NULL::smallint
                                                END
                                    )::numeric(10, 2) / NULLIF(COUNT(rkpi."Score"), 0)::numeric,
                                    0::numeric
                            ) * 100::numeric(10, 2) AS "CDSAT",
                            COUNT(
                                    CASE
                                        WHEN rkpi."Score" < 4 THEN rkpi."Score"
                                        ELSE NULL::smallint
                                        END
                            ) AS "Count_CDSAT",
                            (SELECT
                                 COUNT(
                                         DISTINCT CASE
                                                      WHEN psir."SSI" IS NULL THEN NULL::bigint
                                                      ELSE psir."RequestId"
                                     END
                                 )) AS "SSI_GROUP_COUNT",
                            AVG(psir."SSI") AS "ASSI",
                            COUNT(psir."SSI") AS "Count_SSI",
                            SUM(psir."SSI") AS "Sum_SSI",
                            count(r."Id") FILTER (WHERE r."TimeClosed" IS NOT NULL) AS "Count_RequestLifeTime",
                            sum(
                            date_part('epoch',
                                      CASE
                                          WHEN r_kpi."TimeClosed" IS NULL THEN timezone('utc'::text, now())::timestamp with time zone
                                          ELSE r_kpi."TimeClosed"::timestamp with time zone
                                          END - r_kpi."TimeCreated"::timestamp with time zone
                            )
                               ) FILTER (WHERE r_kpi."TimeClosed" IS NOT NULL) AS "Sum_RequestLifeTime",
                            count(r_kpi."WT") FILTER ( WHERE r_kpi."WT" <> 0 ) AS "Count_WT",
                            sum(r_kpi."WT") AS "Sum_WT"
                     FROM
                         requests_un r
                             LEFT JOIN "CRPM_DATA"."REQUEST_CA_GUID" AS rkpii ON r."Id" = rkpii."Id"
                             LEFT JOIN LATERAL (SELECT * FROM kpi_request_cte r_k WHERE r."Id" = r_k."Id") r_kpi
                                       ON TRUE
                             LEFT JOIN LATERAL (SELECT * FROM "CALC"."BULK_REQUESTSVIEW" brv WHERE r."Id" = brv."Id") bulk_requests_view ON TRUE
                             LEFT JOIN LATERAL (SELECT * FROM acw_kpi_info WHERE acw_kpi_info."RequestId" = r."Id") ocs ON TRUE
                             LEFT JOIN LATERAL (SELECT ee."Score"
                                                FROM "EXTERNAL_EVALUATIONS"."ExternalEvaluations" ee
                                                WHERE ee."RequestGuidId" = rkpii."Value3" AND r."SchemaId" <> 3 AND ee."OperatorId" IS NOT NULL
                                                ORDER BY ee."Date" DESC LIMIT 1) rkpi ON TRUE
                             LEFT JOIN LATERAL (SELECT
                                                    (CASE
                                                         WHEN (r_kpi."SSI" IS NOT NULL AND "PublishedAt" IS NULL) THEN r_kpi."SSI"
                                                         ELSE COALESCE (e."ScoreResult", r_kpi."SSI")
                                                        END) AS "SSI",
                                                    r."Id" as "RequestId"
                                                FROM "CALC"."V_EVALUATIONS" AS e 
    											WHERE r."Id" = (e."Context" ->> 'requestId')::bigint 
    												AND (e."ScoreResult" IS NOT NULL OR r_kpi."SSI" IS NOT NULL)) psir ON TRUE
                     WHERE
                         r."QueueId" = queues."Id"
                     ) rbq
                                    ON TRUE
                          LEFT JOIN LATERAL (
                     SELECT
                         sum(ore."RT") AS "sumRT",
                         COUNT(ore."RT") AS "countRT",
                         sum(ore."RT") / COUNT(ore."RT") AS "ART",
                         AVG(ore."RT") AS "A1RT",
                         SUM(ore."RT") AS "sum1RT",
                         MAX(ore."RT")::numeric (10, 0) AS "MAX_1RT",
                         COUNT(DISTINCT ore."RequestId") AS "count1RT"
                     FROM art_req_kpi ore
                     WHERE ore."QueueId" = queues."Id"
                     ) art_req ON TRUE
                          LEFT JOIN LATERAL (
                     select
                         max(
                                 CASE
                                     WHEN "CustomAttributes"."Key" = 'Queue.ThresholdValues.AlarmASA'::text 
                                     THEN NULLIF(COALESCE("CustomAttributes"."Value", '0'::character varying), '')::double precision
                                     ELSE NULL::double precision
                                     END) AS "ASA_ALARM",
                         max(
                                 CASE
                                     WHEN "CustomAttributes"."Key" = 'Queue.ThresholdValues.WarningASA'::text 
                                     THEN NULLIF(COALESCE("CustomAttributes"."Value", '0'::character varying), '')::double precision
                                     ELSE NULL::double precision
                                     END) AS "ASA_WARNING",
                         max(
                                 CASE
                                     WHEN "CustomAttributes"."Key" = 'Queue.ThresholdValues.AlarmAHT'::text 
                                     THEN NULLIF(COALESCE("CustomAttributes"."Value", '0'::character varying), '')::double precision
                                     ELSE NULL::double precision
                                     END) AS "AHT_ALARM",
                         max(
                                 CASE
                                     WHEN "CustomAttributes"."Key" = 'Queue.ThresholdValues.WarningAHT'::text 
                                     THEN NULLIF(COALESCE("CustomAttributes"."Value", '0'::character varying), '')::double precision
                                     ELSE NULL::double precision
                                     END) AS "AHT_WARNING",
                         max(
                                 CASE
                                     WHEN "CustomAttributes"."Key" = 'Queue.ThresholdValues.AlarmACSI'::text 
                                     THEN NULLIF(COALESCE("CustomAttributes"."Value", '0'::character varying), '')::double precision
                                     ELSE NULL::double precision
                                     END) AS "ACSI_ALARM",
                         max(
                                 CASE
                                     WHEN "CustomAttributes"."Key" = 'Queue.ThresholdValues.WarningACSI'::text 
                                     THEN NULLIF(COALESCE("CustomAttributes"."Value", '0'::character varying), '')::double precision
                                     ELSE NULL::double precision
                                     END) AS "ACSI_WARNING",
                         max(
                                 CASE
                                     WHEN "CustomAttributes"."Key" = 'Queue.ThresholdValues.AlarmClientResponseTimeout'::text 
                                     THEN NULLIF(COALESCE("CustomAttributes"."Value", '0'::character varying), '')::double precision
                                     ELSE NULL::double precision
                                     END) AS "ART_ALARM",
                         max(
                                 CASE
                                     WHEN "CustomAttributes"."Key" = 'Queue.ThresholdValues.WarningClientResponseTimeout'::text 
                                     THEN NULLIF(COALESCE("CustomAttributes"."Value", '0'::character varying), '')::double precision
                                     ELSE NULL::double precision
                                     END) AS "ART_WARNING",
                         COALESCE(max(
                                          CASE
                                              WHEN "CustomAttributes"."Key" = 'Queue.ThresholdValues.MaxQueueLength'::text 
                                              THEN NULLIF(COALESCE("CustomAttributes"."Value", '0'::character varying), '')::double precision
                                              ELSE NULL::double precision
                                              END), 0::double precision)::numeric(10, 2)  AS "MAX_QUEUE_LENGTH",
                         max(
                                 CASE
                                     WHEN "CustomAttributes"."Key" = 'Queue.ThresholdValues.AcwTime'::text 
                                     THEN NULLIF(COALESCE("CustomAttributes"."Value", '0'::character varying), '')::double precision
                                     ELSE NULL::double precision
                                     END) AS "ACW_ALARM",
                         max(
                                 CASE
                                     WHEN "CustomAttributes"."Key" = 'Queue.ThresholdValues.WarningAcwTime'::text 
                                     THEN NULLIF(COALESCE("CustomAttributes"."Value", '0'::character varying), '')::double precision
                                     ELSE NULL::double precision
                                     END) AS "ACW_WARNING",
                         max(
                                 CASE
                                     WHEN "CustomAttributes"."Key" = 'Queue.ThresholdValues.AlarmQWT'::text 
                                     THEN NULLIF(COALESCE("CustomAttributes"."Value", '0'::character varying), '')::double precision
                                     ELSE NULL::double precision
                                     END) AS "QWT_ALARM",
                         max(
                                 CASE
                                     WHEN "CustomAttributes"."Key" = 'Queue.ThresholdValues.WarningQWT'::text 
                                     THEN NULLIF(COALESCE("CustomAttributes"."Value", '0'::character varying), '')::double precision
                                     ELSE NULL::double precision
                                     END) AS "QWT_WARNING",
                         max(
                                 CASE
                                     WHEN "CustomAttributes"."Key" = 'Queue.ThresholdValues.AlarmCP'::text 
                                     THEN NULLIF(COALESCE("CustomAttributes"."Value", '0'::character varying), '')::double precision
                                     ELSE NULL::double precision
                                     END) AS "TST_ALARM",
                         max(
                                 CASE
                                     WHEN "CustomAttributes"."Key" = 'Queue.ThresholdValues.WarningCP'::text 
                                     THEN NULLIF(COALESCE("CustomAttributes"."Value", '0'::character varying), '')::double precision
                                     ELSE NULL::double precision
                                     END) AS "TST_WARNING",
                         max(
                                 CASE
                                     WHEN "CustomAttributes"."Key" = 'Queue.ThresholdValues.AlarmSessionInactivityTimer'::text 
                                     THEN NULLIF(COALESCE("CustomAttributes"."Value", '0'::character varying), '')::double precision
                                     ELSE NULL::double precision
                                     END) AS "SIT_ALARM",
                         max(
                                 CASE
                                     WHEN "CustomAttributes"."Key" = 'Queue.ThresholdValues.WarningSessionInactivityTimer'::text 
                                     THEN NULLIF(COALESCE("CustomAttributes"."Value", '0'::character varying), '')::double precision
                                     ELSE NULL::double precision
                                     END) AS "SIT_WARNING"
                     FROM "CRPM_CFG"."CustomAttributes"
                     WHERE "CustomAttributes"."QueueId" = queues."Id" and "CustomAttributes"."Key" = ANY (ARRAY['Queue.ThresholdValues.AlarmASA'::text, 'Queue.ThresholdValues.WarningASA'::text, 'Queue.ThresholdValues.AlarmAHT'::text, 'Queue.ThresholdValues.WarningAHT'::text, 'Queue.ThresholdValues.AlarmACSI'::text, 'Queue.ThresholdValues.WarningACSI'::text, 'Queue.ThresholdValues.AlarmClientResponseTimeout'::text, 'Queue.ThresholdValues.WarningClientResponseTimeout'::text, 'Queue.ThresholdValues.MaxQueueLength'::text, 'Queue.ThresholdValues.AcwTime'::text, 'Queue.ThresholdValues.WarningAcwTime'::text, 'Queue.ThresholdValues.AlarmQWT'::text, 'Queue.ThresholdValues.WarningQWT'::text, 'Queue.ThresholdValues.AlarmCP'::text, 'Queue.ThresholdValues.WarningCP'::text, 'Queue.ThresholdValues.AlarmSessionInactivityTimer'::text, 'Queue.ThresholdValues.WarningSessionInactivityTimer'::text])
                     ) alarms_queue ON TRUE
                          LEFT JOIN operator_root_group ON operator_root_group."QueueId" = queues."Id"
         )
    SELECT "ID",                                    -- Id очереди
           "TITLE",                                 -- Название очереди
           "QUEUE_TIME_DELETED",                    -- Время удаление очереди (Для фильтрации)
           "TOTAL_REQUESTS",                        -- Всего
           "NEW_REQUESTS",                          -- Новые
           "REMAINDER",                             -- Перешедшие остатком
           "AUTOPROCESSING",                        -- Автообработка
           "PENDING_OPERATOR_RESPONSE",             -- Ожидают обработки оператора
           "WAITING",                               -- Ожидание
           "CLOSED_REQUESTS",                       -- Закрытые Всего
           "CLOSED_REQUESTS_BY_OPERATOR",           -- Закрытые Оператором
           "CLOSED_REQUESTS_BY_SUPERVISOR",         -- Закрытые Супервизором
           "CLOSED_REQUESTS_BY_BOT",                -- Закрытые БОТом
           "CLOSED_REQUESTS_BY_SYSTEM",             -- Закрытые Системой
           "PROCESSING_BY_OPERATOR",                -- Обрабатываются оператором
           "CLOSED_REQUESTS_BY_BOT_PERCENT",        -- % автоматиации (Доля обращений, которые были закрыты БОТом)
           "LOST",                                  -- Потеряно
           "LOST_PERCENT",                          -- LCR
           "ESCALATED_BY_DISTRIBUTION",             -- Просрочено по распределению (Кол-во обращений очереди, которые были просрочены по сроку распределения)
           "ESCALATED_BY_DECISION",                 -- Просрочено по решению (Кол-во обращений очереди, которые были просрочены по сроку решения)
           "ADT",                                   -- ADT
           "MAX_DT",                                -- Max DT
           "ACW",                                   -- ACW
           "CW_COUNT",
           "CW_SUM",
           "ASA",                                   -- ASA
           "ASA_ALARM",
           "ASA_WARNING",
           "SA_COUNT",
           "SA_SUM",
           "SA_MIN",
           "SA_MAX",
           "AHT",                                   -- AHT
           "AHT_ALARM",
           "AHT_WARNING",
           "HT_COUNT",
           "HT_SUM",
           "HT_MIN",
           "HT_MAX",
           "ART",                                   -- ART
           "ART_ALARM",
           "ART_WARNING",
           "ART_SUM",
           "ART_COUNT",
           "A1RT",                                  -- A1RT
           "A1RT_SUM",
           "A1RT_COUNT",
           "A1RT_MAX",
           "ACSI",                                  -- ACSI
           "ACSI_ALARM",
           "ACSI_WARNING",
           "CSI_COUNT",
           "CSI_SUM",
           "CSI_GROUP_COUNT",
           "CSI_TOTAL_COUNT",
           "ASSI",                                  -- ASSI
           "SSI_GROUP_COUNT",
           "SSI_COUNT",
           "SSI_SUM",
           "CSAT",                                  -- CSAT
           "CSAT_COUNT",
           "CDSAT",                                 -- CDSAT
           "CDSAT_COUNT",
           "REDIRECTED",                            -- Переведено по очереди
           "REDIRECTED_PERCENT",                    -- Переведено %
           "SL",
           "SL_PERCENT",                            -- % обращений, по которым клиент получил ответ за заданное для очереди целевое время ASA, от общего количества обращений в очереди 
           "NOW",
           "MAX_QUEUE_LENGTH",
           "CLOSED_REQUESTS_BY_OPERATOR_LAST_HOUR", -- Закрыто за _ENDDATE - 1ч
           "ASA_LAST_HOUR",
           "SA_COUNT_LAST_HOUR",
           "SA_SUM_LAST_HOUR",
           "LOST_LAST_HOUR",                        --  Потеряно за _ENDDATE - 1ч
           "PENDING_FIRST_OPERATOR_RESPONSE",       -- Ожидает первого ответа оператора

           "CURRENT_MAX_SA",
           "ACW_ALARM",
           "ACW_WARNING",
           "QWT_ALARM",
           "QWT_WARNING",
           "TST_ALARM",
           "TST_WARNING",
           "SIT_ALARM",
           "SIT_WARNING",
           "DIVISION_NAME",
           "DIVISION_ID",
           "WT_COUNT",
           "WT_SUM",
           "REQUEST_LIFETIME_COUNT",
           "REQUEST_LIFETIME_SUM"
    FROM res AS r;



    $function$
    ;

    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "CALC"."__CalcContextMigrations" WHERE "MigrationId" = '20250124124531_fix_ASSI_calculating') THEN
    INSERT INTO "CALC"."__CalcContextMigrations" ("MigrationId", "ProductVersion")
    VALUES ('20250124124531_fix_ASSI_calculating', '9.0.1');
    END IF;
END $EF$;
