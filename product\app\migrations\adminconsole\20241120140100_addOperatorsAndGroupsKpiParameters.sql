INSERT INTO "AWP_INFRA"."Configurations" ("Id", "Name", "Value", "Description", "LastChangeTime")
SELECT uuid_generate_v4()::uuid,
        'KpiThresholdsDefaultValueSetting',
       '{
     "OperatorsKpiThresholdsDefaultValueSetting": [
       {
         "Code": "CountProcessedRequests",
         "DisplayName": "Кол-во обработанных обращений",
         "IsInteger": true,
         "DefaultValue": 100,
         "MinValue": 0,
         "MaxValue": null
       },
       {
         "Code": "CountClosedRequests",
         "DisplayName": "Кол-во закрытых обращений",
         "IsInteger": true,
         "DefaultValue": 120,
         "MinValue": 0,
         "MaxValue": null
       },
       {
         "Code": "CountTransferRequests",
         "DisplayName": "Кол-во переведенных обращений",
         "IsInteger": true,
         "DefaultValue": 10,
         "MinValue": 0,
         "MaxValue": 30
       },
       {
         "Code": "CountPostponeRequests",
         "DisplayName": "Кол-во отложенных обращений",
         "IsInteger": true,
         "DefaultValue": 5,
         "MinValue": 0,
         "MaxValue": 30
       },
       {
         "Code": "AverageReactionTime",
         "DisplayName": "Среднее время реакции (сек.)",
         "IsInteger": true,
         "DefaultValue": 20,
         "MinValue": 5,
         "MaxValue": 40
       },
       {
         "Code": "AHT",
         "DisplayName": "AHT (сек.)",
         "IsInteger": true,
         "DefaultValue": 180,
         "MinValue": 30,
         "MaxValue": 350
       },
       {
         "Code": "ACSI",
         "DisplayName": "ACSI",
         "IsInteger": false,
         "DefaultValue": 4.8,
         "MinValue": 1.0,
         "MaxValue": 5.0
       },
       {
         "Code": "ASSI",
         "DisplayName": "ASSI",
         "IsInteger": false,
         "DefaultValue": 4.7,
         "MinValue": 1.0,
         "MaxValue": 5.0
       }
     ],
     "OperatorGroupsKpiThresholdsDefaultValueSetting": [
       {
         "Code": "CountProcessedRequests",
         "DisplayName": "Кол-во обработанных обращений",
         "IsInteger": true,
         "DefaultValue": 120,
         "MinValue": 0,
         "MaxValue": null
       },
       {
         "Code": "CountClosedRequests",
         "DisplayName": "Кол-во закрытых обращений",
         "IsInteger": true,
         "DefaultValue": 120,
         "MinValue": 0,
         "MaxValue": null
       },
       {
         "Code": "CountTransferRequests",
         "DisplayName": "Кол-во переведенных обращений",
         "IsInteger": true,
         "DefaultValue": 10,
         "MinValue": 0,
         "MaxValue": null
       },
       {
         "Code": "CountPostponeRequests",
         "DisplayName": "Кол-во отложенных обращений",
         "IsInteger": true,
         "DefaultValue": 5,
         "MinValue": 0,
         "MaxValue": 30
       },
       {
         "Code": "AverageReactionTime",
         "DisplayName": "Среднее время реакции (сек.)",
         "IsInteger": true,
         "DefaultValue": 20,
         "MinValue": 5,
         "MaxValue": 40
       },
       {
         "Code": "AHT",
         "DisplayName": "AHT (сек.)",
         "IsInteger": true,
         "DefaultValue": 180,
         "MinValue": 30,
         "MaxValue": 350
       },
       {
         "Code": "ACSI",
         "DisplayName": "ACSI",
         "IsInteger": false,
         "DefaultValue": 4.8,
         "MinValue": 1.0,
         "MaxValue": 5.0
       },
       {
         "Code": "ASSI",
         "DisplayName": "ASSI",
         "IsInteger": false,
         "DefaultValue": 4.7,
         "MinValue": 1.0,
         "MaxValue": 5.0
       }
     ]
   }',
       'Настройки KPI по умолчанию',
       NOW()
    WHERE
NOT EXISTS (
  SELECT "Id" FROM "AWP_INFRA"."Configurations"
  WHERE "Name"='KpiThresholdsDefaultValueSetting'
);