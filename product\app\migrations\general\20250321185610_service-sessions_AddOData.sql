DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "SERVICE_SESSIONS"."__Migrations_ServiceSessionsDbContext" WHERE "MigrationId" = '20250321185610_AddOData') THEN
    
    CREATE OR REPLACE VIEW "CALC"."V_SERVICESESSIONS" AS
    WITH service_sessions_data AS (
        SELECT 
            ss."Id",
            ss."ServiceObjectTypeId",
            ss."ServiceObjectId",
            ss."RequestId",
            ss."ContactPersonId",
            ss."StartTime",
            ss."CloseTime" AS "EndTime",
            ss."Comment",
            ss."StartedBy",
            ss."ClosedBy",
            CASE 
                WHEN ss."CloseTime" IS NOT NULL 
                THEN EXTRACT(EPOCH FROM (ss."CloseTime" - ss."StartTime"))::integer 
                ELSE NULL 
            END AS "Duration"
        FROM 
            "SERVICE_SESSIONS"."ServiceSessions" ss
    )
    SELECT 
        ssd."Id",
        ssd."ServiceObjectTypeId",
        ssd."ServiceObjectId",
        ssd."RequestId",
        ssd."ContactPersonId",
        CONCAT_WS(' ', cp."LAST_NAME", cp."FIRST_NAME", cp."MIDDLE_NAME") AS "ContactPersonFullName",
        ssd."StartTime",
        ssd."EndTime",
        ssd."Comment",
        ssd."StartedBy",
        ssd."ClosedBy",
        op."FIO" AS "StartedByFullName",
        ssd."Duration"
    FROM 
        service_sessions_data ssd
    LEFT JOIN 
        "UCMM_DATA"."CONTACT_PERSON" cp ON ssd."ContactPersonId" = cp."ID"
    LEFT JOIN 
        "CALC"."INFRA_Operators_FIO" op ON ssd."StartedBy" = op."LOGIN";

    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "SERVICE_SESSIONS"."__Migrations_ServiceSessionsDbContext" WHERE "MigrationId" = '20250321185610_AddOData') THEN
    INSERT INTO "SERVICE_SESSIONS"."__Migrations_ServiceSessionsDbContext" ("MigrationId", "ProductVersion")
    VALUES ('20250321185610_AddOData', '9.0.2');
    END IF;
END $EF$;
