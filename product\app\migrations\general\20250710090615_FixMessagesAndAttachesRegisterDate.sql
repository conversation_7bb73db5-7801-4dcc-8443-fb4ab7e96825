-- "CALC"."V_SERVICESESSIONS" source

CREATE OR REPLACE VIEW "CALC"."V_SERVICESESSIONS"
AS WITH service_sessions_data AS (
         SELECT ss."Id",
            ss."ServiceObjectTypeId",
            ss."ServiceObjectId",
            ss."RequestId",
            ss."ContactPersonId",
            ss."StartTime",
            ss."CloseTime" AS "EndTime",
            ss."Comment",
            ss."StartedBy",
            ss."ClosedBy",
                CASE
                    WHEN ss."CloseTime" IS NOT NULL THEN EXTRACT(epoch FROM ss."CloseTime" - ss."StartTime")::integer
                    ELSE NULL::integer
                END AS "Duration",
            ( SELECT count(1) AS count
                   FROM "UCMM_DATA"."MESSAGE_EXTERNAL_SYSTEM_LINK" esl
                     JOIN "UCMM_DATA"."MESSAGE" m ON esl."MESSAGE_ID" = m."ID"
                  WHERE m."DIRECTION" = 1 AND m."TypeCode" <> 'Note'::text AND esl."EXTERNAL_ENTITY_ID" = ss."RequestId" AND m."TIME_REGISTERED" >= ss."StartTime" AND m."TIME_REGISTERED" <= COALESCE(ss."CloseTime", m."TIME_REGISTERED")) AS "TotalIncomingMessagesCount",
            ( SELECT count(1) AS count
                   FROM "UCMM_DATA"."MESSAGE_EXTERNAL_SYSTEM_LINK" esl
                     JOIN "UCMM_DATA"."MESSAGE" m ON esl."MESSAGE_ID" = m."ID"
                  WHERE m."DIRECTION" = 2 AND m."TypeCode" <> 'Note'::text AND esl."EXTERNAL_ENTITY_ID" = ss."RequestId" AND m."TIME_REGISTERED" >= ss."StartTime" AND m."TIME_REGISTERED" <= COALESCE(ss."CloseTime", m."TIME_REGISTERED")) AS "TotalOutgoingMessagesCount",
            ( SELECT count(1) AS count
                   FROM "UCMM_DATA"."MESSAGE_EXTERNAL_SYSTEM_LINK" esl
                     JOIN "UCMM_DATA"."ATTACHMENTS" a ON esl."MESSAGE_ID" = a."MessageId"
                     JOIN "UCMM_DATA"."MESSAGE" m ON a."MessageId" = m."ID"
                  WHERE esl."EXTERNAL_ENTITY_ID" = ss."RequestId"  AND m."TIME_REGISTERED" >= ss."StartTime" AND m."TIME_REGISTERED" <= COALESCE(ss."CloseTime", m."TIME_REGISTERED")) AS "TotalAttachmentsCount"
           FROM "SERVICE_SESSIONS"."ServiceSessions" ss
        )
SELECT ssd."Id",
       ssd."ServiceObjectTypeId",
       ssd."ServiceObjectId",
       ssd."RequestId",
       ssd."ContactPersonId",
       concat_ws(' '::text, cp."LAST_NAME", cp."FIRST_NAME", cp."MIDDLE_NAME") AS "ContactPersonFullName",
       ssd."StartTime",
       ssd."EndTime",
       ssd."Comment",
       ssd."StartedBy",
       ssd."ClosedBy",
       op."FIO" AS "StartedByFullName",
       ssd."Duration",
       ssd."TotalIncomingMessagesCount",
       ssd."TotalOutgoingMessagesCount",
       ssd."TotalAttachmentsCount",
       req."Channel" AS "RequestChannel"
FROM service_sessions_data ssd
         LEFT JOIN "UCMM_DATA"."CONTACT_PERSON" cp ON ssd."ContactPersonId" = cp."ID"
         LEFT JOIN "CALC"."INFRA_Operators_FIO" op ON ssd."StartedBy" = op."LOGIN"::text
     LEFT JOIN "CRPM_DATA"."Requests" req ON ssd."RequestId" = req."Id";

INSERT INTO "SERVICE_SESSIONS"."__Migrations_ServiceSessionsDbContext" ("MigrationId", "ProductVersion")
VALUES ('20250710090615_FixMessagesAndAttachesRegisterDate', '9.0.6');
