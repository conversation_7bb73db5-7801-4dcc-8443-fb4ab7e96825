-- https://yt.avelana.ru/issue/AVELANA-1747/Analiz-i-optimizaciya-zap<PERSON>ov-ot-ARM-ot-neaktivnyh-prilozhenij#focus=Change-4-11173.0-0
UPDATE "AWP_INFRA"."WebClientAppInits" SET "ComponentInit" = '{"isLazy": true}' WHERE "ComponentName"='ServiceObjects';
UPDATE "AWP_INFRA"."WebClientAppInits" SET "ComponentInit" = '{"isLazy": true}' WHERE "ComponentName"='Clients';
UPDATE "AWP_INFRA"."WebClientAppInits" SET "ComponentInit" = '{"isLazy": true}' WHERE "ComponentName"='RequestsAwaiting';
UPDATE "AWP_INFRA"."WebClientAppInits" SET "ComponentInit" = '{"isLazy": true}' WHERE "ComponentName"='Administrator';