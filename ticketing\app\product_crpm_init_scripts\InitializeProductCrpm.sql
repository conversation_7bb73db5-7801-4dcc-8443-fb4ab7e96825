DO $EF$

DECLARE operatorProcessingStateId smallint;
DECLARE waitForOperatorStatusId smallint;
DECLARE mainSchemeId smallint;
DECLARE operatorProcessingRequestStateWorkId smallint;
DECLARE serviceWorkExecutionManagerId smallint;

DECLARE waitAnswersMaxDurationId smallint;
DECLARE waitAnswersStateId smallint;
DECLARE waitAnswersStatusId smallint;
DECLARE returnToOperatorIfTicketsHasErrorsWorkExecutorId smallint;
DECLARE returnToOperatorIfTicketsHasErrorsRequestStateWorkId smallint;

BEGIN

--	основные ббазовые идентификаторы
operatorProcessingStateId := (select "Id" from "CRPM_CFG"."RequestStates" where "Code" = N'OPERATOR.PROCESSING');
waitForOperatorStatusId := (select "Id" from "CRPM_CFG"."RequestStateStatuses" where "Code" = N'OPERATOR.WAIT');
mainSchemeId := (select "SchemeId" from "CRPM_CFG"."RequestStates" where "Id" = operatorProcessingStateId);
operatorProcessingRequestStateWorkId := (select "Id" from "CRPM_CFG"."RequestStateWorks" where "RequestStateId" = operatorProcessingStateId and "ProvideExecutorToRequest" = true);
serviceWorkExecutionManagerId := (select "Id" from "CRPM_CFG"."WorkExecutionManagers" where "TypeCode" = N'ServiceWorkExecutionManager');

--	вставка стэйт-статуса под тикетинг
INSERT INTO "CRPM_CFG"."MaxDurations"(
	"Duration", "TargetStatusId", "RowVersion")
	VALUES (259200, waitForOperatorStatusId, 638155200103208599);
waitAnswersMaxDurationId := (select "Id" from "CRPM_CFG"."MaxDurations" where "RowVersion" = 638155200103208599 AND "TargetStatusId" = waitForOperatorStatusId);

INSERT INTO "CRPM_CFG"."RequestStates"(
	"Title", "SchemeId", "IsDefault", "IsTerminating", "MaxDurationId", "Code")
	VALUES (N'Ожидание ответов по задачам', mainSchemeId, false, false, waitAnswersMaxDurationId, N'TICKETING.WAITANSWERS');
waitAnswersStateId := (select "Id" from "CRPM_CFG"."RequestStates" where "Code" = N'TICKETING.WAITANSWERS');

INSERT INTO "CRPM_CFG"."RequestStateStatuses"(
	"Title", "StateId", "Code")
	VALUES (N'Ожидание ответов по задачам', waitAnswersStateId, N'TICKETING.WAITANSWERS');
waitAnswersStatusId := (select "Id" from "CRPM_CFG"."RequestStateStatuses" where "Code" = N'TICKETING.WAITANSWERS');

INSERT INTO "CRPM_CFG"."AvailableTargetStates"(
	"StateId", "TargetStatusId", "StatusId")
	VALUES (waitAnswersStateId, waitForOperatorStatusId, waitAnswersStatusId);


--	вставка работы под тикетинг
INSERT INTO "CRPM_CFG"."WorkExecutors"(
	"RequestStreamId", "TypeCode", "IsEnabled", "Title", "Configuration")
	VALUES (1, N'Product.Ticketing.ReturnToOperatorIfTicketsHasErrors', true, N'Тикеты. Возврат оператору, если есть задачи с ошибками',
	N'<?xml version="1.0" encoding="utf-16"?>
<Config TicketFailedStatusCodes="Failed.SEND_ERROR, SendError" StatusCodeToSwitchTo="OPERATOR.WAIT" AssignToOperator="true" AssignToOperatorTimeout="PT2H" />');
returnToOperatorIfTicketsHasErrorsWorkExecutorId := (select "Id" from "CRPM_CFG"."WorkExecutors" where "TypeCode" = N'Product.Ticketing.ReturnToOperatorIfTicketsHasErrors');

INSERT INTO "CRPM_CFG"."RequestStateWorks"(
	"Title", "RequestStateId", "Order", "ProvideExecutorToRequest", "WorkExecutorId", "GenerateEvents", "Enabled")
	VALUES (N'Возврат оператору, если есть задачи с ошибками', waitAnswersStateId, 0, false, returnToOperatorIfTicketsHasErrorsWorkExecutorId, false, true);
returnToOperatorIfTicketsHasErrorsRequestStateWorkId := (select "Id" from "CRPM_CFG"."RequestStateWorks" where "RequestStateId" = waitAnswersStateId);

INSERT INTO "CRPM_CFG"."WorkExecutionConfigurations"(
	"WorkExecutionManagerId", "WorkExecutorId", "Count")
	VALUES (serviceWorkExecutionManagerId, returnToOperatorIfTicketsHasErrorsWorkExecutorId, 100);


--	настройка кодов результатов работ
INSERT INTO "CRPM_CFG"."WorkSuccessCodes"(
	"WorkId", "Code", "TargetStatusId")
	VALUES (operatorProcessingRequestStateWorkId, N'TICKETING.WAITANSWERS', waitAnswersStatusId);

INSERT INTO "CRPM_CFG"."WorkSuccessCodes"(
	"WorkId", "Code", "TargetStatusId")
	VALUES (returnToOperatorIfTicketsHasErrorsRequestStateWorkId, N'NOT_USED_TicketsHasErrors', waitForOperatorStatusId);

	
--	вставка ивент-хэндлеров под тикетинг
INSERT INTO "CRPM_CFG"."RequestEventHandlers"(
	"Title", "TypeCode", "IsEnabled", "EventType", "SchemeId", "Configuration")
	VALUES (N'Тикеты. Обновление количеств тикетов (новый тикет)', N'Product.Ticketing.SetTicketsCa', true, N'Ticketing.TicketRegistered', mainSchemeId, N'<?xml version="1.0" encoding="utf-16"?>
<Config />');
INSERT INTO "CRPM_CFG"."RequestEventHandlers"(
	"Title", "TypeCode", "IsEnabled", "EventType", "SchemeId", "Configuration")
	VALUES (N'Тикеты. Обновление количеств тикетов (изменение статуса тикета)', N'Product.Ticketing.SetTicketsCa', true, N'Ticketing.TicketStateStatusChanged', mainSchemeId, N'<?xml version="1.0" encoding="utf-16"?>
<Config TicketAnswerReceivedStateCode="AnswerReceived" />');
INSERT INTO "CRPM_CFG"."RequestEventHandlers"(
	"Title", "TypeCode", "IsEnabled", "EventType", "StatusId", "Configuration")
	VALUES (N'Тикеты. Ответ получен: возврат обращения на обработку оператором', N'Product.Ticketing.ReturnToOperator.OnAnswerReceived', true, N'Ticketing.TicketAnswerReceived', waitAnswersStatusId, N'<?xml version="1.0" encoding="utf-16"?>
<Config StatusCodeToSwitchTo="OPERATOR.WAIT" TicketAnswerReceivedStatusCodes="AnswerReceived,Failed.SEND_ERROR,Closed" />');
INSERT INTO "CRPM_CFG"."RequestEventHandlers"(
	"Title", "TypeCode", "IsEnabled", "EventType", "StatusId", "Configuration")
	VALUES (N'Тикеты. Ошибка отправки: возврат обращения на обработку оператором', N'Product.Ticketing.ReturnToOperator.OnSendFailed', true, N'Ticketing.TicketSendFailed', waitAnswersStatusId, N'<?xml version="1.0" encoding="utf-16"?>
<Config StatusCodeToSwitchTo="OPERATOR.WAIT" />');

END $EF$;
