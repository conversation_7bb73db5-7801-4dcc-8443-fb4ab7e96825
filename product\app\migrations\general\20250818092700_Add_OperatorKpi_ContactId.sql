DO $EF$
BEGIN
	-- добавляем столбец "ActiveStateExecutionId"
	IF NOT EXISTS(SELECT 1 FROM "CRPM_DATA"."__EFMigrationsHistory" WHERE "MigrationId" = '20250815110303_AddActiveStateExecutionId') THEN
		ALTER TABLE "CRPM_DATA"."Works" ADD "ActiveStateExecutionId" uuid;

		INSERT INTO "CRPM_DATA"."__EFMigrationsHistory" ("MigrationId", "ProductVersion")
		VALUES ('20250815110303_AddActiveStateExecutionId', '9.0.1');
	END IF;

	-- изменения в "KPI"."OPERATOR_KPI"
	IF NOT EXISTS(SELECT 1 FROM "KPI"."__KpiServiceDbContextMigrations" WHERE "MigrationId" = '20250814113507_AddContactId') THEN
		ALTER TABLE "KPI"."OPERATOR_KPI" ALTER COLUMN "ContactStartTime" DROP NOT NULL;

		ALTER TABLE "KPI"."OPERATOR_KPI" ADD "ContactId" uuid;

		INSERT INTO "KPI"."__KpiServiceDbContextMigrations" ("MigrationId", "ProductVersion")
		VALUES ('20250814113507_AddContactId', '9.0.3');
	END IF;
END $EF$;