DO $EF$
BEGIN

	ALTER TABLE IF EXISTS "CALC"."BULK_REQUESTSVIEW"
		ADD COLUMN IF NOT EXISTS "ExecutorCuratorFio" character varying(2000);
			
	COMMENT ON COLUMN "CALC"."BULK_REQUESTSVIEW"."ExecutorCuratorFio"
		IS 'ФИО куратора оператора из столбца Executor';
	
	ALTER TABLE IF EXISTS "CALC"."BULK_REQUESTSVIEW"
		ADD COLUMN IF NOT EXISTS "ExecutorCuratorId" uuid;

	COMMENT ON COLUMN "CALC"."BULK_REQUESTSVIEW"."ExecutorCuratorId"
		IS 'ID куратора оператора из столбца Executor';
	    

	CREATE OR REPLACE TRIGGER "T_CA_GUID_CHANGED" BEFORE
	INSERT
		OR
	UPDATE
		ON
		"CRPM_DATA"."REQUEST_CA_GUID" FOR each ROW EXECUTE FUNCTION "CRPM_DATA"."T_REQ_CHANGED"();
		
	CREATE OR REPLACE TRIGGER "T_CA_GUID_DELETED" BEFORE
	DELETE
		ON
		"CRPM_DATA"."REQUEST_CA_GUID" FOR each ROW EXECUTE FUNCTION "CRPM_DATA"."T_REQ_DELETED"();

CREATE OR REPLACE VIEW "CALC"."REQUESTSVIEW"
AS SELECT "Id",
          COALESCE(( SELECT kpi."RequestType"::integer AS "RequestType"
                     FROM "KPI"."REQUEST_KPI" kpi
                     WHERE kpi."Id" = r."Id"), 0) AS "IsOutgoingRequest",
          "ExternalId",
          date_part('epoch'::text,
                    CASE
                        WHEN "TimeClosed" IS NULL THEN timezone('utc'::text, now())::timestamp with time zone
            ELSE "TimeClosed"::timestamp with time zone
        END - "TimeCreated"::timestamp with time zone)::numeric(20,0) AS "RequestLifeTime",
              "TimeCreated",
          "TimeRegistered",
          "TimeLastStateChanged",
          COALESCE(date_part('epoch'::text, COALESCE(( SELECT kpi."TimeOperatorFirstDistribution"::timestamp with time zone AS "TimeOperatorFirstDistribution"
                                                     FROM "KPI"."REQUEST_KPI" kpi
                                                     WHERE kpi."Id" = r."Id"),
        CASE
            WHEN "TimeClosed" IS NULL THEN timezone('utc'::text, now())::timestamp with time zone
            ELSE "TimeClosed"::timestamp with time zone
        END) - COALESCE(( SELECT kpi."TimeBotWorkEnded"::timestamp with time zone AS "TimeBotWorkEnded"
                        FROM "KPI"."REQUEST_KPI" kpi
                        WHERE kpi."Id" = r."Id"), "TimeCreated"::timestamp with time zone)), 0::double precision)::numeric(20,0) AS "InQueueTime",
              "StatusModifiedBy",
          "TimeClosed",
          "Title",
          "Description",
          "BranchId",
          "Priority",
          "State",
          "Status",
          CASE
              WHEN "StatusTime" IS NOT NULL THEN date_part('epoch'::text, timezone('utc'::text, now()) - "StatusTime")::numeric(20,0)
            ELSE 0::numeric
END AS "StatusTime",
    "AssignedToId",
    "ExecutorId",
    "SchemaId",
    "Source",
    "Channel",
    "QueueId",
    "QueueTitle",
    "PlainText",
    "Originator",
    "ParentId",
    "StatusName",
    "ServiceAreaName",
    "UtcOffset",
    "LifeTime",
    "AssignedToName",
    "ExecutorName",
    "TotalAttachments",
    "TotalIncomingMessages",
    "TotalOutgoingMessages",
    "AssignedToLastTime",
    "ForwardNote",
    "CustomerDatabaseType",
    "MSISDN",
    COALESCE(
        CASE
            WHEN "TimeClosed" IS NULL AND (( SELECT max("OPERATOR_KPI"."ContactStartTime") AS "ContactStartTime"
               FROM "KPI"."OPERATOR_KPI"
              WHERE "OPERATOR_KPI"."ContactEndTime" IS NULL AND "OPERATOR_KPI"."RequestId" = r."Id"
              GROUP BY "OPERATOR_KPI"."RequestId")) IS NOT NULL THEN (( SELECT kpi."HT"::double precision AS "HT"
               FROM "KPI"."REQUEST_KPI" kpi
              WHERE kpi."Id" = r."Id")) + date_part('epoch'::text, timezone('utc'::text, now())::timestamp with time zone - (( SELECT max("OPERATOR_KPI"."ContactStartTime") AS "ContactStartTime"
               FROM "KPI"."OPERATOR_KPI"
              WHERE "OPERATOR_KPI"."ContactEndTime" IS NULL AND "OPERATOR_KPI"."RequestId" = r."Id"
              GROUP BY "OPERATOR_KPI"."RequestId"))::timestamp with time zone)
            ELSE ( SELECT kpi."HT"::double precision AS "HT"
               FROM "KPI"."REQUEST_KPI" kpi
              WHERE kpi."Id" = r."Id")
        END, 0::double precision)::numeric(20,0) AS "HT",
    COALESCE(
        CASE
            WHEN (( SELECT kpi."TimeOperatorFirstResponse"
               FROM "KPI"."REQUEST_KPI" kpi
              WHERE kpi."Id" = r."Id")) IS NULL AND "TimeClosed" IS NULL AND (( SELECT kpi."TimeCreated"
               FROM "KPI"."REQUEST_KPI" kpi
              WHERE kpi."Id" = r."Id")) IS NOT NULL THEN
            CASE
                WHEN ("Status" IN ( SELECT rss."Id"
                   FROM "CRPM_CFG"."RequestStateStatuses" rss
                  WHERE rss."Code"::text = ANY (ARRAY['CHATBOT'::text, 'CHATBOT.WAIT'::text, 'AUTOPROCESSING'::text]))) THEN ( SELECT kpi."SA"::double precision AS "SA"
                   FROM "KPI"."REQUEST_KPI" kpi
                  WHERE kpi."Id" = r."Id")
                WHEN (( SELECT kpi."TimeBotWorkEnded"
                   FROM "KPI"."REQUEST_KPI" kpi
                  WHERE kpi."Id" = r."Id")) IS NOT NULL THEN date_part('epoch'::text, timezone('utc'::text, now())::timestamp with time zone - (( SELECT kpi."TimeBotWorkEnded"::timestamp with time zone AS "TimeBotWorkEnded"
                   FROM "KPI"."REQUEST_KPI" kpi
                  WHERE kpi."Id" = r."Id")))
                ELSE date_part('epoch'::text, timezone('utc'::text, now())::timestamp with time zone - (( SELECT kpi."TimeCreated"::timestamp with time zone AS "TimeCreated"
                   FROM "KPI"."REQUEST_KPI" kpi
                  WHERE kpi."Id" = r."Id")))
            END
            ELSE ( SELECT kpi."SA"::double precision AS "SA"
               FROM "KPI"."REQUEST_KPI" kpi
              WHERE kpi."Id" = r."Id")
        END, 0::double precision)::numeric(20,0) AS "SA",
    COALESCE(
        CASE
            WHEN "TimeClosed" IS NULL THEN date_part('epoch'::text, timezone('utc'::text, now())::timestamp with time zone - COALESCE(( SELECT kpi."TimeBotWorkEnded"
               FROM "KPI"."REQUEST_KPI" kpi
              WHERE kpi."Id" = r."Id"), ( SELECT kpi."TimeCreated"
               FROM "KPI"."REQUEST_KPI" kpi
              WHERE kpi."Id" = r."Id"))::timestamp with time zone) -
            CASE
                WHEN "TimeClosed" IS NULL AND (( SELECT max("OPERATOR_KPI"."ContactStartTime") AS "ContactStartTime"
                   FROM "KPI"."OPERATOR_KPI"
                  WHERE "OPERATOR_KPI"."ContactEndTime" IS NULL AND "OPERATOR_KPI"."RequestId" = r."Id"
                  GROUP BY "OPERATOR_KPI"."RequestId")) IS NOT NULL THEN (( SELECT kpi."HT"::double precision AS "HT"
                   FROM "KPI"."REQUEST_KPI" kpi
                  WHERE kpi."Id" = r."Id")) + date_part('epoch'::text, timezone('utc'::text, now())::timestamp with time zone - (( SELECT max("OPERATOR_KPI"."ContactStartTime") AS "ContactStartTime"
                   FROM "KPI"."OPERATOR_KPI"
                  WHERE "OPERATOR_KPI"."ContactEndTime" IS NULL AND "OPERATOR_KPI"."RequestId" = r."Id"
                  GROUP BY "OPERATOR_KPI"."RequestId"))::timestamp with time zone)
                ELSE ( SELECT kpi."HT"::double precision AS "HT"
                   FROM "KPI"."REQUEST_KPI" kpi
                  WHERE kpi."Id" = r."Id")
            END - (( SELECT kpi."ClosedTimeBeforeLastClosing"::double precision AS "ClosedTimeBeforeLastClosing"
               FROM "KPI"."REQUEST_KPI" kpi
              WHERE kpi."Id" = r."Id"))
            ELSE ( SELECT kpi."WT"::double precision AS "WT"
               FROM "KPI"."REQUEST_KPI" kpi
              WHERE kpi."Id" = r."Id")
        END, 0::double precision)::numeric(20,0) AS "WT",
    (( SELECT resp."RT"
           FROM "KPI"."OPERATOR_RESPONSE" resp
          WHERE resp."RequestId" = r."Id"
          ORDER BY resp."ReactionStartDate"
         LIMIT 1))::numeric(20,0) AS "FirstResponsePrepareTime",
    COALESCE((( SELECT sum(callstate."ACW") AS sum
           FROM "KPI"."OPERATOR_CALL_STATE" callstate
          WHERE callstate."RequestId" = r."Id"))::double precision, 0::double precision)::numeric(20,0) AS "CW",
    "RequestType",
    COALESCE("LOST_SMS_FLAG", 0::smallint) AS "LOST_SMS_FLAG",
    "RegionName",
    "ContactPersonID",
    "ContactPersonName",
    "PostponeTime",
    "SupervisorScore",
    ( SELECT kpi."CSI"
           FROM "KPI"."REQUEST_KPI" kpi
          WHERE kpi."Id" = r."Id") AS "CSI",
    "SupervisorScore" AS "SSI",
    0 AS "SCSI",
    "TargetResponseTime",
    "StringValue35",
    "StringValue36",
    "StringValue37",
    "StringValue38",
    "StringValue39",
    "StringValue40",
    "StringValue41",
    "StringValue42",
    "StringValue43",
    "StringValue44",
    "StringValue45",
    "StringValue46",
    "StringValue47",
    "StringValue48",
    "StringValue49",
    "StringValue50",
    "StatusModifiedByLogin",
    (( SELECT sum(callstate."ACW") AS sum
           FROM "KPI"."OPERATOR_CALL_STATE" callstate
          WHERE callstate."RequestId" = r."Id"))::integer AS "ACW",
    "RequestOverdue",
    "RepeatedRequestId",
    "TargetDecisionTime",
    "RequestDecisionOverdue",
    "TicketsTotalCount",
    "TicketsRequiredProcessingCount",
    "TicketsProcessingRequiredAnswerReceivedCount",
    "GuidValueCode10",
    "GuidValueCode11",
    "GuidValueCode12",
    "GuidValueCode13",
    "GuidValueCode14",
    "GuidValueCode15",
    "GuidValueCode16",
    "GuidValueCode17",
    "GuidValueCode18",
    "GuidValueCode19",
    "GuidValueCode20",
    "GuidValueCode21",
    "GuidValueCode22",
    "GuidValueCode23",
    "GuidValueCode24",
    "GuidValueCode25",
    "GuidValueCode26",
    "GuidValueCode27",
    "GuidValueCode28",
    "GuidValueCode29",
    "GuidValueCode30",
    "GuidValueCode31",
    "GuidValueCode32",
    "GuidValueCode33",
    "GuidValueCode34",
    "GuidValueCode35",
    "GuidValueCode36",
    "GuidValueCode37",
    "GuidValueCode38",
    "GuidValueCode39",
    "GuidValueCode40",
    "GuidValueCode41",
    "GuidValueCode42",
    "GuidValueCode43",
    "GuidValueCode44",
    "GuidValueCode45",
    "GuidValueCode46",
    "GuidValueCode47",
    "GuidValueCode48",
    "GuidValueCode49",
    "GuidValueCode50",
    "GuidValueName10",
    "GuidValueName11",
    "GuidValueName12",
    "GuidValueName13",
    "GuidValueName14",
    "GuidValueName15",
    "GuidValueName16",
    "GuidValueName17",
    "GuidValueName18",
    "GuidValueName19",
    "GuidValueName20",
    "GuidValueName21",
    "GuidValueName22",
    "GuidValueName23",
    "GuidValueName24",
    "GuidValueName25",
    "GuidValueName26",
    "GuidValueName27",
    "GuidValueName28",
    "GuidValueName29",
    "GuidValueName30",
    "GuidValueName31",
    "GuidValueName32",
    "GuidValueName33",
    "GuidValueName34",
    "GuidValueName35",
    "GuidValueName36",
    "GuidValueName37",
    "GuidValueName38",
    "GuidValueName39",
    "GuidValueName40",
    "GuidValueName41",
    "GuidValueName42",
    "GuidValueName43",
    "GuidValueName44",
    "GuidValueName45",
    "GuidValueName46",
    "GuidValueName47",
    "GuidValueName48",
    "GuidValueName49",
    "GuidValueName50",
    "ClientId",
    "StringValue51",
    "StringValue52",
    "StringValue53",
    "StringValue54",
    "StringValue55",
    "StringValue56",
    "StringValue57",
    "StringValue58",
    "StringValue59",
    "StringValue60",
    "StringValue61",
    "StringValue62",
    "StringValue63",
    "StringValue64",
    "StringValue65",
    "StringValue66",
    "StringValue67",
    "StringValue68",
    "StringValue69",
    "StringValue70",
    "StringValue71",
    "StringValue72",
    "StringValue73",
    "StringValue74",
    "StringValue75",
    "StringValue76",
    "StringValue77",
    "StringValue78",
    "StringValue79",
    "StringValue80",
    "StringValue81",
    "StringValue82",
    "StringValue83",
    "StringValue84",
    "StringValue85",
    "StringValue86",
    "StringValue87",
    "StringValue88",
    "StringValue89",
    "StringValue90",
    "StringValue91",
    "StringValue92",
    "StringValue93",
    "StringValue94",
    "StringValue95",
    "StringValue96",
    "StringValue97",
    "StringValue98",
    "StringValue99",
    "StringValue100",
    "GuidValueCode51",
    "GuidValueCode52",
    "GuidValueCode53",
    "GuidValueCode54",
    "GuidValueCode55",
    "GuidValueCode56",
    "GuidValueCode57",
    "GuidValueCode58",
    "GuidValueCode59",
    "GuidValueCode60",
    "GuidValueCode61",
    "GuidValueCode62",
    "GuidValueCode63",
    "GuidValueCode64",
    "GuidValueCode65",
    "GuidValueCode66",
    "GuidValueCode67",
    "GuidValueCode68",
    "GuidValueCode69",
    "GuidValueCode70",
    "GuidValueCode71",
    "GuidValueCode72",
    "GuidValueCode73",
    "GuidValueCode74",
    "GuidValueCode75",
    "GuidValueCode76",
    "GuidValueCode77",
    "GuidValueCode78",
    "GuidValueCode79",
    "GuidValueCode80",
    "GuidValueCode81",
    "GuidValueCode82",
    "GuidValueCode83",
    "GuidValueCode84",
    "GuidValueCode85",
    "GuidValueCode86",
    "GuidValueCode87",
    "GuidValueCode88",
    "GuidValueCode89",
    "GuidValueCode90",
    "GuidValueCode91",
    "GuidValueCode92",
    "GuidValueCode93",
    "GuidValueCode94",
    "GuidValueCode95",
    "GuidValueCode96",
    "GuidValueCode97",
    "GuidValueCode98",
    "GuidValueCode99",
    "GuidValueCode100",
    "GuidValueName51",
    "GuidValueName52",
    "GuidValueName53",
    "GuidValueName54",
    "GuidValueName55",
    "GuidValueName56",
    "GuidValueName57",
    "GuidValueName58",
    "GuidValueName59",
    "GuidValueName60",
    "GuidValueName61",
    "GuidValueName62",
    "GuidValueName63",
    "GuidValueName64",
    "GuidValueName65",
    "GuidValueName66",
    "GuidValueName67",
    "GuidValueName68",
    "GuidValueName69",
    "GuidValueName70",
    "GuidValueName71",
    "GuidValueName72",
    "GuidValueName73",
    "GuidValueName74",
    "GuidValueName75",
    "GuidValueName76",
    "GuidValueName77",
    "GuidValueName78",
    "GuidValueName79",
    "GuidValueName80",
    "GuidValueName81",
    "GuidValueName82",
    "GuidValueName83",
    "GuidValueName84",
    "GuidValueName85",
    "GuidValueName86",
    "GuidValueName87",
    "GuidValueName88",
    "GuidValueName89",
    "GuidValueName90",
    "GuidValueName91",
    "GuidValueName92",
    "GuidValueName93",
    "GuidValueName94",
    "GuidValueName95",
    "GuidValueName96",
    "GuidValueName97",
    "GuidValueName98",
    "GuidValueName99",
    "GuidValueName100",
    "DateTimeValue1",
    "DateTimeValue2",
    "DateTimeValue3",
    "DateTimeValue4",
    "DateTimeValue5",
    "DateTimeValue6",
    "DateTimeValue7",
    "DateTimeValue8",
    "DateTimeValue9",
    "DateTimeValue10",
    "DateTimeValue11",
    "DateTimeValue12",
    "DateTimeValue13",
    "DateTimeValue14",
    "DateTimeValue15",
    "DateTimeValue16",
    "DateTimeValue17",
    "DateTimeValue18",
    "DateTimeValue19",
    "DateTimeValue20",
    "DateTimeValue21",
    "DateTimeValue22",
    "DateTimeValue23",
    "DateTimeValue24",
    "DateTimeValue25",
    "DateTimeValue26",
    "DateTimeValue27",
    "DateTimeValue28",
    "DateTimeValue29",
    "DateTimeValue30",
    "DateTimeValue31",
    "DateTimeValue32",
    "DateTimeValue33",
    "DateTimeValue34",
    "DateTimeValue35",
    "DateTimeValue36",
    "DateTimeValue37",
    "DateTimeValue38",
    "DateTimeValue39",
    "DateTimeValue40",
    "DateTimeValue41",
    "DateTimeValue42",
    "DateTimeValue43",
    "DateTimeValue44",
    "DateTimeValue45",
    "DateTimeValue46",
    "DateTimeValue47",
    "DateTimeValue48",
    "DateTimeValue49",
    "DateTimeValue50",
    "DateTimeValue51",
    "DateTimeValue52",
    "DateTimeValue53",
    "DateTimeValue54",
    "DateTimeValue55",
    "DateTimeValue56",
    "DateTimeValue57",
    "DateTimeValue58",
    "DateTimeValue59",
    "DateTimeValue60",
    "DateTimeValue61",
    "DateTimeValue62",
    "DateTimeValue63",
    "DateTimeValue64",
    "DateTimeValue65",
    "DateTimeValue66",
    "DateTimeValue67",
    "DateTimeValue68",
    "DateTimeValue69",
    "DateTimeValue70",
    "DateTimeValue71",
    "DateTimeValue72",
    "DateTimeValue73",
    "DateTimeValue74",
    "DateTimeValue75",
    "DateTimeValue76",
    "DateTimeValue77",
    "DateTimeValue78",
    "DateTimeValue79",
    "DateTimeValue80",
    "DateTimeValue81",
    "DateTimeValue82",
    "DateTimeValue83",
    "DateTimeValue84",
    "DateTimeValue85",
    "DateTimeValue86",
    "DateTimeValue87",
    "DateTimeValue88",
    "DateTimeValue89",
    "DateTimeValue90",
    "DateTimeValue91",
    "DateTimeValue92",
    "DateTimeValue93",
    "DateTimeValue94",
    "DateTimeValue95",
    "DateTimeValue96",
    "DateTimeValue97",
    "DateTimeValue98",
    "DateTimeValue99",
    "DateTimeValue100",
    "LongValue1",
    "LongValue2",
    "LongValue3",
    "LongValue4",
    "LongValue5",
    "LongValue6",
    "LongValue7",
    "LongValue8",
    "LongValue9",
    "LongValue10",
    "LongValue11",
    "LongValue12",
    "LongValue13",
    "LongValue14",
    "LongValue15",
    "LongValue16",
    "LongValue17",
    "LongValue18",
    "LongValue19",
    "LongValue20",
    "LongValue21",
    "LongValue22",
    "LongValue23",
    "LongValue24",
    "LongValue25",
    "LongValue26",
    "LongValue27",
    "LongValue28",
    "LongValue29",
    "LongValue30",
    "LongValue31",
    "LongValue32",
    "LongValue33",
    "LongValue34",
    "LongValue35",
    "LongValue36",
    "LongValue37",
    "LongValue38",
    "LongValue39",
    "LongValue40",
    "LongValue41",
    "LongValue42",
    "LongValue43",
    "LongValue44",
    "LongValue45",
    "LongValue46",
    "LongValue47",
    "LongValue48",
    "LongValue49",
    "LongValue50",
    "LongValue51",
    "LongValue52",
    "LongValue53",
    "LongValue54",
    "LongValue55",
    "LongValue56",
    "LongValue57",
    "LongValue58",
    "LongValue59",
    "LongValue60",
    "LongValue61",
    "LongValue62",
    "LongValue63",
    "LongValue64",
    "LongValue65",
    "LongValue66",
    "LongValue67",
    "LongValue68",
    "LongValue69",
    "LongValue70",
    "LongValue71",
    "LongValue72",
    "LongValue73",
    "LongValue74",
    "LongValue75",
    "LongValue76",
    "LongValue77",
    "LongValue78",
    "LongValue79",
    "LongValue80",
    "LongValue81",
    "LongValue82",
    "LongValue83",
    "LongValue84",
    "LongValue85",
    "LongValue86",
    "LongValue87",
    "LongValue88",
    "LongValue89",
    "LongValue90",
    "LongValue91",
    "LongValue92",
    "LongValue93",
    "LongValue94",
    "LongValue95",
    "LongValue96",
    "LongValue97",
    "LongValue98",
    "LongValue99",
    "LongValue100",
    "OperatorRootGroupName",
    "OperatorRootGroupId",
    "ContactPersonOrganization",
    "ContactPersonNameOrOrganization",
    "ContactPersonType",
    "ReturnsFromRatingBotCount",
    "ExecutorCuratorId",
    "ExecutorCuratorFio"
   FROM "CALC"."BULK_REQUESTSVIEW" r;

CREATE OR REPLACE VIEW "CALC"."REQUESTSVIEW_UPLOAD"
AS SELECT r."Id",
          COALESCE(kpi."RequestType"::integer, 0) AS "IsOutgoingRequest",
          r."ExternalId",
          date_part('epoch'::text,
                    CASE
                        WHEN r."TimeClosed" IS NULL THEN timezone('utc'::text, now())::timestamp with time zone
            ELSE r."TimeClosed"::timestamp with time zone
        END - r."TimeCreated"::timestamp with time zone)::numeric(20,0) AS "RequestLifeTime",
              r."TimeCreated",
          r."TimeRegistered",
          r."TimeLastStateChanged",
          COALESCE(date_part('epoch'::text, COALESCE(kpi."TimeOperatorFirstDistribution"::timestamp with time zone,
                                                     CASE
                                                         WHEN r."TimeClosed" IS NULL THEN timezone('utc'::text, now())::timestamp with time zone
                                                     ELSE r."TimeClosed"::timestamp with time zone
                                                     END) - COALESCE(kpi."TimeBotWorkEnded"::timestamp with time zone, r."TimeCreated"::timestamp with time zone)), 0::double precision)::numeric(20,0) AS "InQueueTime",
              r."StatusModifiedBy",
          r."TimeClosed",
          r."Title",
          r."Description",
          r."BranchId",
          r."Priority",
          r."State",
          r."Status",
          CASE
              WHEN r."StatusTime" IS NOT NULL THEN date_part('epoch'::text, timezone('utc'::text, now()) - r."StatusTime")::numeric(20,0)
            ELSE 0::numeric
END AS "StatusTime",
    r."AssignedToId",
    r."ExecutorId",
    r."SchemaId",
    r."Source",
    r."Channel",
    r."QueueId",
    r."QueueTitle",
    r."PlainText",
    r."Originator",
    r."ParentId",
    r."StatusName",
    r."ServiceAreaName",
    r."UtcOffset",
    r."LifeTime",
    r."AssignedToName",
    r."ExecutorName",
    r."TotalAttachments",
    r."TotalIncomingMessages",
    r."TotalOutgoingMessages",
    r."AssignedToLastTime",
    r."ForwardNote",
    r."CustomerDatabaseType",
    r."MSISDN",
    COALESCE(
        CASE
            WHEN r."TimeClosed" IS NULL AND okpi."ContactStartTime" IS NOT NULL THEN kpi."HT"::double precision + date_part('epoch'::text, timezone('utc'::text, now())::timestamp with time zone - okpi."ContactStartTime"::timestamp with time zone)
            ELSE kpi."HT"::double precision
        END, 0::double precision)::numeric(20,0) AS "HT",
    COALESCE(
        CASE
            WHEN kpi."TimeOperatorFirstResponse" IS NULL AND r."TimeClosed" IS NULL AND kpi."TimeCreated" IS NOT NULL THEN
            CASE
                WHEN (r."Status" IN ( SELECT rss."Id"
                   FROM "CRPM_CFG"."RequestStateStatuses" rss
                  WHERE rss."Code"::text = ANY (ARRAY['CHATBOT'::text, 'CHATBOT.WAIT'::text, 'AUTOPROCESSING'::text]))) THEN kpi."SA"::double precision
                WHEN kpi."TimeBotWorkEnded" IS NOT NULL THEN date_part('epoch'::text, timezone('utc'::text, now())::timestamp with time zone - kpi."TimeBotWorkEnded"::timestamp with time zone)
                ELSE date_part('epoch'::text, timezone('utc'::text, now())::timestamp with time zone - kpi."TimeCreated"::timestamp with time zone)
            END
            ELSE kpi."SA"::double precision
        END, 0::double precision)::numeric(20,0) AS "SA",
    COALESCE(
        CASE
            WHEN r."TimeClosed" IS NULL THEN date_part('epoch'::text, timezone('utc'::text, now())::timestamp with time zone - COALESCE(kpi."TimeBotWorkEnded", kpi."TimeCreated")::timestamp with time zone) -
            CASE
                WHEN r."TimeClosed" IS NULL AND okpi."ContactStartTime" IS NOT NULL THEN kpi."HT"::double precision + date_part('epoch'::text, timezone('utc'::text, now())::timestamp with time zone - okpi."ContactStartTime"::timestamp with time zone)
                ELSE kpi."HT"::double precision
            END - kpi."ClosedTimeBeforeLastClosing"::double precision
            ELSE kpi."WT"::double precision
        END, 0::double precision)::numeric(20,0) AS "WT",
    (( SELECT resp."RT"
           FROM "KPI"."OPERATOR_RESPONSE" resp
          WHERE resp."RequestId" = r."Id"
          ORDER BY resp."ReactionStartDate"
         LIMIT 1))::numeric(20,0) AS "FirstResponsePrepareTime",
    COALESCE((( SELECT sum("OPERATOR_CALL_STATE"."ACW") AS sum
           FROM "KPI"."OPERATOR_CALL_STATE"
          WHERE "OPERATOR_CALL_STATE"."RequestId" = r."Id"))::double precision, 0::double precision)::numeric(20,0) AS "CW",
    r."RequestType",
    COALESCE(r."LOST_SMS_FLAG", 0::smallint) AS "LOST_SMS_FLAG",
    r."RegionName",
    r."ContactPersonID",
    r."ContactPersonName",
    r."PostponeTime",
    r."SupervisorScore",
    kpi."CSI",
    r."SupervisorScore" AS "SSI",
    0 AS "SCSI",
    r."TargetResponseTime",
    r."StringValue35",
    r."StringValue36",
    r."StringValue37",
    r."StringValue38",
    r."StringValue39",
    r."StringValue40",
    r."StringValue41",
    r."StringValue42",
    r."StringValue43",
    r."StringValue44",
    r."StringValue45",
    r."StringValue46",
    r."StringValue47",
    r."StringValue48",
    r."StringValue49",
    r."StringValue50",
    r."StatusModifiedByLogin",
    (( SELECT sum("OPERATOR_CALL_STATE"."ACW") AS sum
           FROM "KPI"."OPERATOR_CALL_STATE"
          WHERE "OPERATOR_CALL_STATE"."RequestId" = r."Id"))::integer AS "ACW",
    r."RequestOverdue",
    r."RepeatedRequestId",
    r."TargetDecisionTime",
    r."RequestDecisionOverdue",
    r."TicketsTotalCount",
    r."TicketsRequiredProcessingCount",
    r."TicketsProcessingRequiredAnswerReceivedCount",
    r."GuidValueCode10",
    r."GuidValueCode11",
    r."GuidValueCode12",
    r."GuidValueCode13",
    r."GuidValueCode14",
    r."GuidValueCode15",
    r."GuidValueCode16",
    r."GuidValueCode17",
    r."GuidValueCode18",
    r."GuidValueCode19",
    r."GuidValueCode20",
    r."GuidValueCode21",
    r."GuidValueCode22",
    r."GuidValueCode23",
    r."GuidValueCode24",
    r."GuidValueCode25",
    r."GuidValueCode26",
    r."GuidValueCode27",
    r."GuidValueCode28",
    r."GuidValueCode29",
    r."GuidValueCode30",
    r."GuidValueCode31",
    r."GuidValueCode32",
    r."GuidValueCode33",
    r."GuidValueCode34",
    r."GuidValueCode35",
    r."GuidValueCode36",
    r."GuidValueCode37",
    r."GuidValueCode38",
    r."GuidValueCode39",
    r."GuidValueCode40",
    r."GuidValueCode41",
    r."GuidValueCode42",
    r."GuidValueCode43",
    r."GuidValueCode44",
    r."GuidValueCode45",
    r."GuidValueCode46",
    r."GuidValueCode47",
    r."GuidValueCode48",
    r."GuidValueCode49",
    r."GuidValueCode50",
    r."GuidValueName10",
    r."GuidValueName11",
    r."GuidValueName12",
    r."GuidValueName13",
    r."GuidValueName14",
    r."GuidValueName15",
    r."GuidValueName16",
    r."GuidValueName17",
    r."GuidValueName18",
    r."GuidValueName19",
    r."GuidValueName20",
    r."GuidValueName21",
    r."GuidValueName22",
    r."GuidValueName23",
    r."GuidValueName24",
    r."GuidValueName25",
    r."GuidValueName26",
    r."GuidValueName27",
    r."GuidValueName28",
    r."GuidValueName29",
    r."GuidValueName30",
    r."GuidValueName31",
    r."GuidValueName32",
    r."GuidValueName33",
    r."GuidValueName34",
    r."GuidValueName35",
    r."GuidValueName36",
    r."GuidValueName37",
    r."GuidValueName38",
    r."GuidValueName39",
    r."GuidValueName40",
    r."GuidValueName41",
    r."GuidValueName42",
    r."GuidValueName43",
    r."GuidValueName44",
    r."GuidValueName45",
    r."GuidValueName46",
    r."GuidValueName47",
    r."GuidValueName48",
    r."GuidValueName49",
    r."GuidValueName50",
    r."ClientId",
    r."StringValue51",
    r."StringValue52",
    r."StringValue53",
    r."StringValue54",
    r."StringValue55",
    r."StringValue56",
    r."StringValue57",
    r."StringValue58",
    r."StringValue59",
    r."StringValue60",
    r."StringValue61",
    r."StringValue62",
    r."StringValue63",
    r."StringValue64",
    r."StringValue65",
    r."StringValue66",
    r."StringValue67",
    r."StringValue68",
    r."StringValue69",
    r."StringValue70",
    r."StringValue71",
    r."StringValue72",
    r."StringValue73",
    r."StringValue74",
    r."StringValue75",
    r."StringValue76",
    r."StringValue77",
    r."StringValue78",
    r."StringValue79",
    r."StringValue80",
    r."StringValue81",
    r."StringValue82",
    r."StringValue83",
    r."StringValue84",
    r."StringValue85",
    r."StringValue86",
    r."StringValue87",
    r."StringValue88",
    r."StringValue89",
    r."StringValue90",
    r."StringValue91",
    r."StringValue92",
    r."StringValue93",
    r."StringValue94",
    r."StringValue95",
    r."StringValue96",
    r."StringValue97",
    r."StringValue98",
    r."StringValue99",
    r."StringValue100",
    r."GuidValueCode51",
    r."GuidValueCode52",
    r."GuidValueCode53",
    r."GuidValueCode54",
    r."GuidValueCode55",
    r."GuidValueCode56",
    r."GuidValueCode57",
    r."GuidValueCode58",
    r."GuidValueCode59",
    r."GuidValueCode60",
    r."GuidValueCode61",
    r."GuidValueCode62",
    r."GuidValueCode63",
    r."GuidValueCode64",
    r."GuidValueCode65",
    r."GuidValueCode66",
    r."GuidValueCode67",
    r."GuidValueCode68",
    r."GuidValueCode69",
    r."GuidValueCode70",
    r."GuidValueCode71",
    r."GuidValueCode72",
    r."GuidValueCode73",
    r."GuidValueCode74",
    r."GuidValueCode75",
    r."GuidValueCode76",
    r."GuidValueCode77",
    r."GuidValueCode78",
    r."GuidValueCode79",
    r."GuidValueCode80",
    r."GuidValueCode81",
    r."GuidValueCode82",
    r."GuidValueCode83",
    r."GuidValueCode84",
    r."GuidValueCode85",
    r."GuidValueCode86",
    r."GuidValueCode87",
    r."GuidValueCode88",
    r."GuidValueCode89",
    r."GuidValueCode90",
    r."GuidValueCode91",
    r."GuidValueCode92",
    r."GuidValueCode93",
    r."GuidValueCode94",
    r."GuidValueCode95",
    r."GuidValueCode96",
    r."GuidValueCode97",
    r."GuidValueCode98",
    r."GuidValueCode99",
    r."GuidValueCode100",
    r."GuidValueName51",
    r."GuidValueName52",
    r."GuidValueName53",
    r."GuidValueName54",
    r."GuidValueName55",
    r."GuidValueName56",
    r."GuidValueName57",
    r."GuidValueName58",
    r."GuidValueName59",
    r."GuidValueName60",
    r."GuidValueName61",
    r."GuidValueName62",
    r."GuidValueName63",
    r."GuidValueName64",
    r."GuidValueName65",
    r."GuidValueName66",
    r."GuidValueName67",
    r."GuidValueName68",
    r."GuidValueName69",
    r."GuidValueName70",
    r."GuidValueName71",
    r."GuidValueName72",
    r."GuidValueName73",
    r."GuidValueName74",
    r."GuidValueName75",
    r."GuidValueName76",
    r."GuidValueName77",
    r."GuidValueName78",
    r."GuidValueName79",
    r."GuidValueName80",
    r."GuidValueName81",
    r."GuidValueName82",
    r."GuidValueName83",
    r."GuidValueName84",
    r."GuidValueName85",
    r."GuidValueName86",
    r."GuidValueName87",
    r."GuidValueName88",
    r."GuidValueName89",
    r."GuidValueName90",
    r."GuidValueName91",
    r."GuidValueName92",
    r."GuidValueName93",
    r."GuidValueName94",
    r."GuidValueName95",
    r."GuidValueName96",
    r."GuidValueName97",
    r."GuidValueName98",
    r."GuidValueName99",
    r."GuidValueName100",
    r."DateTimeValue1",
    r."DateTimeValue2",
    r."DateTimeValue3",
    r."DateTimeValue4",
    r."DateTimeValue5",
    r."DateTimeValue6",
    r."DateTimeValue7",
    r."DateTimeValue8",
    r."DateTimeValue9",
    r."DateTimeValue10",
    r."DateTimeValue11",
    r."DateTimeValue12",
    r."DateTimeValue13",
    r."DateTimeValue14",
    r."DateTimeValue15",
    r."DateTimeValue16",
    r."DateTimeValue17",
    r."DateTimeValue18",
    r."DateTimeValue19",
    r."DateTimeValue20",
    r."DateTimeValue21",
    r."DateTimeValue22",
    r."DateTimeValue23",
    r."DateTimeValue24",
    r."DateTimeValue25",
    r."DateTimeValue26",
    r."DateTimeValue27",
    r."DateTimeValue28",
    r."DateTimeValue29",
    r."DateTimeValue30",
    r."DateTimeValue31",
    r."DateTimeValue32",
    r."DateTimeValue33",
    r."DateTimeValue34",
    r."DateTimeValue35",
    r."DateTimeValue36",
    r."DateTimeValue37",
    r."DateTimeValue38",
    r."DateTimeValue39",
    r."DateTimeValue40",
    r."DateTimeValue41",
    r."DateTimeValue42",
    r."DateTimeValue43",
    r."DateTimeValue44",
    r."DateTimeValue45",
    r."DateTimeValue46",
    r."DateTimeValue47",
    r."DateTimeValue48",
    r."DateTimeValue49",
    r."DateTimeValue50",
    r."DateTimeValue51",
    r."DateTimeValue52",
    r."DateTimeValue53",
    r."DateTimeValue54",
    r."DateTimeValue55",
    r."DateTimeValue56",
    r."DateTimeValue57",
    r."DateTimeValue58",
    r."DateTimeValue59",
    r."DateTimeValue60",
    r."DateTimeValue61",
    r."DateTimeValue62",
    r."DateTimeValue63",
    r."DateTimeValue64",
    r."DateTimeValue65",
    r."DateTimeValue66",
    r."DateTimeValue67",
    r."DateTimeValue68",
    r."DateTimeValue69",
    r."DateTimeValue70",
    r."DateTimeValue71",
    r."DateTimeValue72",
    r."DateTimeValue73",
    r."DateTimeValue74",
    r."DateTimeValue75",
    r."DateTimeValue76",
    r."DateTimeValue77",
    r."DateTimeValue78",
    r."DateTimeValue79",
    r."DateTimeValue80",
    r."DateTimeValue81",
    r."DateTimeValue82",
    r."DateTimeValue83",
    r."DateTimeValue84",
    r."DateTimeValue85",
    r."DateTimeValue86",
    r."DateTimeValue87",
    r."DateTimeValue88",
    r."DateTimeValue89",
    r."DateTimeValue90",
    r."DateTimeValue91",
    r."DateTimeValue92",
    r."DateTimeValue93",
    r."DateTimeValue94",
    r."DateTimeValue95",
    r."DateTimeValue96",
    r."DateTimeValue97",
    r."DateTimeValue98",
    r."DateTimeValue99",
    r."DateTimeValue100",
    r."LongValue1",
    r."LongValue2",
    r."LongValue3",
    r."LongValue4",
    r."LongValue5",
    r."LongValue6",
    r."LongValue7",
    r."LongValue8",
    r."LongValue9",
    r."LongValue10",
    r."LongValue11",
    r."LongValue12",
    r."LongValue13",
    r."LongValue14",
    r."LongValue15",
    r."LongValue16",
    r."LongValue17",
    r."LongValue18",
    r."LongValue19",
    r."LongValue20",
    r."LongValue21",
    r."LongValue22",
    r."LongValue23",
    r."LongValue24",
    r."LongValue25",
    r."LongValue26",
    r."LongValue27",
    r."LongValue28",
    r."LongValue29",
    r."LongValue30",
    r."LongValue31",
    r."LongValue32",
    r."LongValue33",
    r."LongValue34",
    r."LongValue35",
    r."LongValue36",
    r."LongValue37",
    r."LongValue38",
    r."LongValue39",
    r."LongValue40",
    r."LongValue41",
    r."LongValue42",
    r."LongValue43",
    r."LongValue44",
    r."LongValue45",
    r."LongValue46",
    r."LongValue47",
    r."LongValue48",
    r."LongValue49",
    r."LongValue50",
    r."LongValue51",
    r."LongValue52",
    r."LongValue53",
    r."LongValue54",
    r."LongValue55",
    r."LongValue56",
    r."LongValue57",
    r."LongValue58",
    r."LongValue59",
    r."LongValue60",
    r."LongValue61",
    r."LongValue62",
    r."LongValue63",
    r."LongValue64",
    r."LongValue65",
    r."LongValue66",
    r."LongValue67",
    r."LongValue68",
    r."LongValue69",
    r."LongValue70",
    r."LongValue71",
    r."LongValue72",
    r."LongValue73",
    r."LongValue74",
    r."LongValue75",
    r."LongValue76",
    r."LongValue77",
    r."LongValue78",
    r."LongValue79",
    r."LongValue80",
    r."LongValue81",
    r."LongValue82",
    r."LongValue83",
    r."LongValue84",
    r."LongValue85",
    r."LongValue86",
    r."LongValue87",
    r."LongValue88",
    r."LongValue89",
    r."LongValue90",
    r."LongValue91",
    r."LongValue92",
    r."LongValue93",
    r."LongValue94",
    r."LongValue95",
    r."LongValue96",
    r."LongValue97",
    r."LongValue98",
    r."LongValue99",
    r."LongValue100",
    r."OperatorRootGroupName",
    r."OperatorRootGroupId",
    r."ContactPersonOrganization",
    r."ContactPersonNameOrOrganization",
    r."ContactPersonType",
    r."ReturnsFromRatingBotCount",
    r."ExecutorCuratorId",
    r."ExecutorCuratorFio"
   FROM "CALC"."BULK_REQUESTSVIEW" r
     LEFT JOIN "KPI"."REQUEST_KPI" kpi ON r."Id" = kpi."Id"
     LEFT JOIN LATERAL ( SELECT max("OPERATOR_KPI"."ContactStartTime") AS "ContactStartTime"
           FROM "KPI"."OPERATOR_KPI"
          WHERE "OPERATOR_KPI"."ContactEndTime" IS NULL AND "OPERATOR_KPI"."RequestId" = r."Id") okpi ON true;

CREATE OR REPLACE PROCEDURE "CALC"."P_INSERT_SINGLE_BULK_REQUESTSVIEW"(IN req_id bigint)
 LANGUAGE plpgsql
AS $procedure$

             DECLARE
BEGIN
BEGIN
INSERT INTO "CALC"."BULK_REQUESTSVIEW"
    (
        SELECT
            r."Id",
            r."ExternalId",
            r."TimeCreated",
            r."TimeRegistered",
            null as "TimeLastStateChanged",
            coalesce(iof."FIO",cast('Система' as varchar(200))) AS "StatusModifiedBy",
            r."TimeClosed",
            r."Title",
            NULL AS "Description",
            r."BranchId",
            r."Priority",
            r."State",
            r."Status",
            ass."AssignedTo" AS "AssignedToId",
            r."ExecutorId",
            r."SchemaId",
            r."Source",
            r."Channel",
            COALESCE(r."QueueId", -1) AS "QueueId",
            (
                SELECT
                    rq."Title"
                FROM "CRPM_CFG"."RequestQueues" rq
                WHERE
                    rq."Id" = r."QueueId"
                LIMIT 1
    )  AS "QueueTitle",
            				        NULL AS "PlainText",
            				        r."Originator",
            				        r."ParentId" as "ParentId",
            				        COALESCE(rss."Title", rs."Title") AS "StatusName",
            				        sa."Name" AS "ServiceAreaName",
            				        sa."UtcOffset",
            				        CASE
            					        WHEN r."State" = 4 THEN 0
            					        ELSE
            					        (
            						        EXTRACT(day from r."TimeRegistered" - now())*24*60*60 +
            						        EXTRACT(hour from r."TimeRegistered" - now())*60*60 +
            						        EXTRACT(minute from r."TimeRegistered" - now())*60
            					        )
END AS "LifeTime",
            				        ass.assto_name AS "AssignedToName",
            				        exec_.exec_name AS "ExecutorName",
            				        (
            					        SELECT COUNT(1)
            					        FROM 
            						        "UCMM_DATA"."MESSAGE_EXTERNAL_SYSTEM_LINK" esl
            					        JOIN "UCMM_DATA"."ATTACHMENTS" a
            					        ON 
            						        esl."MESSAGE_ID" = a."MessageId"
            					        WHERE 
            						        a."Is_INLINED" = FALSE
            						        AND 
            						        esl."EXTERNAL_ENTITY_ID" = r."Id"
            				        ) AS "TotalAttachments",
            				        (
            					        SELECT 
            						        COUNT(1)
            					        FROM "UCMM_DATA"."MESSAGE_EXTERNAL_SYSTEM_LINK" esl
            					        JOIN "UCMM_DATA"."MESSAGE" m
            					        ON 
            						        esl."MESSAGE_ID" = m."ID"
            					        WHERE 
            						        m."DIRECTION" = 1
            						        AND
            							        m."TypeCode" != 'Note'
            						        AND 
            						        esl."EXTERNAL_ENTITY_ID" = r."Id"
            					        ) AS "TotalIncomingMessages",
            					        (
            						        SELECT 
            							        COUNT(1)
            						        FROM "UCMM_DATA"."MESSAGE_EXTERNAL_SYSTEM_LINK" esl
            						        JOIN "UCMM_DATA"."MESSAGE" m
            						        ON 
            							        esl."MESSAGE_ID" = m."ID"
            						        WHERE 
            							        m."DIRECTION" = 2
            						        AND
            							        m."TypeCode" != 'Note'
            						        AND 
            							        esl."EXTERNAL_ENTITY_ID" = r."Id"
            					        ) AS "TotalOutgoingMessages",
            					        h."TimeOperatorLastDistribution"  AS "AssignedToLastTime",
            					        COALESCE(ca_str."Value15", '') AS "ForwardNote",
            					        COALESCE(ca_str."Value12", '') AS "CustomerDatabaseType",
            					        COALESCE(ca_str."Value6", '') AS "MSISDN",
            					        ------------
            					        h."HT",
            					        h."SA",
            					        (ca_bool."Value3"::int) as lost_sms_flag,
            					        ca_str."Value5" as "RegionName",
            					        cp."ContactPersonID",
            					        cp."ContactPersonName",
            					        CASE
            						        WHEN r."TimeClosed" IS NOT NULL THEN NULL
            						        ELSE postpone."PostponeTime"
END AS "PostponeTime",
            					        r."TimeClosed" IS NOT NULL AS "Closed",
            					        COALESCE(
            						        (
            							        SELECT
            								        AVG("ScoreResult")
            							        FROM "CALC"."V_EVALUATIONS"
            							        WHERE
            								        ("Context"->>'requestId')::bigint = r."Id"
            								        AND "PublishedAt" IS NOT NULL
            							        GROUP BY r."Id"
            						        ), ca_long."Value2")::numeric(3,1) AS "SupervisorScore",
            					        ca_str."Value35" as "StringValue35",
            					        ca_str."Value36" as "StringValue36",
            					        ca_str."Value37" as "StringValue37",
            					        ca_str."Value38" as "StringValue38",
            					        ca_str."Value39" as "StringValue39",
            					        ca_str."Value40" as "StringValue40",
            					        ca_str."Value41" as "StringValue41",
            					        ca_str."Value42" as "StringValue42",
            					        ca_str."Value43" as "StringValue43",
            					        ca_str."Value44" as "StringValue44",
            					        ca_str."Value45" as "StringValue45",
            					        ca_str."Value46" as "StringValue46",
            					        ca_str."Value47" as "StringValue47",
            					        ca_str."Value48" as "StringValue48",
            					        ca_str."Value49" as "StringValue49",
            					        ca_str."Value50" as "StringValue50",
            					        ca_datetime."Value30" as "TargetResponseTime",
            					        rlci."STATUS_LAST_CHANGE_DATE_TIME" as "StatusTime",
            					        iof."LOGIN" as "StatusModifiedByLogin",
            					        COALESCE(ca_long."Value3", 0) "RequestType",
            					        case  when ca_datetime."Value7" IS NULL then 0 else 1 end as "RequestOverdue",
            					        r."RepeatedRequestId",
            			                ca_datetime."Value9" as "TargetDecisionTime",
            					        case when ca_datetime."Value8" IS NULL then 0 else 1 end as "RequestDecisionOverdue",
            					        ---------------

            					        -- ticketing
            					        ca_long."Value5" as "TicketsTotalCount",
            					        ca_long."Value6" as "TicketsRequiredProcessingCount",
            					        ca_long."Value7" as "TicketsProcessingRequiredAnswerReceivedCount",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value10" = dv."ID") as "GuidValueCode10",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value11" = dv."ID") as "GuidValueCode11",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value12" = dv."ID") as "GuidValueCode12",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value13" = dv."ID") as "GuidValueCode13",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value14" = dv."ID") as "GuidValueCode14",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value15" = dv."ID") as "GuidValueCode15",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value16" = dv."ID") as "GuidValueCode16",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value17" = dv."ID") as "GuidValueCode17",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value18" = dv."ID") as "GuidValueCode18",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value19" = dv."ID") as "GuidValueCode19",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value20" = dv."ID") as "GuidValueCode20",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value21" = dv."ID") as "GuidValueCode21",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value22" = dv."ID") as "GuidValueCode22",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value23" = dv."ID") as "GuidValueCode23",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value24" = dv."ID") as "GuidValueCode24",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value25" = dv."ID") as "GuidValueCode25",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value26" = dv."ID") as "GuidValueCode26",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value27" = dv."ID") as "GuidValueCode27",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value28" = dv."ID") as "GuidValueCode28",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value29" = dv."ID") as "GuidValueCode29",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value30" = dv."ID") as "GuidValueCode30",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value31" = dv."ID") as "GuidValueCode31",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value32" = dv."ID") as "GuidValueCode32",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value33" = dv."ID") as "GuidValueCode33",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value34" = dv."ID") as "GuidValueCode34",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value35" = dv."ID") as "GuidValueCode35",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value36" = dv."ID") as "GuidValueCode36",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value37" = dv."ID") as "GuidValueCode37",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value38" = dv."ID") as "GuidValueCode38",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value39" = dv."ID") as "GuidValueCode39",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value40" = dv."ID") as "GuidValueCode40",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value41" = dv."ID") as "GuidValueCode41",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value42" = dv."ID") as "GuidValueCode42",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value43" = dv."ID") as "GuidValueCode43",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value44" = dv."ID") as "GuidValueCode44",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value45" = dv."ID") as "GuidValueCode45",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value46" = dv."ID") as "GuidValueCode46",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value47" = dv."ID") as "GuidValueCode47",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value48" = dv."ID") as "GuidValueCode48",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value49" = dv."ID") as "GuidValueCode49",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value50" = dv."ID") as "GuidValueCode50",

            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value10" = dv."ID") as "GuidValueName10",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value11" = dv."ID") as "GuidValueName11",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value12" = dv."ID") as "GuidValueName12",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value13" = dv."ID") as "GuidValueName13",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value14" = dv."ID") as "GuidValueName14",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value15" = dv."ID") as "GuidValueName15",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value16" = dv."ID") as "GuidValueName16",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value17" = dv."ID") as "GuidValueName17",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value18" = dv."ID") as "GuidValueName18",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value19" = dv."ID") as "GuidValueName19",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value20" = dv."ID") as "GuidValueName20",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value21" = dv."ID") as "GuidValueName21",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value22" = dv."ID") as "GuidValueName22",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value23" = dv."ID") as "GuidValueName23",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value24" = dv."ID") as "GuidValueName24",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value25" = dv."ID") as "GuidValueName25",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value26" = dv."ID") as "GuidValueName26",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value27" = dv."ID") as "GuidValueName27",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value28" = dv."ID") as "GuidValueName28",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value29" = dv."ID") as "GuidValueName29",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value30" = dv."ID") as "GuidValueName30",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value31" = dv."ID") as "GuidValueName31",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value32" = dv."ID") as "GuidValueName32",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value33" = dv."ID") as "GuidValueName33",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value34" = dv."ID") as "GuidValueName34",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value35" = dv."ID") as "GuidValueName35",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value36" = dv."ID") as "GuidValueName36",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value37" = dv."ID") as "GuidValueName37",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value38" = dv."ID") as "GuidValueName38",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value39" = dv."ID") as "GuidValueName39",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value40" = dv."ID") as "GuidValueName40",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value41" = dv."ID") as "GuidValueName41",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value42" = dv."ID") as "GuidValueName42",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value43" = dv."ID") as "GuidValueName43",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value44" = dv."ID") as "GuidValueName44",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value45" = dv."ID") as "GuidValueName45",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value46" = dv."ID") as "GuidValueName46",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value47" = dv."ID") as "GuidValueName47",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value48" = dv."ID") as "GuidValueName48",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value49" = dv."ID") as "GuidValueName49",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value50" = dv."ID") as "GuidValueName50",
            					        cp_ca_str."Value2" as "ClientId",
            					        ca_str."Value51" as "StringValue51",
            					        ca_str."Value52" as "StringValue52",
            					        ca_str."Value53" as "StringValue53",
            					        ca_str."Value54" as "StringValue54",
            					        ca_str."Value55" as "StringValue55",
            					        ca_str."Value56" as "StringValue56",
            					        ca_str."Value57" as "StringValue57",
            					        ca_str."Value58" as "StringValue58",
            					        ca_str."Value59" as "StringValue59",
            					        ca_str."Value60" as "StringValue60",
            					        ca_str."Value61" as "StringValue61",
            					        ca_str."Value62" as "StringValue62",
            					        ca_str."Value63" as "StringValue63",
            					        ca_str."Value64" as "StringValue64",
            					        ca_str."Value65" as "StringValue65",
            					        ca_str."Value66" as "StringValue66",
            					        ca_str."Value67" as "StringValue67",
            					        ca_str."Value68" as "StringValue68",
            					        ca_str."Value69" as "StringValue69",
            					        ca_str."Value70" as "StringValue70",
            					        ca_str."Value71" as "StringValue71",
            					        ca_str."Value72" as "StringValue72",
            					        ca_str."Value73" as "StringValue73",
            					        ca_str."Value74" as "StringValue74",
            					        ca_str."Value75" as "StringValue75",
            					        ca_str."Value76" as "StringValue76",
            					        ca_str."Value77" as "StringValue77",
            					        ca_str."Value78" as "StringValue78",
            					        ca_str."Value79" as "StringValue79",
            					        ca_str."Value80" as "StringValue80",
            					        ca_str."Value81" as "StringValue81",
            					        ca_str."Value82" as "StringValue82",
            					        ca_str."Value83" as "StringValue83",
            					        ca_str."Value84" as "StringValue84",
            					        ca_str."Value85" as "StringValue85",
            					        ca_str."Value86" as "StringValue86",
            					        ca_str."Value87" as "StringValue87",
            					        ca_str."Value88" as "StringValue88",
            					        ca_str."Value89" as "StringValue89",
            					        ca_str."Value90" as "StringValue90",
            					        ca_str."Value91" as "StringValue91",
            					        ca_str."Value92" as "StringValue92",
            					        ca_str."Value93" as "StringValue93",
            					        ca_str."Value94" as "StringValue94",
            					        ca_str."Value95" as "StringValue95",
            					        ca_str."Value96" as "StringValue96",
            					        ca_str."Value97" as "StringValue97",
            					        ca_str."Value98" as "StringValue98",
            					        ca_str."Value99" as "StringValue99",
            					        ca_str."Value100" as "StringValue100",

            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value51" = dv."ID") as "GuidValueCode51",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value52" = dv."ID") as "GuidValueCode52",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value53" = dv."ID") as "GuidValueCode53",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value54" = dv."ID") as "GuidValueCode54",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value55" = dv."ID") as "GuidValueCode55",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value56" = dv."ID") as "GuidValueCode56",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value57" = dv."ID") as "GuidValueCode57",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value58" = dv."ID") as "GuidValueCode58",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value59" = dv."ID") as "GuidValueCode59",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value60" = dv."ID") as "GuidValueCode60",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value61" = dv."ID") as "GuidValueCode61",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value62" = dv."ID") as "GuidValueCode62",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value63" = dv."ID") as "GuidValueCode63",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value64" = dv."ID") as "GuidValueCode64",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value65" = dv."ID") as "GuidValueCode65",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value66" = dv."ID") as "GuidValueCode66",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value67" = dv."ID") as "GuidValueCode67",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value68" = dv."ID") as "GuidValueCode68",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value69" = dv."ID") as "GuidValueCode69",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value70" = dv."ID") as "GuidValueCode70",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value71" = dv."ID") as "GuidValueCode71",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value72" = dv."ID") as "GuidValueCode72",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value73" = dv."ID") as "GuidValueCode73",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value74" = dv."ID") as "GuidValueCode74",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value75" = dv."ID") as "GuidValueCode75",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value76" = dv."ID") as "GuidValueCode76",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value77" = dv."ID") as "GuidValueCode77",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value78" = dv."ID") as "GuidValueCode78",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value79" = dv."ID") as "GuidValueCode79",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value80" = dv."ID") as "GuidValueCode80",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value81" = dv."ID") as "GuidValueCode81",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value82" = dv."ID") as "GuidValueCode82",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value83" = dv."ID") as "GuidValueCode83",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value84" = dv."ID") as "GuidValueCode84",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value85" = dv."ID") as "GuidValueCode85",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value86" = dv."ID") as "GuidValueCode86",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value87" = dv."ID") as "GuidValueCode87",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value88" = dv."ID") as "GuidValueCode88",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value89" = dv."ID") as "GuidValueCode89",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value90" = dv."ID") as "GuidValueCode90",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value91" = dv."ID") as "GuidValueCode91",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value92" = dv."ID") as "GuidValueCode92",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value93" = dv."ID") as "GuidValueCode93",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value94" = dv."ID") as "GuidValueCode94",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value95" = dv."ID") as "GuidValueCode95",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value96" = dv."ID") as "GuidValueCode96",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value97" = dv."ID") as "GuidValueCode97",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value98" = dv."ID") as "GuidValueCode98",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value99" = dv."ID") as "GuidValueCode99",
            					        (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value100" = dv."ID") as "GuidValueCode100",

            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value51" = dv."ID") as "GuidValueName51",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value52" = dv."ID") as "GuidValueName52",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value53" = dv."ID") as "GuidValueName53",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value54" = dv."ID") as "GuidValueName54",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value55" = dv."ID") as "GuidValueName55",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value56" = dv."ID") as "GuidValueName56",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value57" = dv."ID") as "GuidValueName57",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value58" = dv."ID") as "GuidValueName58",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value59" = dv."ID") as "GuidValueName59",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value60" = dv."ID") as "GuidValueName60",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value61" = dv."ID") as "GuidValueName61",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value62" = dv."ID") as "GuidValueName62",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value63" = dv."ID") as "GuidValueName63",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value64" = dv."ID") as "GuidValueName64",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value65" = dv."ID") as "GuidValueName65",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value66" = dv."ID") as "GuidValueName66",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value67" = dv."ID") as "GuidValueName67",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value68" = dv."ID") as "GuidValueName68",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value69" = dv."ID") as "GuidValueName69",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value70" = dv."ID") as "GuidValueName70",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value71" = dv."ID") as "GuidValueName71",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value72" = dv."ID") as "GuidValueName72",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value73" = dv."ID") as "GuidValueName73",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value74" = dv."ID") as "GuidValueName74",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value75" = dv."ID") as "GuidValueName75",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value76" = dv."ID") as "GuidValueName76",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value77" = dv."ID") as "GuidValueName77",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value78" = dv."ID") as "GuidValueName78",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value79" = dv."ID") as "GuidValueName79",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value80" = dv."ID") as "GuidValueName80",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value81" = dv."ID") as "GuidValueName81",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value82" = dv."ID") as "GuidValueName82",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value83" = dv."ID") as "GuidValueName83",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value84" = dv."ID") as "GuidValueName84",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value85" = dv."ID") as "GuidValueName85",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value86" = dv."ID") as "GuidValueName86",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value87" = dv."ID") as "GuidValueName87",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value88" = dv."ID") as "GuidValueName88",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value89" = dv."ID") as "GuidValueName89",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value90" = dv."ID") as "GuidValueName90",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value91" = dv."ID") as "GuidValueName91",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value92" = dv."ID") as "GuidValueName92",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value93" = dv."ID") as "GuidValueName93",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value94" = dv."ID") as "GuidValueName94",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value95" = dv."ID") as "GuidValueName95",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value96" = dv."ID") as "GuidValueName96",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value97" = dv."ID") as "GuidValueName97",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value98" = dv."ID") as "GuidValueName98",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value99" = dv."ID") as "GuidValueName99",
            					        (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value100" = dv."ID") as "GuidValueName100",

            					        ca_datetime."Value1" as "DateTimeValue1",
            					        ca_datetime."Value2" as "DateTimeValue2",
            					        ca_datetime."Value3" as "DateTimeValue3",
            					        ca_datetime."Value4" as "DateTimeValue4",
            					        ca_datetime."Value5" as "DateTimeValue5",
            					        ca_datetime."Value6" as "DateTimeValue6",
            					        ca_datetime."Value7" as "DateTimeValue7",
            					        ca_datetime."Value8" as "DateTimeValue8",
            					        ca_datetime."Value9" as "DateTimeValue9",
            					        ca_datetime."Value10" as "DateTimeValue10",
            					        ca_datetime."Value11" as "DateTimeValue11",
            					        ca_datetime."Value12" as "DateTimeValue12",
            					        ca_datetime."Value13" as "DateTimeValue13",
            					        ca_datetime."Value14" as "DateTimeValue14",
            					        ca_datetime."Value15" as "DateTimeValue15",
            					        ca_datetime."Value16" as "DateTimeValue16",
            					        ca_datetime."Value17" as "DateTimeValue17",
            					        ca_datetime."Value18" as "DateTimeValue18",
            					        ca_datetime."Value19" as "DateTimeValue19",
            					        ca_datetime."Value20" as "DateTimeValue20",
            					        ca_datetime."Value21" as "DateTimeValue21",
            					        ca_datetime."Value22" as "DateTimeValue22",
            					        ca_datetime."Value23" as "DateTimeValue23",
            					        ca_datetime."Value24" as "DateTimeValue24",
            					        ca_datetime."Value25" as "DateTimeValue25",
            					        ca_datetime."Value26" as "DateTimeValue26",
            					        ca_datetime."Value27" as "DateTimeValue27",
            					        ca_datetime."Value28" as "DateTimeValue28",
            					        ca_datetime."Value29" as "DateTimeValue29",
            					        ca_datetime."Value30" as "DateTimeValue30",
            					        ca_datetime."Value31" as "DateTimeValue31",
            					        ca_datetime."Value32" as "DateTimeValue32",
            					        ca_datetime."Value33" as "DateTimeValue33",
            					        ca_datetime."Value34" as "DateTimeValue34",
            					        ca_datetime."Value35" as "DateTimeValue35",
            					        ca_datetime."Value36" as "DateTimeValue36",
            					        ca_datetime."Value37" as "DateTimeValue37",
            					        ca_datetime."Value38" as "DateTimeValue38",
            					        ca_datetime."Value39" as "DateTimeValue39",
            					        ca_datetime."Value40" as "DateTimeValue40",
            					        ca_datetime."Value41" as "DateTimeValue41",
            					        ca_datetime."Value42" as "DateTimeValue42",
            					        ca_datetime."Value43" as "DateTimeValue43",
            					        ca_datetime."Value44" as "DateTimeValue44",
            					        ca_datetime."Value45" as "DateTimeValue45",
            					        ca_datetime."Value46" as "DateTimeValue46",
            					        ca_datetime."Value47" as "DateTimeValue47",
            					        ca_datetime."Value48" as "DateTimeValue48",
            					        ca_datetime."Value49" as "DateTimeValue49",
            					        ca_datetime."Value50" as "DateTimeValue50",
            					        ca_datetime."Value51" as "DateTimeValue51",
            					        ca_datetime."Value52" as "DateTimeValue52",
            					        ca_datetime."Value53" as "DateTimeValue53",
            					        ca_datetime."Value54" as "DateTimeValue54",
            					        ca_datetime."Value55" as "DateTimeValue55",
            					        ca_datetime."Value56" as "DateTimeValue56",
            					        ca_datetime."Value57" as "DateTimeValue57",
            					        ca_datetime."Value58" as "DateTimeValue58",
            					        ca_datetime."Value59" as "DateTimeValue59",
            					        ca_datetime."Value60" as "DateTimeValue60",
            					        ca_datetime."Value61" as "DateTimeValue61",
            					        ca_datetime."Value62" as "DateTimeValue62",
            					        ca_datetime."Value63" as "DateTimeValue63",
            					        ca_datetime."Value64" as "DateTimeValue64",
            					        ca_datetime."Value65" as "DateTimeValue65",
            					        ca_datetime."Value66" as "DateTimeValue66",
            					        ca_datetime."Value67" as "DateTimeValue67",
            					        ca_datetime."Value68" as "DateTimeValue68",
            					        ca_datetime."Value69" as "DateTimeValue69",
            					        ca_datetime."Value70" as "DateTimeValue70",
            					        ca_datetime."Value71" as "DateTimeValue71",
            					        ca_datetime."Value72" as "DateTimeValue72",
            					        ca_datetime."Value73" as "DateTimeValue73",
            					        ca_datetime."Value74" as "DateTimeValue74",
            					        ca_datetime."Value75" as "DateTimeValue75",
            					        ca_datetime."Value76" as "DateTimeValue76",
            					        ca_datetime."Value77" as "DateTimeValue77",
            					        ca_datetime."Value78" as "DateTimeValue78",
            					        ca_datetime."Value79" as "DateTimeValue79",
            					        ca_datetime."Value80" as "DateTimeValue80",
            					        ca_datetime."Value81" as "DateTimeValue81",
            					        ca_datetime."Value82" as "DateTimeValue82",
            					        ca_datetime."Value83" as "DateTimeValue83",
            					        ca_datetime."Value84" as "DateTimeValue84",
            					        ca_datetime."Value85" as "DateTimeValue85",
            					        ca_datetime."Value86" as "DateTimeValue86",
            					        ca_datetime."Value87" as "DateTimeValue87",
            					        ca_datetime."Value88" as "DateTimeValue88",
            					        ca_datetime."Value89" as "DateTimeValue89",
            					        ca_datetime."Value90" as "DateTimeValue90",
            					        ca_datetime."Value91" as "DateTimeValue91",
            					        ca_datetime."Value92" as "DateTimeValue92",
            					        ca_datetime."Value93" as "DateTimeValue93",
            					        ca_datetime."Value94" as "DateTimeValue94",
            					        ca_datetime."Value95" as "DateTimeValue95",
            					        ca_datetime."Value96" as "DateTimeValue96",
            					        ca_datetime."Value97" as "DateTimeValue97",
            					        ca_datetime."Value98" as "DateTimeValue98",
            					        ca_datetime."Value99" as "DateTimeValue99",
            					        ca_datetime."Value100" as "DateTimeValue100",

            					        ca_long."Value1" as "LongValue1",
            					        ca_long."Value2" as "LongValue2",
            					        ca_long."Value3" as "LongValue3",
            					        ca_long."Value4" as "LongValue4",
            					        ca_long."Value5" as "LongValue5",
            					        ca_long."Value6" as "LongValue6",
            					        ca_long."Value7" as "LongValue7",
            					        ca_long."Value8" as "LongValue8",
            					        ca_long."Value9" as "LongValue9",
            					        ca_long."Value10" as "LongValue10",
            					        ca_long."Value11" as "LongValue11",
            					        ca_long."Value12" as "LongValue12",
            					        ca_long."Value13" as "LongValue13",
            					        ca_long."Value14" as "LongValue14",
            					        ca_long."Value15" as "LongValue15",
            					        ca_long."Value16" as "LongValue16",
            					        ca_long."Value17" as "LongValue17",
            					        ca_long."Value18" as "LongValue18",
            					        ca_long."Value19" as "LongValue19",
            					        ca_long."Value20" as "LongValue20",
            					        ca_long."Value21" as "LongValue21",
            					        ca_long."Value22" as "LongValue22",
            					        ca_long."Value23" as "LongValue23",
            					        ca_long."Value24" as "LongValue24",
            					        ca_long."Value25" as "LongValue25",
            					        ca_long."Value26" as "LongValue26",
            					        ca_long."Value27" as "LongValue27",
            					        ca_long."Value28" as "LongValue28",
            					        ca_long."Value29" as "LongValue29",
            					        ca_long."Value30" as "LongValue30",
            					        ca_long."Value31" as "LongValue31",
            					        ca_long."Value32" as "LongValue32",
            					        ca_long."Value33" as "LongValue33",
            					        ca_long."Value34" as "LongValue34",
            					        ca_long."Value35" as "LongValue35",
            					        ca_long."Value36" as "LongValue36",
            					        ca_long."Value37" as "LongValue37",
            					        ca_long."Value38" as "LongValue38",
            					        ca_long."Value39" as "LongValue39",
            					        ca_long."Value40" as "LongValue40",
            					        ca_long."Value41" as "LongValue41",
            					        ca_long."Value42" as "LongValue42",
            					        ca_long."Value43" as "LongValue43",
            					        ca_long."Value44" as "LongValue44",
            					        ca_long."Value45" as "LongValue45",
            					        ca_long."Value46" as "LongValue46",
            					        ca_long."Value47" as "LongValue47",
            					        ca_long."Value48" as "LongValue48",
            					        ca_long."Value49" as "LongValue49",
            					        ca_long."Value50" as "LongValue50",
            					        ca_long."Value51" as "LongValue51",
            					        ca_long."Value52" as "LongValue52",
            					        ca_long."Value53" as "LongValue53",
            					        ca_long."Value54" as "LongValue54",
            					        ca_long."Value55" as "LongValue55",
            					        ca_long."Value56" as "LongValue56",
            					        ca_long."Value57" as "LongValue57",
            					        ca_long."Value58" as "LongValue58",
            					        ca_long."Value59" as "LongValue59",
            					        ca_long."Value60" as "LongValue60",
            					        ca_long."Value61" as "LongValue61",
            					        ca_long."Value62" as "LongValue62",
            					        ca_long."Value63" as "LongValue63",
            					        ca_long."Value64" as "LongValue64",
            					        ca_long."Value65" as "LongValue65",
            					        ca_long."Value66" as "LongValue66",
            					        ca_long."Value67" as "LongValue67",
            					        ca_long."Value68" as "LongValue68",
            					        ca_long."Value69" as "LongValue69",
            					        ca_long."Value70" as "LongValue70",
            					        ca_long."Value71" as "LongValue71",
            					        ca_long."Value72" as "LongValue72",
            					        ca_long."Value73" as "LongValue73",
            					        ca_long."Value74" as "LongValue74",
            					        ca_long."Value75" as "LongValue75",
            					        ca_long."Value76" as "LongValue76",
            					        ca_long."Value77" as "LongValue77",
            					        ca_long."Value78" as "LongValue78",
            					        ca_long."Value79" as "LongValue79",
            					        ca_long."Value80" as "LongValue80",
            					        ca_long."Value81" as "LongValue81",
            					        ca_long."Value82" as "LongValue82",
            					        ca_long."Value83" as "LongValue83",
            					        ca_long."Value84" as "LongValue84",
            					        ca_long."Value85" as "LongValue85",
            					        ca_long."Value86" as "LongValue86",
            					        ca_long."Value87" as "LongValue87",
            					        ca_long."Value88" as "LongValue88",
            					        ca_long."Value89" as "LongValue89",
            					        ca_long."Value90" as "LongValue90",
            					        ca_long."Value91" as "LongValue91",
            					        ca_long."Value92" as "LongValue92",
            					        ca_long."Value93" as "LongValue93",
            					        ca_long."Value94" as "LongValue94",
            					        ca_long."Value95" as "LongValue95",
            					        ca_long."Value96" as "LongValue96",
            					        ca_long."Value97" as "LongValue97",
            					        ca_long."Value98" as "LongValue98",
            					        ca_long."Value99" as "LongValue99",
            					        ca_long."Value100" as "LongValue100",
        				                operatorgroup."Name",
        				                operatorgroup."Id",
        				                cp."ContactPersonOrganization",
            					        cp."ContactPersonNameOrOrganization",
        				                cp."ContactPersonType",
        				                ca_long."Value10" as "ReturnsFromRatingBotCount",
        				                curators."FIO" as "ExecutorCuratorFio",
        				                curators."ID" as "ExecutorCuratorId"
            			        FROM "CRPM_DATA"."Requests" r
            			        LEFT JOIN 
            			        (
            				        SELECT  
            					        ass."AssignedTo", 
            					        ass."RequestId", 
            					        OP.assto_name
            				        FROM "CRPM_DATA"."Assignments" ass
            				        join 
            				        (
            					        SELECT 
            						        "ID" as "Id", 
            						        op."FIO" as assto_name
            					        FROM "CALC"."INFRA_Operators_FIO" op
            				        ) OP 
            				        ON 
            					        op."Id" = ass."AssignedTo"
            				        WHERE 
            					        ass."RequestId" = req_id
            						        --order by ass."RequestId"
            			        ) ass 
            			        on  
            				        ass."RequestId" = r."Id"
            			        LEFT JOIN "CRPM_DATA"."REQUESTS_LAST_CHANGES_INFO" rlci 
            				        on 
            					        rlci."REQUEST_ID"=r."Id"
            			        LEFT JOIN "CALC"."INFRA_Operators_FIO" iof 
            				        on 
            					        lower(rlci."STATUS_LAST_CHANGED_BY") = lower(iof."LOGIN")
            			        LEFT JOIN "CALC"."CFG_RequestStates" rs 
            				        ON 
            					        rs."Id" = r."State"
            			        LEFT JOIN "CALC"."CFG_RequestStateStatuses" rss 
            				        ON 
            					        rss."Id" = r."Status"
            			        LEFT JOIN "CALC"."INFRA_ServiceAreas" sa 
            				        ON 
            					        sa."Id" = r."BranchId"
            			        LEFT JOIN "CRPM_DATA"."REQUEST_CA_STRING" ca_str 
            				        on 
            					        ca_str."Id" = r."Id"
            			        LEFT JOIN "CRPM_DATA"."REQUEST_CA_LONG" ca_long 
            				        on 
            					        ca_long."Id" = r."Id"
            			        LEFT JOIN "CRPM_DATA"."REQUEST_CA_BOOL" ca_bool 
            				        on 
            					        ca_bool."Id" = r."Id"
            			        LEFT JOIN "CRPM_DATA"."REQUEST_CA_DATETIME" ca_datetime 
            				        on 
            					        ca_datetime."Id" = r."Id"
            			        LEFT JOIN "CRPM_DATA"."REQUEST_CA_GUID" ca_guid 
            				        on 
            					        ca_guid."Id"  = r."Id"
            			        LEFT JOIN
            			        (
            				        SELECT  
            					        "ParentId", 
            					        COUNT(1) "ChildCount"
            				        FROM "CRPM_DATA"."Requests"
            				        WHERE 
            					        "ParentId" IS NOT NULL
            				        GROUP BY 
            					        "ParentId"
            				        --ORDER BY "ParentId"
            			        ) c 
            			        ON 
            				        c."ParentId" = r."Id"
            			        LEFT JOIN 
            			        (
            				        SELECT
            					        CASE
            						        WHEN op3."ID" IS NULL THEN '???????'
            						        ELSE op3."FIO"
            					        END as exec_name,
            					        op3."ID" as "Id"
            				        FROM "CALC"."INFRA_Operators_FIO" op3
            			        ) exec_ 
            			        ON 
            				        exec_."Id"=r."ExecutorId"

            			        LEFT JOIN "KPI"."REQUEST_KPI" h  
            			        ON 
            				        h."Id" = r."Id"
            			        LEFT JOIN 
            			        (
            				        SELECT cp_in."ID" as "ContactPersonID", 
        						    (
        						        CONCAT_WS(' ', 
        						        	COALESCE(NULLIF(TRIM(cp_in."LAST_NAME"), ''), ''), 
        						        	COALESCE(NULLIF(TRIM(cp_in."FIRST_NAME"), ''), ''), 
        						        	COALESCE(TRIM(cp_in."MIDDLE_NAME"), ''))
        						    ) AS "ContactPersonName",
        							cp_in."NAME" AS "ContactPersonOrganization",
        							NULL AS "ContactPersonNameOrOrganization",
        							cpcalong."Value1" AS "ContactPersonType"
        						from "UCMM_DATA"."CONTACT_PERSON" cp_in
        						JOIN "UCMM_DATA"."CONTACT_PERSON_CA_LONG" cpcalong ON cpcalong."ID" = cp_in."ID"
        						JOIN "UCMM_DATA"."CONTACT_PERSON_CA_STRING" cpcastring ON cpcastring."ID" = cp_in."ID"
            			        ) cp 
            			        on 
            				        r."OriginatorId"=cp."ContactPersonID"
            			        LEFT JOIN "UCMM_DATA"."CONTACT_PERSON_CA_STRING" cp_ca_str  
            			        on 
            				        cp_ca_str."ID" = r."OriginatorId"
            			        LEFT JOIN 
            			        (
            				        SELECT 
            					        w."RequestId", 
            					        wse."ExpirationDate" AS "PostponeTime"
            				        FROM "CRPM_DATA"."Works" w
            				        INNER JOIN "CRPM_DATA"."WorkStateExpirations" wse ON w."Id" = wse."WorkId"
            				        WHERE w."State" = 1
            			        ) postpone 
            			        ON 
            				        postpone."RequestId" = r."Id"
            			        LEFT JOIN
        		                (
        			                SELECT operatorgroup."Name", operatorgroup."Id", rq."Id" as "QueueId"
        			                FROM 
        				                "CRPM_CFG"."CustomAttributes" ca
        				                JOIN "CRPM_CFG"."RequestQueues" rq ON rq."Id" = ca."QueueId"
        				                JOIN "AWP_INFRA"."OperatorGroups" operatorgroup ON operatorgroup."Id"::character varying::text = ca."Value"::text
        			                WHERE 
        				                ca."Key" = 'Division'::text
        		                ) operatorgroup on operatorgroup."QueueId" = r."QueueId"
            			        LEFT JOIN "CRPM_DATA"."REQUEST_CA_GUID" curator_ca
					                ON r."Id" = curator_ca."Id"
            			        LEFT JOIN "CALC"."INFRA_Operators_FIO" curators
					                ON curators."ID" = curator_ca."Value3"			
            			        WHERE 
            				        r."Id" = req_id	
            		        );
EXCEPTION WHEN OTHERS THEN
            		        CALL "CALC"."P_WRITE_LOG"(SQLERRM || ', RequestId = ' || req_id, 'Error', 'P_INSERT_SINGLE_BULK_REQUESTSVIEW');
END;
            	        --COMMIT;		
END;

    $procedure$
;

CREATE OR REPLACE PROCEDURE "CALC"."P_INSERT_SINGLE_BULK_REQUESTSVIEW"(IN req_id bigint, IN is_closed boolean)
 LANGUAGE plpgsql
AS $procedure$
                        DECLARE

BEGIN
BEGIN
INSERT INTO "CALC"."BULK_REQUESTSVIEW"
    (
        SELECT
            r."Id",
            r."ExternalId",
            r."TimeCreated",
            r."TimeRegistered",
            null as "TimeLastStateChanged",
            coalesce(iof."FIO",cast('Система' as varchar(200))) AS "StatusModifiedBy",
            r."TimeClosed",
            r."Title",
            NULL AS "Description",
            r."BranchId",
            r."Priority",
            r."State",
            r."Status",
            ass."AssignedTo" AS "AssignedToId",
            r."ExecutorId",
            r."SchemaId",
            r."Source",
            r."Channel",
            COALESCE(r."QueueId", -1) AS "QueueId",
            (
                SELECT
                    rq."Title"
                FROM "CRPM_CFG"."RequestQueues" rq
                WHERE
                    rq."Id" = r."QueueId"
                LIMIT 1
    )  AS "QueueTitle",
            				            NULL AS "PlainText",
            				            r."Originator",
            				            r."ParentId" as "ParentId",
            				            COALESCE(rss."Title", rs."Title") AS "StatusName",
            				            sa."Name" AS "ServiceAreaName",
            				            sa."UtcOffset",
            				            CASE
            					            WHEN r."State" = 4 THEN 0
            					            ELSE
            					            (
            						            EXTRACT(day from r."TimeRegistered" - now())*24*60*60 +
            						            EXTRACT(hour from r."TimeRegistered" - now())*60*60 +
            						            EXTRACT(minute from r."TimeRegistered" - now())*60
            					            )
END AS "LifeTime",
            				            ass.assto_name AS "AssignedToName",
            				            exec_.exec_name AS "ExecutorName",
            				            (
            					            SELECT COUNT(1)
            					            FROM 
            						            "UCMM_DATA"."MESSAGE_EXTERNAL_SYSTEM_LINK" esl
            					            JOIN "UCMM_DATA"."ATTACHMENTS" a
            					            ON 
            						            esl."MESSAGE_ID" = a."MessageId"
            					            WHERE 
            						            a."Is_INLINED" = FALSE
            						            AND 
            						            esl."EXTERNAL_ENTITY_ID" = r."Id"
            				            ) AS "TotalAttachments",
            				            (
            					            SELECT 
            						            COUNT(1)
            					            FROM "UCMM_DATA"."MESSAGE_EXTERNAL_SYSTEM_LINK" esl
            					            JOIN "UCMM_DATA"."MESSAGE" m
            					            ON 
            						            esl."MESSAGE_ID" = m."ID"
            					            WHERE 
            						            m."DIRECTION" = 1
            						            AND
            							            m."TypeCode" != 'Note'
            						            AND 
            						            esl."EXTERNAL_ENTITY_ID" = r."Id"
            					            ) AS "TotalIncomingMessages",
            					            (
            						            SELECT 
            							            COUNT(1)
            						            FROM "UCMM_DATA"."MESSAGE_EXTERNAL_SYSTEM_LINK" esl
            						            JOIN "UCMM_DATA"."MESSAGE" m
            						            ON 
            							            esl."MESSAGE_ID" = m."ID"
            						            WHERE 
            							            m."DIRECTION" = 2 
            							            AND 
            							            esl."EXTERNAL_ENTITY_ID" = r."Id"
            					            ) AS "TotalOutgoingMessages",
            					            last_distributed.time_distributed  AS "AssignedToLastTime",
            					            COALESCE(ca_str."Value15", '') AS "ForwardNote",
            					            COALESCE(ca_str."Value12", '') AS "CustomerDatabaseType",
            					            COALESCE(ca_str."Value6", '') AS "MSISDN",
            					            ------------
            					            h."HT",
            					            h."SA",
            					            (ca_bool."Value3"::int) as lost_sms_flag,
            					            ca_str."Value5" as "RegionName",
            					            cp."ContactPersonID",
            					            cp."ContactPersonName",
            					            CASE
            						            WHEN is_closed = TRUE THEN NULL
            						            ELSE postpone."PostponeTime"
END AS "PostponeTime",
            					            is_closed AS "Closed",
            					            COALESCE(
            						            (
            							            SELECT
            								            AVG("ScoreResult")
            							            FROM "CALC"."V_EVALUATIONS"
            							            WHERE
            								            ("Context"->>'requestId')::bigint = r."Id"
            								            AND "PublishedAt" IS NOT NULL
            							            GROUP BY r."Id"
            						            ), ca_long."Value2")::numeric(3,1) AS "SupervisorScore",
            					            ca_str."Value35" as "StringValue35",
            					            ca_str."Value36" as "StringValue36",
            					            ca_str."Value37" as "StringValue37",
            					            ca_str."Value38" as "StringValue38",
            					            ca_str."Value39" as "StringValue39",
            					            ca_str."Value40" as "StringValue40",
            					            ca_str."Value41" as "StringValue41",
            					            ca_str."Value42" as "StringValue42",
            					            ca_str."Value43" as "StringValue43",
            					            ca_str."Value44" as "StringValue44",
            					            ca_str."Value45" as "StringValue45",
            					            ca_str."Value46" as "StringValue46",
            					            ca_str."Value47" as "StringValue47",
            					            ca_str."Value48" as "StringValue48",
            					            ca_str."Value49" as "StringValue49",
            					            ca_str."Value50" as "StringValue50",
            					            ca_datetime."value30" as "TargetResponseTime",
            					            NULL as "StatusTime",
            					            NULL as "StatusModifiedByLogin",
            					            COALESCE(ca_long."Value3", 0) "RequestType", 
            			                    case  when ca_datetime."Value7" IS NULL then 0 else 1 end as "RequestOverdue",
            					            r."RepeatedRequestId",
            					            ca_datetime."Value9" as "TargetDecisionTime",
            					            case when ca_datetime."Value8" IS NULL then 0 else 1 end as "RequestDecisionOverdue",
            					            ---------------

            					            -- ticketing
            					            ca_long."Value5" as "TicketsTotalCount",
            					            ca_long."Value6" as "TicketsRequiredProcessingCount",
            					            ca_long."Value7" as "TicketsProcessingRequiredAnswerReceivedCount",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value10" = dv."ID") as "GuidValueCode10",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value11" = dv."ID") as "GuidValueCode11",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value12" = dv."ID") as "GuidValueCode12",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value13" = dv."ID") as "GuidValueCode13",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value14" = dv."ID") as "GuidValueCode14",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value15" = dv."ID") as "GuidValueCode15",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value16" = dv."ID") as "GuidValueCode16",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value17" = dv."ID") as "GuidValueCode17",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value18" = dv."ID") as "GuidValueCode18",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value19" = dv."ID") as "GuidValueCode19",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value20" = dv."ID") as "GuidValueCode20",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value21" = dv."ID") as "GuidValueCode21",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value22" = dv."ID") as "GuidValueCode22",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value23" = dv."ID") as "GuidValueCode23",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value24" = dv."ID") as "GuidValueCode24",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value25" = dv."ID") as "GuidValueCode25",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value26" = dv."ID") as "GuidValueCode26",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value27" = dv."ID") as "GuidValueCode27",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value28" = dv."ID") as "GuidValueCode28",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value29" = dv."ID") as "GuidValueCode29",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value30" = dv."ID") as "GuidValueCode30",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value31" = dv."ID") as "GuidValueCode31",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value32" = dv."ID") as "GuidValueCode32",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value33" = dv."ID") as "GuidValueCode33",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value34" = dv."ID") as "GuidValueCode34",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value35" = dv."ID") as "GuidValueCode35",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value36" = dv."ID") as "GuidValueCode36",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value37" = dv."ID") as "GuidValueCode37",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value38" = dv."ID") as "GuidValueCode38",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value39" = dv."ID") as "GuidValueCode39",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value40" = dv."ID") as "GuidValueCode40",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value41" = dv."ID") as "GuidValueCode41",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value42" = dv."ID") as "GuidValueCode42",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value43" = dv."ID") as "GuidValueCode43",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value44" = dv."ID") as "GuidValueCode44",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value45" = dv."ID") as "GuidValueCode45",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value46" = dv."ID") as "GuidValueCode46",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value47" = dv."ID") as "GuidValueCode47",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value48" = dv."ID") as "GuidValueCode48",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value49" = dv."ID") as "GuidValueCode49",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value50" = dv."ID") as "GuidValueCode50",

            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value10" = dv."ID") as "GuidValueName10",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value11" = dv."ID") as "GuidValueName11",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value12" = dv."ID") as "GuidValueName12",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value13" = dv."ID") as "GuidValueName13",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value14" = dv."ID") as "GuidValueName14",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value15" = dv."ID") as "GuidValueName15",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value16" = dv."ID") as "GuidValueName16",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value17" = dv."ID") as "GuidValueName17",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value18" = dv."ID") as "GuidValueName18",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value19" = dv."ID") as "GuidValueName19",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value20" = dv."ID") as "GuidValueName20",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value21" = dv."ID") as "GuidValueName21",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value22" = dv."ID") as "GuidValueName22",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value23" = dv."ID") as "GuidValueName23",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value24" = dv."ID") as "GuidValueName24",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value25" = dv."ID") as "GuidValueName25",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value26" = dv."ID") as "GuidValueName26",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value27" = dv."ID") as "GuidValueName27",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value28" = dv."ID") as "GuidValueName28",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value29" = dv."ID") as "GuidValueName29",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value30" = dv."ID") as "GuidValueName30",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value31" = dv."ID") as "GuidValueName31",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value32" = dv."ID") as "GuidValueName32",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value33" = dv."ID") as "GuidValueName33",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value34" = dv."ID") as "GuidValueName34",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value35" = dv."ID") as "GuidValueName35",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value36" = dv."ID") as "GuidValueName36",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value37" = dv."ID") as "GuidValueName37",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value38" = dv."ID") as "GuidValueName38",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value39" = dv."ID") as "GuidValueName39",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value40" = dv."ID") as "GuidValueName40",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value41" = dv."ID") as "GuidValueName41",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value42" = dv."ID") as "GuidValueName42",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value43" = dv."ID") as "GuidValueName43",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value44" = dv."ID") as "GuidValueName44",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value45" = dv."ID") as "GuidValueName45",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value46" = dv."ID") as "GuidValueName46",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value47" = dv."ID") as "GuidValueName47",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value48" = dv."ID") as "GuidValueName48",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value49" = dv."ID") as "GuidValueName49",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value50" = dv."ID") as "GuidValueName50",					
            					            cp_ca_str."Value2" as "ClientId",
            					            ca_str."Value51" as "StringValue51",
            					            ca_str."Value52" as "StringValue52",
            					            ca_str."Value53" as "StringValue53",
            					            ca_str."Value54" as "StringValue54",
            					            ca_str."Value55" as "StringValue55",
            					            ca_str."Value56" as "StringValue56",
            					            ca_str."Value57" as "StringValue57",
            					            ca_str."Value58" as "StringValue58",
            					            ca_str."Value59" as "StringValue59",
            					            ca_str."Value60" as "StringValue60",
            					            ca_str."Value61" as "StringValue61",
            					            ca_str."Value62" as "StringValue62",
            					            ca_str."Value63" as "StringValue63",
            					            ca_str."Value64" as "StringValue64",
            					            ca_str."Value65" as "StringValue65",
            					            ca_str."Value66" as "StringValue66",
            					            ca_str."Value67" as "StringValue67",
            					            ca_str."Value68" as "StringValue68",
            					            ca_str."Value69" as "StringValue69",
            					            ca_str."Value70" as "StringValue70",
            					            ca_str."Value71" as "StringValue71",
            					            ca_str."Value72" as "StringValue72",
            					            ca_str."Value73" as "StringValue73",
            					            ca_str."Value74" as "StringValue74",
            					            ca_str."Value75" as "StringValue75",
            					            ca_str."Value76" as "StringValue76",
            					            ca_str."Value77" as "StringValue77",
            					            ca_str."Value78" as "StringValue78",
            					            ca_str."Value79" as "StringValue79",
            					            ca_str."Value80" as "StringValue80",
            					            ca_str."Value81" as "StringValue81",
            					            ca_str."Value82" as "StringValue82",
            					            ca_str."Value83" as "StringValue83",
            					            ca_str."Value84" as "StringValue84",
            					            ca_str."Value85" as "StringValue85",
            					            ca_str."Value86" as "StringValue86",
            					            ca_str."Value87" as "StringValue87",
            					            ca_str."Value88" as "StringValue88",
            					            ca_str."Value89" as "StringValue89",
            					            ca_str."Value90" as "StringValue90",
            					            ca_str."Value91" as "StringValue91",
            					            ca_str."Value92" as "StringValue92",
            					            ca_str."Value93" as "StringValue93",
            					            ca_str."Value94" as "StringValue94",
            					            ca_str."Value95" as "StringValue95",
            					            ca_str."Value96" as "StringValue96",
            					            ca_str."Value97" as "StringValue97",
            					            ca_str."Value98" as "StringValue98",
            					            ca_str."Value99" as "StringValue99",
            					            ca_str."Value100" as "StringValue100",

            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value51" = dv."ID") as "GuidValueCode51",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value52" = dv."ID") as "GuidValueCode52",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value53" = dv."ID") as "GuidValueCode53",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value54" = dv."ID") as "GuidValueCode54",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value55" = dv."ID") as "GuidValueCode55",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value56" = dv."ID") as "GuidValueCode56",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value57" = dv."ID") as "GuidValueCode57",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value58" = dv."ID") as "GuidValueCode58",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value59" = dv."ID") as "GuidValueCode59",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value60" = dv."ID") as "GuidValueCode60",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value61" = dv."ID") as "GuidValueCode61",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value62" = dv."ID") as "GuidValueCode62",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value63" = dv."ID") as "GuidValueCode63",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value64" = dv."ID") as "GuidValueCode64",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value65" = dv."ID") as "GuidValueCode65",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value66" = dv."ID") as "GuidValueCode66",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value67" = dv."ID") as "GuidValueCode67",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value68" = dv."ID") as "GuidValueCode68",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value69" = dv."ID") as "GuidValueCode69",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value70" = dv."ID") as "GuidValueCode70",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value71" = dv."ID") as "GuidValueCode71",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value72" = dv."ID") as "GuidValueCode72",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value73" = dv."ID") as "GuidValueCode73",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value74" = dv."ID") as "GuidValueCode74",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value75" = dv."ID") as "GuidValueCode75",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value76" = dv."ID") as "GuidValueCode76",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value77" = dv."ID") as "GuidValueCode77",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value78" = dv."ID") as "GuidValueCode78",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value79" = dv."ID") as "GuidValueCode79",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value80" = dv."ID") as "GuidValueCode80",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value81" = dv."ID") as "GuidValueCode81",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value82" = dv."ID") as "GuidValueCode82",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value83" = dv."ID") as "GuidValueCode83",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value84" = dv."ID") as "GuidValueCode84",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value85" = dv."ID") as "GuidValueCode85",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value86" = dv."ID") as "GuidValueCode86",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value87" = dv."ID") as "GuidValueCode87",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value88" = dv."ID") as "GuidValueCode88",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value89" = dv."ID") as "GuidValueCode89",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value90" = dv."ID") as "GuidValueCode90",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value91" = dv."ID") as "GuidValueCode91",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value92" = dv."ID") as "GuidValueCode92",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value93" = dv."ID") as "GuidValueCode93",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value94" = dv."ID") as "GuidValueCode94",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value95" = dv."ID") as "GuidValueCode95",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value96" = dv."ID") as "GuidValueCode96",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value97" = dv."ID") as "GuidValueCode97",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value98" = dv."ID") as "GuidValueCode98",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value99" = dv."ID") as "GuidValueCode99",
            					            (select "CODE" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value100" = dv."ID") as "GuidValueCode100",

            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value51" = dv."ID") as "GuidValueName51",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value52" = dv."ID") as "GuidValueName52",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value53" = dv."ID") as "GuidValueName53",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value54" = dv."ID") as "GuidValueName54",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value55" = dv."ID") as "GuidValueName55",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value56" = dv."ID") as "GuidValueName56",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value57" = dv."ID") as "GuidValueName57",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value58" = dv."ID") as "GuidValueName58",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value59" = dv."ID") as "GuidValueName59",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value60" = dv."ID") as "GuidValueName60",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value61" = dv."ID") as "GuidValueName61",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value62" = dv."ID") as "GuidValueName62",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value63" = dv."ID") as "GuidValueName63",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value64" = dv."ID") as "GuidValueName64",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value65" = dv."ID") as "GuidValueName65",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value66" = dv."ID") as "GuidValueName66",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value67" = dv."ID") as "GuidValueName67",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value68" = dv."ID") as "GuidValueName68",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value69" = dv."ID") as "GuidValueName69",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value70" = dv."ID") as "GuidValueName70",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value71" = dv."ID") as "GuidValueName71",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value72" = dv."ID") as "GuidValueName72",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value73" = dv."ID") as "GuidValueName73",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value74" = dv."ID") as "GuidValueName74",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value75" = dv."ID") as "GuidValueName75",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value76" = dv."ID") as "GuidValueName76",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value77" = dv."ID") as "GuidValueName77",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value78" = dv."ID") as "GuidValueName78",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value79" = dv."ID") as "GuidValueName79",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value80" = dv."ID") as "GuidValueName80",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value81" = dv."ID") as "GuidValueName81",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value82" = dv."ID") as "GuidValueName82",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value83" = dv."ID") as "GuidValueName83",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value84" = dv."ID") as "GuidValueName84",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value85" = dv."ID") as "GuidValueName85",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value86" = dv."ID") as "GuidValueName86",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value87" = dv."ID") as "GuidValueName87",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value88" = dv."ID") as "GuidValueName88",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value89" = dv."ID") as "GuidValueName89",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value90" = dv."ID") as "GuidValueName90",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value91" = dv."ID") as "GuidValueName91",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value92" = dv."ID") as "GuidValueName92",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value93" = dv."ID") as "GuidValueName93",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value94" = dv."ID") as "GuidValueName94",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value95" = dv."ID") as "GuidValueName95",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value96" = dv."ID") as "GuidValueName96",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value97" = dv."ID") as "GuidValueName97",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value98" = dv."ID") as "GuidValueName98",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value99" = dv."ID") as "GuidValueName99",
            					            (select "NAME" from "RIS_DATA"."DICTIONARIES_VALUES" dv where ca_guid."Value100" = dv."ID") as "GuidValueName100",

            					            ca_datetime."Value1" as "DateTimeValue1",
            					            ca_datetime."Value2" as "DateTimeValue2",
            					            ca_datetime."Value3" as "DateTimeValue3",
            					            ca_datetime."Value4" as "DateTimeValue4",
            					            ca_datetime."Value5" as "DateTimeValue5",
            					            ca_datetime."Value6" as "DateTimeValue6",
            					            ca_datetime."Value7" as "DateTimeValue7",
            					            ca_datetime."Value8" as "DateTimeValue8",
            					            ca_datetime."Value9" as "DateTimeValue9",
            					            ca_datetime."Value10" as "DateTimeValue10",
            					            ca_datetime."Value11" as "DateTimeValue11",
            					            ca_datetime."Value12" as "DateTimeValue12",
            					            ca_datetime."Value13" as "DateTimeValue13",
            					            ca_datetime."Value14" as "DateTimeValue14",
            					            ca_datetime."Value15" as "DateTimeValue15",
            					            ca_datetime."Value16" as "DateTimeValue16",
            					            ca_datetime."Value17" as "DateTimeValue17",
            					            ca_datetime."Value18" as "DateTimeValue18",
            					            ca_datetime."Value19" as "DateTimeValue19",
            					            ca_datetime."Value20" as "DateTimeValue20",
            					            ca_datetime."Value21" as "DateTimeValue21",
            					            ca_datetime."Value22" as "DateTimeValue22",
            					            ca_datetime."Value23" as "DateTimeValue23",
            					            ca_datetime."Value24" as "DateTimeValue24",
            					            ca_datetime."Value25" as "DateTimeValue25",
            					            ca_datetime."Value26" as "DateTimeValue26",
            					            ca_datetime."Value27" as "DateTimeValue27",
            					            ca_datetime."Value28" as "DateTimeValue28",
            					            ca_datetime."Value29" as "DateTimeValue29",
            					            ca_datetime."Value30" as "DateTimeValue30",
            					            ca_datetime."Value31" as "DateTimeValue31",
            					            ca_datetime."Value32" as "DateTimeValue32",
            					            ca_datetime."Value33" as "DateTimeValue33",
            					            ca_datetime."Value34" as "DateTimeValue34",
            					            ca_datetime."Value35" as "DateTimeValue35",
            					            ca_datetime."Value36" as "DateTimeValue36",
            					            ca_datetime."Value37" as "DateTimeValue37",
            					            ca_datetime."Value38" as "DateTimeValue38",
            					            ca_datetime."Value39" as "DateTimeValue39",
            					            ca_datetime."Value40" as "DateTimeValue40",
            					            ca_datetime."Value41" as "DateTimeValue41",
            					            ca_datetime."Value42" as "DateTimeValue42",
            					            ca_datetime."Value43" as "DateTimeValue43",
            					            ca_datetime."Value44" as "DateTimeValue44",
            					            ca_datetime."Value45" as "DateTimeValue45",
            					            ca_datetime."Value46" as "DateTimeValue46",
            					            ca_datetime."Value47" as "DateTimeValue47",
            					            ca_datetime."Value48" as "DateTimeValue48",
            					            ca_datetime."Value49" as "DateTimeValue49",
            					            ca_datetime."Value50" as "DateTimeValue50",
            					            ca_datetime."Value51" as "DateTimeValue51",
            					            ca_datetime."Value52" as "DateTimeValue52",
            					            ca_datetime."Value53" as "DateTimeValue53",
            					            ca_datetime."Value54" as "DateTimeValue54",
            					            ca_datetime."Value55" as "DateTimeValue55",
            					            ca_datetime."Value56" as "DateTimeValue56",
            					            ca_datetime."Value57" as "DateTimeValue57",
            					            ca_datetime."Value58" as "DateTimeValue58",
            					            ca_datetime."Value59" as "DateTimeValue59",
            					            ca_datetime."Value60" as "DateTimeValue60",
            					            ca_datetime."Value61" as "DateTimeValue61",
            					            ca_datetime."Value62" as "DateTimeValue62",
            					            ca_datetime."Value63" as "DateTimeValue63",
            					            ca_datetime."Value64" as "DateTimeValue64",
            					            ca_datetime."Value65" as "DateTimeValue65",
            					            ca_datetime."Value66" as "DateTimeValue66",
            					            ca_datetime."Value67" as "DateTimeValue67",
            					            ca_datetime."Value68" as "DateTimeValue68",
            					            ca_datetime."Value69" as "DateTimeValue69",
            					            ca_datetime."Value70" as "DateTimeValue70",
            					            ca_datetime."Value71" as "DateTimeValue71",
            					            ca_datetime."Value72" as "DateTimeValue72",
            					            ca_datetime."Value73" as "DateTimeValue73",
            					            ca_datetime."Value74" as "DateTimeValue74",
            					            ca_datetime."Value75" as "DateTimeValue75",
            					            ca_datetime."Value76" as "DateTimeValue76",
            					            ca_datetime."Value77" as "DateTimeValue77",
            					            ca_datetime."Value78" as "DateTimeValue78",
            					            ca_datetime."Value79" as "DateTimeValue79",
            					            ca_datetime."Value80" as "DateTimeValue80",
            					            ca_datetime."Value81" as "DateTimeValue81",
            					            ca_datetime."Value82" as "DateTimeValue82",
            					            ca_datetime."Value83" as "DateTimeValue83",
            					            ca_datetime."Value84" as "DateTimeValue84",
            					            ca_datetime."Value85" as "DateTimeValue85",
            					            ca_datetime."Value86" as "DateTimeValue86",
            					            ca_datetime."Value87" as "DateTimeValue87",
            					            ca_datetime."Value88" as "DateTimeValue88",
            					            ca_datetime."Value89" as "DateTimeValue89",
            					            ca_datetime."Value90" as "DateTimeValue90",
            					            ca_datetime."Value91" as "DateTimeValue91",
            					            ca_datetime."Value92" as "DateTimeValue92",
            					            ca_datetime."Value93" as "DateTimeValue93",
            					            ca_datetime."Value94" as "DateTimeValue94",
            					            ca_datetime."Value95" as "DateTimeValue95",
            					            ca_datetime."Value96" as "DateTimeValue96",
            					            ca_datetime."Value97" as "DateTimeValue97",
            					            ca_datetime."Value98" as "DateTimeValue98",
            					            ca_datetime."Value99" as "DateTimeValue99",
            					            ca_datetime."Value100" as "DateTimeValue100",

            					            ca_long."Value1" as "LongValue1",
            					            ca_long."Value2" as "LongValue2",
            					            ca_long."Value3" as "LongValue3",
            					            ca_long."Value4" as "LongValue4",
            					            ca_long."Value5" as "LongValue5",
            					            ca_long."Value6" as "LongValue6",
            					            ca_long."Value7" as "LongValue7",
            					            ca_long."Value8" as "LongValue8",
            					            ca_long."Value9" as "LongValue9",
            					            ca_long."Value10" as "LongValue10",
            					            ca_long."Value11" as "LongValue11",
            					            ca_long."Value12" as "LongValue12",
            					            ca_long."Value13" as "LongValue13",
            					            ca_long."Value14" as "LongValue14",
            					            ca_long."Value15" as "LongValue15",
            					            ca_long."Value16" as "LongValue16",
            					            ca_long."Value17" as "LongValue17",
            					            ca_long."Value18" as "LongValue18",
            					            ca_long."Value19" as "LongValue19",
            					            ca_long."Value20" as "LongValue20",
            					            ca_long."Value21" as "LongValue21",
            					            ca_long."Value22" as "LongValue22",
            					            ca_long."Value23" as "LongValue23",
            					            ca_long."Value24" as "LongValue24",
            					            ca_long."Value25" as "LongValue25",
            					            ca_long."Value26" as "LongValue26",
            					            ca_long."Value27" as "LongValue27",
            					            ca_long."Value28" as "LongValue28",
            					            ca_long."Value29" as "LongValue29",
            					            ca_long."Value30" as "LongValue30",
            					            ca_long."Value31" as "LongValue31",
            					            ca_long."Value32" as "LongValue32",
            					            ca_long."Value33" as "LongValue33",
            					            ca_long."Value34" as "LongValue34",
            					            ca_long."Value35" as "LongValue35",
            					            ca_long."Value36" as "LongValue36",
            					            ca_long."Value37" as "LongValue37",
            					            ca_long."Value38" as "LongValue38",
            					            ca_long."Value39" as "LongValue39",
            					            ca_long."Value40" as "LongValue40",
            					            ca_long."Value41" as "LongValue41",
            					            ca_long."Value42" as "LongValue42",
            					            ca_long."Value43" as "LongValue43",
            					            ca_long."Value44" as "LongValue44",
            					            ca_long."Value45" as "LongValue45",
            					            ca_long."Value46" as "LongValue46",
            					            ca_long."Value47" as "LongValue47",
            					            ca_long."Value48" as "LongValue48",
            					            ca_long."Value49" as "LongValue49",
            					            ca_long."Value50" as "LongValue50",
            					            ca_long."Value51" as "LongValue51",
            					            ca_long."Value52" as "LongValue52",
            					            ca_long."Value53" as "LongValue53",
            					            ca_long."Value54" as "LongValue54",
            					            ca_long."Value55" as "LongValue55",
            					            ca_long."Value56" as "LongValue56",
            					            ca_long."Value57" as "LongValue57",
            					            ca_long."Value58" as "LongValue58",
            					            ca_long."Value59" as "LongValue59",
            					            ca_long."Value60" as "LongValue60",
            					            ca_long."Value61" as "LongValue61",
            					            ca_long."Value62" as "LongValue62",
            					            ca_long."Value63" as "LongValue63",
            					            ca_long."Value64" as "LongValue64",
            					            ca_long."Value65" as "LongValue65",
            					            ca_long."Value66" as "LongValue66",
            					            ca_long."Value67" as "LongValue67",
            					            ca_long."Value68" as "LongValue68",
            					            ca_long."Value69" as "LongValue69",
            					            ca_long."Value70" as "LongValue70",
            					            ca_long."Value71" as "LongValue71",
            					            ca_long."Value72" as "LongValue72",
            					            ca_long."Value73" as "LongValue73",
            					            ca_long."Value74" as "LongValue74",
            					            ca_long."Value75" as "LongValue75",
            					            ca_long."Value76" as "LongValue76",
            					            ca_long."Value77" as "LongValue77",
            					            ca_long."Value78" as "LongValue78",
            					            ca_long."Value79" as "LongValue79",
            					            ca_long."Value80" as "LongValue80",
            					            ca_long."Value81" as "LongValue81",
            					            ca_long."Value82" as "LongValue82",
            					            ca_long."Value83" as "LongValue83",
            					            ca_long."Value84" as "LongValue84",
            					            ca_long."Value85" as "LongValue85",
            					            ca_long."Value86" as "LongValue86",
            					            ca_long."Value87" as "LongValue87",
            					            ca_long."Value88" as "LongValue88",
            					            ca_long."Value89" as "LongValue89",
            					            ca_long."Value90" as "LongValue90",
            					            ca_long."Value91" as "LongValue91",
            					            ca_long."Value92" as "LongValue92",
            					            ca_long."Value93" as "LongValue93",
            					            ca_long."Value94" as "LongValue94",
            					            ca_long."Value95" as "LongValue95",
            					            ca_long."Value96" as "LongValue96",
            					            ca_long."Value97" as "LongValue97",
            					            ca_long."Value98" as "LongValue98",
            					            ca_long."Value99" as "LongValue99",
            					            ca_long."Value100" as "LongValue100",
            					            operatorgroup."Name",
            					            operatorgroup."Id",
        				                	cp."ContactPersonOrganization",
        				                	cp."ContactPersonNameOrOrganization",
        				                	cp."ContactPersonType",
        				                	ca_long."Value10" as "ReturnsFromRatingBotCount",
        				                	curators."FIO" as "ExecutorCuratorFio",
        				                	curators."ID" as "ExecutorCuratorId"
            			            FROM "CRPM_DATA"."Requests" r
            			            LEFT JOIN 
            			            (
            				            SELECT  
            					            ass."AssignedTo", 
            					            ass."RequestId", 
            					            OP.assto_name
            				            FROM "CRPM_DATA"."Assignments" ass
            				            join 
            				            (
            					            SELECT 
            						            "ID" as "Id", 
            						            op."FIO" as assto_name
            					            FROM "CALC"."INFRA_Operators_FIO" op
            				            ) OP 
            				            ON 
            					            op."Id" = ass."AssignedTo"
            				            WHERE 
            					            ass."RequestId" = req_id
            						            --order by ass."RequestId"
            			            ) ass 
            			            on  
            				            ass."RequestId" = r."Id"
            			            LEFT JOIN "CRPM_DATA"."REQUESTS_LAST_CHANGES_INFO" rlci 
            				            on 
            					            rlci."REQUEST_ID"=r."Id"
            			            LEFT JOIN "CALC"."INFRA_Operators_FIO" iof 
            				            on 
            					            rlci."STATUS_LAST_CHANGED_BY" = iof."LOGIN"
            			            LEFT JOIN "CALC"."CFG_RequestStates" rs 
            				            ON 
            					            rs."Id" = r."State"
            			            LEFT JOIN "CALC"."CFG_RequestStateStatuses" rss 
            				            ON 
            					            rss."Id" = r."Status"
            			            LEFT JOIN "CALC"."INFRA_ServiceAreas" sa 
            				            ON 
            					            sa."Id" = r."BranchId"
            			            LEFT JOIN "CRPM_DATA"."REQUEST_CA_STRING" ca_str 
            				            on 
            					            ca_str."Id" = r."Id"
            			            LEFT JOIN "CRPM_DATA"."REQUEST_CA_LONG" ca_long 
            				            on 
            					            ca_long."Id" = r."Id"
            			            LEFT JOIN "CRPM_DATA"."REQUEST_CA_BOOL" ca_bool 
            				            on 
            					            ca_bool."Id" = r."Id"
            			            LEFT JOIN "CRPM_DATA"."REQUEST_CA_DATETIME" ca_datetime 
            				            on 
            					            ca_datetime."Id" = r."Id"
            			            LEFT JOIN "CRPM_DATA"."REQUEST_CA_GUID" ca_guid 
            				            on 
            					            ca_guid."Id"  = r."Id"
            			            LEFT JOIN
            			            (
            				            SELECT  
            					            "ParentId", 
            					            COUNT(1) "ChildCount"
            				            FROM "CRPM_DATA"."Requests"
            				            WHERE 
            					            "ParentId" IS NOT NULL
            				            GROUP BY 
            					            "ParentId"
            				            --ORDER BY "ParentId"
            			            ) c 
            			            ON 
            				            c."ParentId" = r."Id"
            			            LEFT JOIN 
            			            (
            				            SELECT
            					            CASE
            						            WHEN op3."Id" IS NULL THEN '???????'
            						            ELSE op3."FIO"
            					            END as exec_name,
            					            op3."ID" as "Id"
            				            FROM "CALC"."INFRA_Operators_FIO" op3
            			            ) exec_ 
            			            ON 
            				            exec_."Id"=r."ExecutorId"
            			            LEFT JOIN /*"CALC"."V_REQUEST_HT"*/
            			            "KPI"."REQUEST_KPI" h 
            			            ON h."Id" = r."Id"
            			            LEFT JOIN 
            			            (
            				            select 
            					            rtp."REQUEST_ID",
            					            max(rtp."OPERATOR_START_WORK") as time_distributed
            				            from "CALC"."V_REQUEST_HT" rtp
            				            WHERE 
            					            "REQUEST_ID" = req_id
            				            group by 
            					            "REQUEST_ID"
            			            ) last_distributed 
            				            on 
            					            last_distributed."REQUEST_ID" = r."Id"
            			            LEFT JOIN 
            			            (
        								SELECT cp_in."ID" as "ContactPersonID", 
        								    (
        								        CONCAT_WS(' ', 
        								        	COALESCE(NULLIF(TRIM(cp_in."LAST_NAME"), ''), ''), 
        								        	COALESCE(NULLIF(TRIM(cp_in."FIRST_NAME"), ''), ''), 
        								        	COALESCE(TRIM(cp_in."MIDDLE_NAME"), ''))
        								    ) AS "ContactPersonName",
        									cp_in."NAME" AS "ContactPersonOrganization",
        									NULL AS "ContactPersonNameOrOrganization",
        									cpcalong."Value1" AS "ContactPersonType"
        								from "UCMM_DATA"."CONTACT_PERSON" cp_in
        								JOIN "UCMM_DATA"."CONTACT_PERSON_CA_LONG" cpcalong ON cpcalong."ID" = cp_in."ID"
        								JOIN "UCMM_DATA"."CONTACT_PERSON_CA_STRING" cpcastring ON cpcastring."ID" = cp_in."ID"
            			            ) cp 
            			            on 
            				            r."OriginatorId"=cp."ContactPersonID"
            			            LEFT JOIN "UCMM_DATA"."CONTACT_PERSON_CA_STRING" cp_ca_str
            				            on 
            					            cp_ca_str."ID" = cp."ContactPersonID"
            			            LEFT JOIN 
            			            (
            				            SELECT 
            					            w."RequestId", 
            					            wse."ExpirationDate" AS "PostponeTime"
            				            FROM "CRPM_DATA"."Works" w
            				            INNER JOIN "CRPM_DATA"."WorkStateExpirations" wse ON w."Id" = wse."WorkId"
            				            WHERE w."State" = 1
            			            ) postpone 
            			            ON 
            				            postpone."RequestId" = r."Id"		
        				            LEFT JOIN
        				            (
        					            SELECT operatorgroup."Name", operatorgroup."Id", rq."Id" as "QueueId"
        					            FROM 
        						            "CRPM_CFG"."CustomAttributes" ca
        						            JOIN "CRPM_CFG"."RequestQueues" rq ON rq."Id" = ca."QueueId"
        						            JOIN "AWP_INFRA"."OperatorGroups" operatorgroup ON operatorgroup."Id"::character varying::text = ca."Value"::text
        					            WHERE 
        						            ca."Key" = 'Division'::text
        				            ) operatorgroup on operatorgroup."QueueId" = r."QueueId"																   	   
            			            LEFT JOIN "CRPM_DATA"."REQUEST_CA_GUID" curator_ca
					                    ON r."Id" = curator_ca."Id"
            			            LEFT JOIN "CALC"."INFRA_Operators_FIO" curators
					                    ON curators."ID" = curator_ca."Value3"			
            			            WHERE 
            				            r."Id" = req_id
            			            AND
            			            (
            				            (is_closed = false AND r."TimeClosed" IS NULL)
            				            OR
            				            (is_closed = true AND r."TimeClosed" IS NOT NULL)
            			            )	
            		            );
EXCEPTION WHEN OTHERS THEN
            		            CALL "CALC"."P_WRITE_LOG"(SQLERRM || ', RequestId = ' || req_id, 'Error', 'P_INSERT_SINGLE_BULK_REQUESTSVIEW');
END;
            	            --COMMIT;		
END;

    $procedure$
;

END $EF$;
