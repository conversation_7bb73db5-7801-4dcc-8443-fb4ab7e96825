--- Удаляем все что касается отчетов Pentaho
DO $func$ 
DECLARE 
    app_id UUID;
BEGIN
	SELECT "Id" INTO app_id FROM "AWP_INFRA"."Applications" WHERE "Name" = 'Отчеты' LIMIT 1;
    
	IF app_id IS NOT NULL THEN
		DELETE FROM "AWP_INFRA"."WebAppProxySettings" 
		WHERE "WebApplicationId" = app_id;

		DELETE FROM "AWP_INFRA"."LoginWorkflows" 
		WHERE "ApplicationId" = app_id;

		DELETE FROM "AWP_INFRA"."ApplicationUserRole" 
		WHERE "ApplicationId" = app_id;

		DELETE FROM "AWP_INFRA"."WebClientAppInits" 
		WHERE "Id" = app_id;

		DELETE FROM "AWP_INFRA"."AppInit_Web" 
		WHERE "Id" = app_id;

		DELETE FROM "AWP_INFRA"."ApplicationInitializations" 
		WHERE "Id" = app_id;

		DELETE FROM "AWP_INFRA"."Applications" 
		WHERE "Id" = app_id;
	ELSE
        RAISE NOTICE 'Приложение ''Отчеты'' не найдено';
    END IF;
END $func$;

--- Переименовываем и включаем Superset
UPDATE "AWP_INFRA"."Applications"
SET "Name" = 'Отчеты' , "Disabled" = false
WHERE "Name" = 'Отчёты Superset'
