DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "KPI"."__KpiServiceDbContextMigrations" WHERE "MigrationId" = '20241028201130_AddRedirectStateMachine') THEN
    CREATE TABLE "KPI"."REDIRECT_REQUEST_STATE" (
        "CorrelationId" uuid NOT NULL,
        "CurrentState" integer NOT NULL,
        "EventTime" timestamp without time zone NOT NULL,
        "RequestId" bigint NOT NULL,
        "InitiateBy" uuid,
        "EventType" character varying(128) NOT NULL,
        "OldOperatorId" uuid,
        "NewOperatorId" uuid,
        "OldQueueId" smallint,
        "NewQueueId" smallint,
        CONSTRAINT "PK_REDIRECT_REQUEST_STATE" PRIMARY KEY ("CorrelationId")
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "KPI"."__KpiServiceDbContextMigrations" WHERE "MigrationId" = '20241028201130_AddRedirectStateMachine') THEN
    CREATE INDEX "IX_REDIRECT_REQUEST_STATE_EventType" ON "KPI"."REDIRECT_REQUEST_STATE" ("EventType");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "KPI"."__KpiServiceDbContextMigrations" WHERE "MigrationId" = '20241028201130_AddRedirectStateMachine') THEN
    INSERT INTO "KPI"."__KpiServiceDbContextMigrations" ("MigrationId", "ProductVersion")
    VALUES ('20241028201130_AddRedirectStateMachine', '8.0.2');
    END IF;
END $EF$;
