DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__SendMessageDbContextMigrations" WHERE "MigrationId" = '20250123204130_AddCorrectRowVersion') THEN
    ALTER TABLE "SEND_MESSAGE_STATE" DROP COLUMN "RowVersion";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__SendMessageDbContextMigrations" WHERE "MigrationId" = '20250123204130_AddCorrectRowVersion') THEN
    ALTER TABLE "SEND_MESSAGE_STATE" ALTER COLUMN "TimeSent" TYPE timestamp with time zone;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__SendMessageDbContextMigrations" WHERE "MigrationId" = '20250123204130_AddCorrectRowVersion') THEN
    ALTER TABLE "SEND_MESSAGE_STATE" ALTER COLUMN "TimeRequestRegistered" TYPE timestamp with time zone;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__SendMessageDbContextMigrations" WHERE "MigrationId" = '20250123204130_AddCorrectRowVersion') THEN
    ALTER TABLE "SEND_MESSAGE_STATE" ALTER COLUMN "TimeRegistered" TYPE timestamp with time zone;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__SendMessageDbContextMigrations" WHERE "MigrationId" = '20250123204130_AddCorrectRowVersion') THEN
    INSERT INTO "__SendMessageDbContextMigrations" ("MigrationId", "ProductVersion")
    VALUES ('20250123204130_AddCorrectRowVersion', '9.0.1');
    END IF;
END $EF$;

