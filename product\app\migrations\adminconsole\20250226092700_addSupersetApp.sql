INSERT INTO "AWP_INFRA"."Applications"(
	"Id", "Name", "Type", "SortOrder", "EnableAutoSignOn", "Disabled", "CredentialsConfigurationName", "IsService", "ManualStart", "LaunchOrder", "ToolbarImage", "ClientTypes", "LastChangeTime")
SELECT '43e74634-ff65-444c-981d-ececd48f97a9','Отчеты Superset',9,101,false,true,'Superset',false,false,0,null,2,NOW()
WHERE NOT EXISTS (
    SELECT 1 FROM "AWP_INFRA"."Applications" WHERE "Id" = '43e74634-ff65-444c-981d-ececd48f97a9'
);

INSERT INTO "AWP_INFRA"."ApplicationInitializations"(
	"Id", "DisplayGroup", "AdapterInitialization", "Adapter_Assembly", "Adapter_TypeName", "StartAsync", "Expander_WrapInExpander", "Expander_InitiallyExpanded", "Expander_Header", "Expander_ExpandDirection", "Expander_ExpandDisplayMode", "SizeSettings_Width", "SizeSettings_Height", "SizeSettings_MinWidth", "SizeSettings_MinHeight", "SizeSettings_MaxWidth", "SizeSettings_MaxHeight", "HideInTaskbar", "HideOnStartup", "LastChangeTime")
SELECT '43e74634-ff65-444c-981d-ececd48f97a9','mainPanel',null,null,null,true,false,false,null,0,0,null,null,null,null,null,null,false,false,NOW()
WHERE NOT EXISTS (
    SELECT 1 FROM "AWP_INFRA"."ApplicationInitializations" WHERE "Id" = '43e74634-ff65-444c-981d-ececd48f97a9'
);

INSERT INTO "AWP_INFRA"."AppInit_Web"(
	"Id", "StartUrl", "SuppressScriptErrors")
SELECT '43e74634-ff65-444c-981d-ececd48f97a9','https://{dbTransform:supersetserver}',false
WHERE NOT EXISTS (
    SELECT 1 FROM "AWP_INFRA"."AppInit_Web" WHERE "Id" = '43e74634-ff65-444c-981d-ececd48f97a9'
);

INSERT INTO "AWP_INFRA"."WebClientAppInits"(
	"Id", "DisplayGroup", "ComponentInit", "ComponentName", "LastChangeTime")
SELECT '43e74634-ff65-444c-981d-ececd48f97a9','mainPanel','{ "iframeAllow": "true"}','WebPage',NOW()
WHERE NOT EXISTS (
    SELECT 1 FROM "AWP_INFRA"."WebClientAppInits" WHERE "Id" = '43e74634-ff65-444c-981d-ececd48f97a9'
);
	
INSERT INTO "AWP_INFRA"."ApplicationUserRole"(
	"ApplicationId", "UserRoleId")
SELECT '43e74634-ff65-444c-981d-ececd48f97a9','7a4d1161-0b2a-4626-9d52-13ae32dddbe4'
WHERE NOT EXISTS (
    SELECT 1 FROM "AWP_INFRA"."ApplicationUserRole" 
    WHERE "ApplicationId" = '43e74634-ff65-444c-981d-ececd48f97a9' AND "UserRoleId" = '7a4d1161-0b2a-4626-9d52-13ae32dddbe4'
);