DO $EF$
DECLARE outboundRequestsQueueId smallint;
DECLARE operatorProcessingStateId smallint;
DECLARE outboundRequestsProcessingStatusId smallint;
DECLARE waitForOperatorStatusId smallint;
BEGIN

INSERT INTO "CRPM_CFG"."RequestQueues"(
	"Title", "Description", "IsDefault", "RequestStreamId", "Code", "IsDeleted")
	VALUES (N'Исходящие обращения', N'Очередь, в которую регистрируютя исходящие обращения. Это техническая очередь, на неё НЕЛЬЗЯ подписывать операторов/группы', false, 1, N'OutboundRequests', false);

outboundRequestsQueueId := (select "Id" from "CRPM_CFG"."RequestQueues" where "Code" = N'OutboundRequests');

INSERT INTO "CRPM_CFG"."CustomAttributes"(
	"Id", "Key", "Value", "CampaignId", "QueueId")
	VALUES (uuid_generate_v4(), N'Queue.IsService', N'true', null, outboundRequestsQueueId);

operatorProcessingStateId := (select "Id" from "CRPM_CFG"."RequestStates" where "Code" = N'OPERATOR.PROCESSING');

INSERT INTO "CRPM_CFG"."RequestStateStatuses"(
	"Title", "StateId", "Code")
	VALUES (N'Обработка исходящего обращения', operatorProcessingStateId, N'OPERATOR.OUTBOUNDREQUEST');

outboundRequestsProcessingStatusId := (select "Id" from "CRPM_CFG"."RequestStateStatuses" where "Code" = N'OPERATOR.OUTBOUNDREQUEST');

waitForOperatorStatusId := (select "Id" from "CRPM_CFG"."RequestStateStatuses" where "Code" = N'OPERATOR.WAIT');

INSERT INTO "CRPM_CFG"."AvailableTargetStates"(
	"StateId", "TargetStatusId", "StatusId")
	VALUES (operatorProcessingStateId, waitForOperatorStatusId, outboundRequestsProcessingStatusId);

INSERT INTO "CRPM_CFG"."RequestEventHandlers"(
	"Title", "Configuration", "TypeCode", "IsEnabled", "EventType", "StatusId", "QueueId")
	VALUES (N'Выдача исходящего обращения оператору', N'<ProposeOutboundRequestToOperatorEventHandlerConfig />', N'ProposeOutboundRequestToOperatorEventHandler', true, N'WorkStatusChanged', outboundRequestsProcessingStatusId, outboundRequestsQueueId);

INSERT INTO "CRPM_CFG"."RequestEventHandlers"(
	"Title", "Configuration", "TypeCode", "IsEnabled", "EventType", "StateId", "QueueId")
	VALUES (N'Перемаршрутизация исходящего обращения', N'<ReinitializeOutboundRequestEventHandlerConfig />', N'ReinitializeOutboundRequestEventHandler', true, N'WorkStatusChanged', operatorProcessingStateId, outboundRequestsQueueId);

update "AWP_INFRA"."Configurations"
set "Value" = replace("Value", N'<StateBeforeAssignment>18</StateBeforeAssignment>', N'')
where "Name" = N'CreateOutboundRequestSettings';

update "AWP_INFRA"."Configurations"
set "Value" = replace("Value", N'<StatusBeforeAssignment>33</StatusBeforeAssignment>', N'')
where "Name" = N'CreateOutboundRequestSettings';

update "AWP_INFRA"."Configurations"
set "Value" = replace("Value", N'<StateAfterAssignment>2</StateAfterAssignment>', N'')
where "Name" = N'CreateOutboundRequestSettings';

update "AWP_INFRA"."Configurations"
set "Value" = replace("Value", N'<StatusAfterAssignment>11</StatusAfterAssignment>', N'')
where "Name" = N'CreateOutboundRequestSettings';

update "AWP_INFRA"."Configurations"
set "Value" = replace("Value", N'<AssignTimeoutMinutes>3</AssignTimeoutMinutes>', N'')
where "Name" = N'CreateOutboundRequestSettings';

update "AWP_INFRA"."Configurations"
set "Value" = replace("Value", N'<SchemeId>1</SchemeId>', N'')
where "Name" = N'CreateOutboundRequestSettings';

update "AWP_INFRA"."Configurations"
set "Value" = replace("Value", N'<StreamId>1</StreamId>', N'')
where "Name" = N'CreateOutboundRequestSettings';

update "AWP_INFRA"."Configurations"
set "Value" = replace("Value", N'<RequestType>1</RequestType>', N'')
where "Name" = N'CreateOutboundRequestSettings';

update "AWP_INFRA"."Configurations"
set "Value" = replace("Value", N'<RequestTypeCustomName>RequestType</RequestTypeCustomName>', N'')
where "Name" = N'CreateOutboundRequestSettings';

update "AWP_INFRA"."Configurations"
set "Value" = replace("Value", N'</ProcessingParallelismDegree>', N'</ProcessingParallelismDegree>
  <StreamId>1</StreamId>
  <SchemeId>1</SchemeId>
  <QueueCode>OutboundRequests</QueueCode>
  <StateCode>OPERATOR.PROCESSING</StateCode>
  <StatusCode>OPERATOR.OUTBOUNDREQUEST</StatusCode>')
where "Name" = N'CreateOutboundRequestSettings';
END $EF$;