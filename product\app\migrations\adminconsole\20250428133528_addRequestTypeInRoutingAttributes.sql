-- добавить (если такового нет) новый элемент в RoutingAttributes имеющегося json у настройки SettingsServiceConfiguration
UPDATE "AWP_INFRA"."Configurations"
SET "Value" =
        jsonb_pretty(
                CASE
                    WHEN EXISTS (
                        SELECT 1
                        FROM jsonb_array_elements("Value"::jsonb->'RoutingAttributes') AS elem
                        WHERE elem->>'Code' = 'RequestType'
                    ) THEN "Value"::jsonb
                    ELSE jsonb_set(
                            "Value"::jsonb,
                            '{RoutingAttributes}',
                            (
                                SELECT jsonb_agg(elem)
                                FROM (
                                         SELECT * FROM jsonb_array_elements("Value"::jsonb->'RoutingAttributes')
                                         UNION ALL
                                         SELECT
                                             '{
                                                 "Code": "RequestType",
                                                 "Field": "ValueLong",
                                                 "Values": [
                                                     {"Name": "Входящее", "Value": "0"},
                                                     {"Name": "Исходящее", "Value": "1"},
                                                     {"Name": "Обратная связь", "Value": "2"},
                                                     {"Name": "Урегулирование негатива", "Value": "3"}
                                                 ],
                                                 "DataType": "Number",
                                                 "IsCustom": true,
                                                 "RuleType": "Request",
                                                 "DisplayName": "Тип обращения",
                                                 "RightPartType": "List",
                                                 "ComparisonRules": {
                                                     "Equals": "Равно",
                                                     "NotEquals": "Не равно"
                                                      }
                                              }'::jsonb
                                     ) AS t(elem)
                            )
                         )
                    END
        )
WHERE "Name" = 'SettingsServiceConfiguration';