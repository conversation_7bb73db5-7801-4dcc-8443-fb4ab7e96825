INSERT INTO "CRPM_CFG"."RequestEventHandlers" ("RowVersion", "Title", "Configuration", "TypeCode", "IsEnabled", "EventType", "StreamId", "SchemeId", "StateId", "StatusId")
SELECT 638657945451038533, 'Статистика. Перевод обращения между операторами',
'<?xml version="1.0" encoding="utf-16"?>
<RequestAssignmentSetEventHandlerConfig />',
'RequestAssignmentSetEventHandler', true, 'RequestAssignmentSet', 1, NULL, NULL, NULL
WHERE NOT EXISTS(
        SELECT "Id" FROM "CRPM_CFG"."RequestEventHandlers" WHERE "TypeCode"='RequestAssignmentSetEventHandler' AND "EventType"='RequestAssignmentSet'
    );

INSERT INTO "CRPM_CFG"."RequestEventHandlers" ("RowVersion", "Title", "Configuration", "TypeCode", "IsEnabled", "EventType", "StreamId", "SchemeId", "StateId", "StatusId")
SELECT 638657945451038533, 'Статистика. Перевод обращения между очередями',
'<?xml version="1.0" encoding="utf-16"?>
<RequestQueueIdChangedEventHandlerConfig />',
'RequestQueueIdChangedEventHandler', true, 'RequestQueueIdChanged', 1, NULL, NULL, NULL
WHERE NOT EXISTS(
        SELECT "Id" FROM "CRPM_CFG"."RequestEventHandlers" WHERE "TypeCode"='RequestQueueIdChangedEventHandler' AND "EventType"='RequestQueueIdChanged'
    );

INSERT INTO "CRPM_CFG"."RequestEventHandlers" ("RowVersion", "Title", "Configuration", "TypeCode", "IsEnabled", "EventType", "StreamId", "SchemeId", "StateId", "StatusId")
SELECT 638657945379058166, 'Статистика. Перевод обращения оператором',
'<?xml version="1.0" encoding="utf-16"?>
<WorkStatusChangedEventHandlerConfig />',
'WorkStatusChangedEventHandler', true, 'WorkStatusChanged', 1, NULL, NULL, NULL
WHERE NOT EXISTS(
        SELECT "Id" FROM "CRPM_CFG"."RequestEventHandlers" WHERE "TypeCode"='WorkStatusChangedEventHandler' AND "EventType"='WorkStatusChanged'
    );