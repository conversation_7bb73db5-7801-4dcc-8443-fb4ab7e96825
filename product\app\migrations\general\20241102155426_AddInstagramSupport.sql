DO $EF$
BEGIN
    -- Добавить канал Instagram в UCMM
    INSERT INTO "UCMM_DATA"."CHANNEL" ("ID", "NAME", "TITLE") VALUES(32, 'Instagram', 'Instagram');

    -- Добавить канал Instagram в каналы в выключенном состоянии
    INSERT INTO "CHANNEL_CFG"."Channels"("Id", "WorkMode", "UpdateDelay", "TypeCode", "IsEnabled", "Title", "Configuration")
    VALUES(20, 2, 1, 'InstagramChannel', false, 'Instagram', 
    '<?xml version="1.0" encoding="utf-16"?>
    <ExternalChannelConfig>
      <QueueName>IncomingMessages.Instagram</QueueName>
    </ExternalChannelConfig>');

    -- Добавить Input адаптер для Instagram в выключенном состоянии
    INSERT INTO "CHANNEL_CFG"."RequestMessageAdapters"
    ("Id", "ChannelId", "RequestStreamId", "ChannelToSet", "TypeCode", "IsEnabled", "Title", "Configuration")
    VALUES(23, 20, 1, 'CHANNEL.INSTAGRAM', 'InstagramInputAdapter', false, 'InstagramInputAdapter', 
    '<?xml version="1.0" encoding="utf-16"?>
    <ExternalInputAdapterConfig DefaultPriority="0" StreamId="1" ExternalSystemCode="product" RegisterCrpmEventOnNewMessageReceived="true" ReceivedByContactPersonIdToSet="00000000-0000-0000-0000-000000000000" ReflectNullAsOriginatorIdIfMoreThanOneContactForFromAddress="true">
      <Channel>CHANNEL.INSTAGRAM</Channel>
      <TypeCode>InstagramMessage</TypeCode>
      <ChannelId>32</ChannelId>
      <MessageSubject>Instagram обращение</MessageSubject>
    </ExternalInputAdapterConfig>');

    -- Добавить Input адаптер для Instagram в выключенном состоянии
    UPDATE "AWP_INFRA"."Configurations"
    SET "Value"='<?xml version="1.0" encoding="utf-16"?>
    <ChannelMappingSetting>
      <ChannelMappings>
        <MappingSetting SourceName="CHANNEL.CHAT" DisplayName="Чат" UcmmId="21" UcmmName="Chat" CanEditMessages="true" CanDeleteMessages="true" Mask="" Regex="" InternalTypeName="Product.ClientComponents.DataProviders.Common.Models.Channels.ChannelChat,Product.ClientComponents.DataProviders.Common" NotificationMessageType="Chat" />
        <MappingSetting SourceName="CHANNEL.SMS" DisplayName="Смс" UcmmId="41" UcmmName="Sms" Mask="7\**********" Regex="^((8|\+7)[\- ]?)?(\(?\d{3}\)?[\- ]?)?[\d\- ]{7,10}$" InternalTypeName="Product.ClientComponents.DataProviders.Common.Models.Channels.ChannelSms,Product.ClientComponents.DataProviders.Common" NotificationMessageType="Sms" />
        <MappingSetting SourceName="CHANNEL.EXCHANGE" DisplayName="Почта" UcmmId="1" UcmmName="Exchange" Mask="" Regex="" InternalTypeName="Product.ClientComponents.DataProviders.Common.Models.Channels.ChannelExchange,Product.ClientComponents.DataProviders.Common" NotificationMessageType="Exchange" />
        <MappingSetting SourceName="CHANNEL.FEEDBACKFORM" DisplayName="Форма" UcmmId="1" UcmmName="Exchange" Mask="" Regex="" InternalTypeName="Product.ClientComponents.DataProviders.Common.Models.Channels.ChannelExchange,Product.ClientComponents.DataProviders.Common" NotificationMessageType="Exchange" />
        <MappingSetting SourceName="CHANNEL.VOICE" DisplayName="Голос" UcmmId="61" UcmmName="Voice" Mask="" Regex="" InternalTypeName="Product.ClientComponents.DataProviders.Common.Models.Channels.ChannelVoice,Product.ClientComponents.DataProviders.Common" NotificationMessageType="Voice" />
        <MappingSetting SourceName="CHANNEL.BACKCALL" DisplayName="Обратный звонок" UcmmId="151" UcmmName="Voice" Regex="" Mask="" InternalTypeName="Product.ClientComponents.DataProviders.Common.Models.Channels.ChannelBackCall,Product.ClientComponents.DataProviders.Common" NotificationMessageType="BackCall" />
        <MappingSetting SourceName="CHANNEL.VIBER" DisplayName="Viber" UcmmId="71" UcmmName="Viber" Mask="" Regex="" InternalTypeName="Product.ClientComponents.DataProviders.Common.Models.Channels.ChannelViber,Product.ClientComponents.DataProviders.Common" NotificationMessageType="Viber" />
        <MappingSetting SourceName="CHANNEL.VK" DisplayName="VK" UcmmId="81" UcmmName="VK" CanEditMessages="true" CanDeleteMessages="true" Mask="" Regex="" InternalTypeName="Product.ClientComponents.DataProviders.Common.Models.Channels.ChannelVK,Product.ClientComponents.DataProviders.Common" NotificationMessageType="Vk" />
        <MappingSetting SourceName="CHANNEL.VKPUBLIC" DisplayName="VK Public" UcmmId="121" UcmmName="VKPublic" Mask="" Regex="" InternalTypeName="Product.ClientComponents.DataProviders.Common.Models.Channels.ChannelVKPublic,Product.ClientComponents.DataProviders.Common" NotificationMessageType="VkPublic" />
        <MappingSetting SourceName="CHANNEL.TELEGRAM" DisplayName="Telegram" UcmmId="91" UcmmName="Telegram" CanEditMessages="true" CanDeleteMessages="true" Mask="" Regex="" InternalTypeName="Product.ClientComponents.DataProviders.Common.Models.Channels.ChannelTelegram,Product.ClientComponents.DataProviders.Common" NotificationMessageType="Telegram" />
        <MappingSetting SourceName="CHANNEL.FACEBOOK" DisplayName="Facebook" UcmmId="31" UcmmName="Facebook" Mask="" Regex="" InternalTypeName="Product.ClientComponents.DataProviders.Common.Models.Channels.ChannelFacebook,Product.ClientComponents.DataProviders.Common" NotificationMessageType="Facebook" />
        <!-- <MappingSetting SourceName="CHANNEL.INSTAGRAM" DisplayName="Instagram" UcmmId="32" UcmmName="Instagram" Mask="" Regex="" InternalTypeName="Product.ClientComponents.DataProviders.Common.Models.Channels.ChannelInstagram,Product.ClientComponents.DataProviders.Common" NotificationMessageType="Instagram" />  -->
    	<MappingSetting SourceName="CHANNEL.VIDEOCHAT" DisplayName="Video Chat" UcmmId="131" UcmmName="VideoChat" Mask="" Regex="" InternalTypeName="Product.ClientComponents.DataProviders.Common.Models.Channels.ChannelVideoChat,Product.ClientComponents.DataProviders.Common" NotificationMessageType="VideoChat" />
        <MappingSetting SourceName="CHANNEL.DEFAULT" DisplayName="-" UcmmId="0" UcmmName="Default" Mask="" Regex="" InternalTypeName="Product.ClientComponents.DataProviders.Common.Models.Channels.ChannelDefault,Product.ClientComponents.DataProviders.Common" NotificationMessageType="Undefined" />
      </ChannelMappings>
    </ChannelMappingSetting>'
    WHERE "Name"='ChannelMappingSetting';
END $EF$;
