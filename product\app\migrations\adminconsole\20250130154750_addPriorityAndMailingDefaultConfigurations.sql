CREATE OR REPLACE FUNCTION "AWP_INFRA"."add_element_in_config_array_if_not_exist"(
	root_element TEXT,
	unique_key TEXT,
    new_elements JSONB[]
) RETURNS VOID AS $function$
DECLARE
existing_count INTEGER;
    elem JSONB;
BEGIN
    FOREACH elem IN ARRAY new_elements
    LOOP
        -- Проверка, существует ли элемент с таким же уникальным ключом
SELECT COUNT(*)
INTO existing_count
FROM (
         SELECT jsonb_array_elements("Value"::jsonb -> root_element) AS existing_elem
         FROM "AWP_INFRA"."Configurations"
         WHERE "Name" = 'AdministrationServiceConfiguration'
     )
WHERE existing_elem->>unique_key = elem->>unique_key;

-- Если элемента не существует, выполняем добавление
IF existing_count = 0 THEN
UPDATE "AWP_INFRA"."Configurations"
SET "Value" = jsonb_pretty(jsonb_set(
        "Value"::jsonb,
        string_to_array(root_element, '.'),
        COALESCE(
                ("Value"::jsonb -> root_element) || jsonb_build_array(elem),	-- Добавляем новый элемент
                jsonb_build_array(elem)											-- Если root_element отсутствует, создаем новый массив
        ),
        true))          														-- Добавляем элемент, если путь не существует
                           
WHERE "Name" = 'AdministrationServiceConfiguration';
END IF;
END LOOP;
END;
$function$ LANGUAGE plpgsql;

SELECT "AWP_INFRA"."add_element_in_config_array_if_not_exist"(
               'PrioritizationRuleAttributes',
               'Code',
               ARRAY[
                   '{
                     "Code": "Channel",
                     "DisplayName": "Канал",
                     "IsCustom": false,
                     "Field": "ValueString",
                     "DataType": "String",
                     "RightPartType": "List"
                   }'::jsonb,
               '{
                 "Code": "CustomerType",
                 "DisplayName": "Тип Клиента",
                 "IsCustom": true,
                 "Field": "ValueString",
                 "DataType": "String",
                 "RightPartType": "List",
                 "Values": [
                   {
                     "Name": "B2C",
                     "Value": "B2C"
                   },
                   {
                     "Name": "B2B",
                     "Value": "B2B"
                   }
                 ]
               }'::jsonb,
               '{
                 "Code": "Originator",
                 "DisplayName": "Отправитель",
                 "IsCustom": false,
                 "Field": "ValueString",
                 "DataType": "String",
                 "RightPartType": "String"
               }'::jsonb,
               '{
                 "Code": "Source",
                 "DisplayName": "Получатель",
                 "IsCustom": false,
                 "Field": "ValueString",
                 "DataType": "String",
                 "RightPartType": "String"
               }'::jsonb,
               '{
                 "Code": "RepeatedRequestId",
                 "DisplayName": "Повторное",
                 "IsCustom": false,
                 "Field": "ValueString",
                 "DataType": "Number",
                 "RightPartType": "String"
               }'::jsonb
    ]
       );

SELECT "AWP_INFRA"."add_element_in_config_array_if_not_exist"(
               'MailingsAttributes',
               'Type',
               ARRAY[
                   '{
                     "Type": "Asa",
                     "Alias": "ASA",
                     "ContentType": "Seconds",
                     "StringFormat": "hh:mm:ss"
                   }'::jsonb,
               '{
                 "Type": "OpenedRequests",
                 "Alias": "Кол-во обращений",
                 "ContentType": "Numeric",
                 "StringFormat": ""
               }'::jsonb,
               '{
                 "Type": "EscalatedRequests",
                 "Alias": "Просроченные обращения (по распределению)",
                 "ContentType": "Numeric",
                 "StringFormat": ""
               }'::jsonb,
               '{
                 "Type": "EscalatedRequestsCount",
                 "Alias": "Кол-во просроченных обращений (по распределению)",
                 "ContentType": "Numeric",
                 "StringFormat": ""
               }'::jsonb,
               '{
                 "Type": "EscalatedRequestsByDecision",
                 "Alias": "Просроченные обращения (по сроку решения)",
                 "ContentType": "Numeric",
                 "StringFormat": ""
               }'::jsonb,
               '{
                 "Type": "EscalatedRequestsByDecisionCount",
                 "Alias": "Кол-во просроченных обращений (по сроку решения)",
                 "ContentType": "Numeric",
                 "StringFormat": ""
               }'::jsonb
    ]
       );

DROP FUNCTION "AWP_INFRA".add_element_in_config_array_if_not_exist(text, text, _jsonb);