-- DROP FUNCTION "CALC".get_audit_info(int8);

CREATE OR REPLACE FUNCTION "CALC".get_audit_info(in_request_id bigint)
 RETURNS TABLE(change_date timestamp without time zone, login character varying, operation_name character varying, branch_id uuid, priority smallint, status smallint, executor_id uuid, queue_id smallint, assigned_to_id uuid)
 LANGUAGE plpgsql
AS $function$
BEGIN
	RETURN QUERY
	WITH ac AS 
	(
		SELECT
			"ASSIGNED_TO_ID",
			"CHANGE_DATE",
			"LOGIN",
			"OPERATION_NAME" 
		FROM
			(
				SELECT
					CASE 
						WHEN ac."DELETE_AUDIT_ID" IS NULL THEN "ASSIGNED_TO_ID" 
						ELSE CAST('00009E62F82EA0000D9F47469D000001' AS uuid) 
					END AS "ASSIGNED_TO_ID",
					A."CHANGE_DATE",
					A."LOGIN",
					A."OPERATION_NAME" 
				FROM "CRPM_DATA_AUD"."ASSIGNMENT_CHANGES" ac
				JOIN "CRPM_DATA_AUD"."AUDITS" A 
				ON 
					A."ID" = COALESCE ( ac."INSERT_AUDIT_ID", ac."UPDATE_AUDIT_ID", ac."DELETE_AUDIT_ID" ) 
				WHERE
					ac."REQUEST_ID" = in_request_id 
			) sq
		GROUP BY
			"ASSIGNED_TO_ID",
			"CHANGE_DATE",
			"LOGIN",
			"OPERATION_NAME"  
	),

	rc AS 
	(
		SELECT 
			A."CHANGE_DATE",
			A."LOGIN",
			A."OPERATION_NAME",
			rc."BRANCH_ID",
			rc."PRIORITY",
			rc."STATUS",
			rc."EXECUTOR_ID",
			rc."QUEUE_ID" 
		FROM "CRPM_DATA_AUD"."REQUEST_CHANGES" rc
		JOIN "CRPM_DATA_AUD"."AUDITS" A 
		ON 
			A."ID" = COALESCE ( rc."INSERT_AUDIT_ID", rc."UPDATE_AUDIT_ID", rc."DELETE_AUDIT_ID" ) 
			AND 
			rc."REQUEST_ID" = in_request_id 
		GROUP BY
			A."CHANGE_DATE",
			A."LOGIN",
			A."OPERATION_NAME",
			rc."BRANCH_ID",
			rc."PRIORITY",
			rc."STATUS",
			rc."EXECUTOR_ID",
			rc."QUEUE_ID" 
	) 
		
	SELECT
		_change_date,
		_login,
		_operation_name,
		_branch_id,
		_PRIORITY,
		_status,
		_executor_id,
		_queue_id,
		CASE	
				WHEN _assigned_to_id = CAST('00009E62F82EA0000D9F47469D000001' AS UUID) THEN CAST (NULL AS uuid) 
				ELSE _assigned_to_id 
		END AS _assigned_to_id 
	FROM
	(
		SELECT 
			CAST (ttab."change_date" AS TIMESTAMP ( 6 ) ) AS _change_date,
			CAST (LAST_VALUE ( ttab.login ) OVER ( ORDER BY ttab.change_date ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW ) AS VARCHAR( 2000 )) AS _login,
			CAST (LAST_VALUE ( ttab.operation_name ) OVER ( ORDER BY ttab.change_date ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW) AS VARCHAR ( 2000 )) AS _operation_name,
			CAST (LAST_VALUE ( "BRANCH_ID" ) OVER ( ORDER BY ttab.change_date ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW ) AS UUID) AS _branch_id,
			CAST (MAX ( "PRIORITY" ) OVER ( ORDER BY ttab.change_date ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW ) AS SMALLINT) AS _PRIORITY,
			CAST (MAX ( "STATUS" ) OVER ( ORDER BY ttab.change_date ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW ) AS SMALLINT) AS _status,
			CAST (LAST_VALUE ( "EXECUTOR_ID" ) OVER ( ORDER BY ttab.change_date ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW ) AS UUID) AS _executor_id,
			CAST (MAX ( "QUEUE_ID" ) OVER ( ORDER BY ttab.change_date ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW ) AS SMALLINT) AS _queue_id,
			CAST (LAST_VALUE ( "ASSIGNED_TO_ID"  ) OVER ( ORDER BY ttab.change_date ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW ) AS UUID) AS _assigned_to_id 
		FROM
		( 
			SELECT 
				"CHANGE_DATE" AS change_date, 
				"OPERATION_NAME" AS operation_name, 
				"LOGIN" AS login
			FROM ac 

			UNION ALL 

			SELECT 
				"CHANGE_DATE" AS change_date, 
				"OPERATION_NAME" AS operation_name, 
				"LOGIN" AS login 
			FROM rc 
		) ttab
		LEFT JOIN ac 
		ON 
			ac."CHANGE_DATE" = ttab."change_date"
		LEFT JOIN rc 
		ON 
			rc."CHANGE_DATE" = ttab."change_date" 
		ORDER BY
			ttab."change_date" ASC 
	) sq; 
END
$function$
;
