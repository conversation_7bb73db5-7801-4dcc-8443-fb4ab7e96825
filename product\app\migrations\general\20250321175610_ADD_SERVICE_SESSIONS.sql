DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM pg_namespace WHERE nspname = 'SERVICE_SESSIONS') THEN
        CREATE SCHEMA "SERVICE_SESSIONS";
    END IF;
END $EF$;
CREATE TABLE IF NOT EXISTS "SERVICE_SESSIONS"."__Migrations_ServiceSessionsDbContext" (
    "MigrationId" character varying(150) NOT NULL,
    "ProductVersion" character varying(32) NOT NULL,
    CONSTRAINT "PK___Migrations_ServiceSessionsDbContext" PRIMARY KEY ("MigrationId")
);

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM pg_namespace WHERE nspname = 'SERVICE_SESSIONS') THEN
        CREATE SCHEMA "SERVICE_SESSIONS";
    END IF;
END $EF$;

CREATE TABLE "SERVICE_SESSIONS"."ServiceSessions" (
    "Id" uuid NOT NULL,
    "ServiceObjectTypeId" smallint NOT NULL,
    "ServiceObjectId" text NOT NULL,
    "RequestId" bigint,
    "ContactPersonId" uuid,
    "StartTime" timestamp with time zone NOT NULL,
    "CloseTime" timestamp with time zone,
    "Comment" text,
    "StartedBy" text NOT NULL,
    "ClosedBy" text,
    CONSTRAINT "PK_ServiceSessions" PRIMARY KEY ("Id")
);

CREATE INDEX "IX_ServiceSessions_ContactPersonId" ON "SERVICE_SESSIONS"."ServiceSessions" ("ContactPersonId");

CREATE INDEX "IX_ServiceSessions_RequestId" ON "SERVICE_SESSIONS"."ServiceSessions" ("RequestId");

CREATE INDEX "IX_ServiceSessions_ServiceObjectTypeId_ServiceObjectId" ON "SERVICE_SESSIONS"."ServiceSessions" ("ServiceObjectTypeId", "ServiceObjectId");

INSERT INTO "SERVICE_SESSIONS"."__Migrations_ServiceSessionsDbContext" ("MigrationId", "ProductVersion")
VALUES ('20250311120558_Initial', '9.0.2');

