#!/bin/bash

# Получаем токен сервисной УЗ
ACCESS_TOKEN=$(curl --location 'https://{transform:kcUrl}/realms/{transform:kcRealm}/protocol/openid-connect/token' \
--header 'Content-Type: application/x-www-form-urlencoded' \
--data-urlencode 'client_id=product.service' \
--data-urlencode 'grant_type=client_credentials' \
--data-urlencode 'client_secret=TMFVYLhrwwBQbYmGjK3zt53EYPzMiSha' \
--data-urlencode 'response_type=code'| jq -r .access_token) || exit 1

# Создаем пользователя #1
curl -X POST "https://{transform:kcUrl}/admin/realms/{transform:kcRealm}/users/" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "user1",
    "emailVerified": true,
    "email": "<EMAIL>",
    "enabled": true,
    "firstName": "#1",
    "lastName": "User"
  }' || exit 1

# Получаем ID пользователя #1
USER_ID=$(curl -X GET "https://{transform:kcUrl}/admin/realms/{transform:kcRealm}/users?username=user1" \
  -H "Authorization: Bearer $ACCESS_TOKEN" | jq -r '.[0].id') || exit 1

# Устанавливаем пароль пользователя #1
curl -X PUT "https://{transform:kcUrl}/admin/realms/{transform:kcRealm}/users/$USER_ID/reset-password" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "password",
    "value": "P@$sWord12",
    "temporary": true
  }' || exit 1

# Добавляем в группу product-users пользователя #1
curl -X PUT "https://{transform:kcUrl}/admin/realms/{transform:kcRealm}/users/$USER_ID/groups/acf981c2-6add-4160-9f97-610aad738cb9" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{}' || exit 1

# Создаем пользователя #2
curl -X POST "https://{transform:kcUrl}/admin/realms/{transform:kcRealm}/users/" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "user2",
    "emailVerified": true,
    "email": "<EMAIL>",
    "enabled": true,
    "firstName": "#2",
    "lastName": "User"
  }' || exit 1

# Получаем ID пользователя #2
USER_ID=$(curl -X GET "https://{transform:kcUrl}/admin/realms/{transform:kcRealm}/users?username=user2" \
  -H "Authorization: Bearer $ACCESS_TOKEN" | jq -r '.[0].id') || exit 1

# Устанавливаем пароль пользователя #2
curl -X PUT "https://{transform:kcUrl}/admin/realms/{transform:kcRealm}/users/$USER_ID/reset-password" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "password",
    "value": "P@$sWord12",
    "temporary": true
  }' || exit 1

# Добавляем в группу product-users пользователя #2
curl -X PUT "https://{transform:kcUrl}/admin/realms/{transform:kcRealm}/users/$USER_ID/groups/acf981c2-6add-4160-9f97-610aad738cb9" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{}' || exit 1

# Создаем пользователя #3
curl -X POST "https://{transform:kcUrl}/admin/realms/{transform:kcRealm}/users/" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "user3",
    "emailVerified": true,
    "email": "<EMAIL>",
    "enabled": true,
    "firstName": "#3",
    "lastName": "User"
  }' || exit 1

# Получаем ID пользователя #3
USER_ID=$(curl -X GET "https://{transform:kcUrl}/admin/realms/{transform:kcRealm}/users?username=user3" \
  -H "Authorization: Bearer $ACCESS_TOKEN" | jq -r '.[0].id') || exit 1

# Устанавливаем пароль пользователя #3
curl -X PUT "https://{transform:kcUrl}/admin/realms/{transform:kcRealm}/users/$USER_ID/reset-password" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "password",
    "value": "P@$sWord12",
    "temporary": true
  }' || exit 1
  
# Добавляем в группу product-users пользователя #3
curl -X PUT "https://{transform:kcUrl}/admin/realms/{transform:kcRealm}/users/$USER_ID/groups/acf981c2-6add-4160-9f97-610aad738cb9" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{}' || exit 1

# Создаем супервизора #1
curl -X POST "https://{transform:kcUrl}/admin/realms/{transform:kcRealm}/users/" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "supervisor1",
    "emailVerified": true,
    "email": "<EMAIL>",
    "enabled": true,
    "firstName": "#1",
    "lastName": "Supervisor"
  }' || exit 1

# Получаем ID супервизора #1
USER_ID=$(curl -X GET "https://{transform:kcUrl}/admin/realms/{transform:kcRealm}/users?username=supervisor1" \
  -H "Authorization: Bearer $ACCESS_TOKEN" | jq -r '.[0].id') || exit 1

# Устанавливаем пароль супервизора #1
curl -X PUT "https://{transform:kcUrl}/admin/realms/{transform:kcRealm}/users/$USER_ID/reset-password" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "password",
    "value": "P@$sWord12",
    "temporary": true
  }' || exit 1

# Добавляем в группы product-users, product-supervisors супервизора #1
curl -X PUT "https://{transform:kcUrl}/admin/realms/{transform:kcRealm}/users/$USER_ID/groups/acf981c2-6add-4160-9f97-610aad738cb9" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{}' || exit 1
curl -X PUT "https://{transform:kcUrl}/admin/realms/{transform:kcRealm}/users/$USER_ID/groups/dc2535c6-bbed-4814-b2b4-c7d2a237c500" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{}' || exit 1

# Создаем супервизора #2
curl -X POST "https://{transform:kcUrl}/admin/realms/{transform:kcRealm}/users/" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "supervisor2",
    "emailVerified": true,
    "email": "<EMAIL>",
    "enabled": true,
    "firstName": "#2",
    "lastName": "Supervisor"
  }' || exit 1

# Получаем ID супервизора #2
USER_ID=$(curl -X GET "https://{transform:kcUrl}/admin/realms/{transform:kcRealm}/users?username=supervisor2" \
  -H "Authorization: Bearer $ACCESS_TOKEN" | jq -r '.[0].id') || exit 1

# Устанавливаем пароль супервизора #2
curl -X PUT "https://{transform:kcUrl}/admin/realms/{transform:kcRealm}/users/$USER_ID/reset-password" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "password",
    "value": "P@$sWord12",
    "temporary": true
  }' || exit 1

# Добавляем в группы product-users, product-supervisors супервизора #2
curl -X PUT "https://{transform:kcUrl}/admin/realms/{transform:kcRealm}/users/$USER_ID/groups/acf981c2-6add-4160-9f97-610aad738cb9" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{}' || exit 1
curl -X PUT "https://{transform:kcUrl}/admin/realms/{transform:kcRealm}/users/$USER_ID/groups/dc2535c6-bbed-4814-b2b4-c7d2a237c500" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{}' || exit 1

# Создаем супервизора #3
curl -X POST "https://{transform:kcUrl}/admin/realms/{transform:kcRealm}/users/" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "supervisor3",
    "emailVerified": true,
    "email": "<EMAIL>",
    "enabled": true,
    "firstName": "#3",
    "lastName": "Supervisor"
  }' || exit 1

# Получаем ID супервизора #3
USER_ID=$(curl -X GET "https://{transform:kcUrl}/admin/realms/{transform:kcRealm}/users?username=supervisor3" \
  -H "Authorization: Bearer $ACCESS_TOKEN" | jq -r '.[0].id') || exit 1

# Устанавливаем пароль супервизора #3
curl -X PUT "https://{transform:kcUrl}/admin/realms/{transform:kcRealm}/users/$USER_ID/reset-password" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "password",
    "value": "P@$sWord12",
    "temporary": true
  }' || exit 1

# Добавляем в группы product-users, product-supervisors супервизора #3
curl -X PUT "https://{transform:kcUrl}/admin/realms/{transform:kcRealm}/users/$USER_ID/groups/acf981c2-6add-4160-9f97-610aad738cb9" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{}' || exit 1
curl -X PUT "https://{transform:kcUrl}/admin/realms/{transform:kcRealm}/users/$USER_ID/groups/dc2535c6-bbed-4814-b2b4-c7d2a237c500" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{}' || exit 1

# Создаем администратора #1
curl -X POST "https://{transform:kcUrl}/admin/realms/{transform:kcRealm}/users/" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin1",
    "emailVerified": true,
    "email": "<EMAIL>",
    "enabled": true,
    "firstName": "#1",
    "lastName": "Administrator"
  }' || exit 1

# Получаем ID администратора #1
USER_ID=$(curl -X GET "https://{transform:kcUrl}/admin/realms/{transform:kcRealm}/users?username=admin1" \
  -H "Authorization: Bearer $ACCESS_TOKEN" | jq -r '.[0].id') || exit 1

# Устанавливаем пароль администратора #1
curl -X PUT "https://{transform:kcUrl}/admin/realms/{transform:kcRealm}/users/$USER_ID/reset-password" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "password",
    "value": "P@$sWord12",
    "temporary": true
  }' || exit 1

# Добавляем в группы product-users, product-supervisors, product-admins администратора #1
curl -X PUT "https://{transform:kcUrl}/admin/realms/{transform:kcRealm}/users/$USER_ID/groups/acf981c2-6add-4160-9f97-610aad738cb9" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{}' || exit 1
curl -X PUT "https://{transform:kcUrl}/admin/realms/{transform:kcRealm}/users/$USER_ID/groups/dc2535c6-bbed-4814-b2b4-c7d2a237c500" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{}' || exit 1
curl -X PUT "https://{transform:kcUrl}/admin/realms/{transform:kcRealm}/users/$USER_ID/groups/ec8b41c1-c261-4422-80b5-9c52a8d312d0" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{}' || exit 1

# Создаем администратора #2
curl -X POST "https://{transform:kcUrl}/admin/realms/{transform:kcRealm}/users/" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin2",
    "emailVerified": true,
    "email": "<EMAIL>",
    "enabled": true,
    "firstName": "#2",
    "lastName": "Administrator"
  }' || exit 1

# Получаем ID администратора #2
USER_ID=$(curl -X GET "https://{transform:kcUrl}/admin/realms/{transform:kcRealm}/users?username=admin2" \
  -H "Authorization: Bearer $ACCESS_TOKEN" | jq -r '.[0].id') || exit 1

# Устанавливаем пароль администратора #2
curl -X PUT "https://{transform:kcUrl}/admin/realms/{transform:kcRealm}/users/$USER_ID/reset-password" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "password",
    "value": "P@$sWord12",
    "temporary": true
  }' || exit 1

# Добавляем в группы product-users, product-supervisors, product-admins администратора #2
curl -X PUT "https://{transform:kcUrl}/admin/realms/{transform:kcRealm}/users/$USER_ID/groups/acf981c2-6add-4160-9f97-610aad738cb9" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{}' || exit 1
curl -X PUT "https://{transform:kcUrl}/admin/realms/{transform:kcRealm}/users/$USER_ID/groups/dc2535c6-bbed-4814-b2b4-c7d2a237c500" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{}' || exit 1
curl -X PUT "https://{transform:kcUrl}/admin/realms/{transform:kcRealm}/users/$USER_ID/groups/ec8b41c1-c261-4422-80b5-9c52a8d312d0" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{}' || exit 1

# Создаем администратора #3
curl -X POST "https://{transform:kcUrl}/admin/realms/{transform:kcRealm}/users/" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin3",
    "emailVerified": true,
    "email": "<EMAIL>",
    "enabled": true,
    "firstName": "#3",
    "lastName": "Administrator"
  }' || exit 1

# Получаем ID администратора #3
USER_ID=$(curl -X GET "https://{transform:kcUrl}/admin/realms/{transform:kcRealm}/users?username=admin3" \
  -H "Authorization: Bearer $ACCESS_TOKEN" | jq -r '.[0].id') || exit 1

# Устанавливаем пароль администратора #3
curl -X PUT "https://{transform:kcUrl}/admin/realms/{transform:kcRealm}/users/$USER_ID/reset-password" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "password",
    "value": "P@$sWord12",
    "temporary": true
  }' || exit 1

# Добавляем в группы product-users, product-supervisors, product-admins администратора #3
curl -X PUT "https://{transform:kcUrl}/admin/realms/{transform:kcRealm}/users/$USER_ID/groups/acf981c2-6add-4160-9f97-610aad738cb9" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{}' || exit 1
curl -X PUT "https://{transform:kcUrl}/admin/realms/{transform:kcRealm}/users/$USER_ID/groups/dc2535c6-bbed-4814-b2b4-c7d2a237c500" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{}' || exit 1
curl -X PUT "https://{transform:kcUrl}/admin/realms/{transform:kcRealm}/users/$USER_ID/groups/ec8b41c1-c261-4422-80b5-9c52a8d312d0" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{}' || exit 1
exit 1