DELETE FROM "AWP_INFRA"."WebAppProxySettings" WHERE "WebApplicationId" = '96a835b8-d757-4c57-8aef-74dbede36836';
DELETE FROM "AWP_INFRA"."AppInit_Web" WHERE "Id" = '96a835b8-d757-4c57-8aef-74dbede36836';
DELETE FROM "AWP_INFRA"."ApplicationInitializations" WHERE "Id" = '96a835b8-d757-4c57-8aef-74dbede36836';
DELETE FROM "AWP_INFRA"."Applications" WHERE "Id" = '96a835b8-d757-4c57-8aef-74dbede36836';
DELETE FROM "AWP_INFRA"."ApplicationUserRole" WHERE "ApplicationId" = '96a835b8-d757-4c57-8aef-74dbede36836';

DO $EF$
DECLARE
    v_initialization jsonb;
    v_has_suspended boolean;
BEGIN
    -- Выбираем значение поля Initialization
    SELECT ("Initialization"::jsonb) INTO v_initialization
    FROM "AWP_INFRA"."Modules"
    WHERE "Name" = 'WorkExecutionModule'
    FOR UPDATE;

    -- Проверяем, есть ли значение 'Suspended' в массиве
    SELECT EXISTS (
        SELECT 1
        FROM jsonb_array_elements_text(v_initialization->'stopGetWorkInPendingStatuses') AS status
        WHERE status = 'Suspended'
    ) INTO v_has_suspended;

    -- Если значение отсутствует, добавляем его
    IF NOT v_has_suspended THEN
        UPDATE "AWP_INFRA"."Modules"
        SET "Initialization" = jsonb_set(
            "Initialization"::jsonb,
            '{stopGetWorkInPendingStatuses}',
            ("Initialization"::jsonb->'stopGetWorkInPendingStatuses') || '"Suspended"'::jsonb
        )
        WHERE "Name" = 'WorkExecutionModule';
    END IF;
END $EF$;