SET SESSION my.vars.id = '9244dc42-8f74-4765-91a8-8c86416a9ccb'; -- current_setting('my.vars.id')::uuid
SET SESSION my.vars.name = 'Панель статусов оператора'; -- current_setting('my.vars.name')::text
SET SESSION my.vars.component = 'OperatorStatusPanelModule'; -- current_setting('my.vars.component')::text
SET SESSION my.vars.displayGroup = 'HP-RIGHT-1'; -- current_setting('my.vars.displayGroup')::text

INSERT INTO "AWP_INFRA"."Applications" (
  "Id",
  "Name",
  "Type",
  "SortOrder",
  "EnableAutoSignOn",
  "Disabled",
  "IsService",
  "ManualStart",
  "LaunchOrder",
  "ClientTypes"
)
SELECT
  current_setting('my.vars.id')::uuid as "Id",
  current_setting('my.vars.name')::text as "Name",
  1 as "Type",
  0 as "SortOrder",
  false as "EnableAutoSignOn",
  false as "Disabled",
  false as "IsService",
  false as "ManualStart",
  0 as "LaunchOrder",
  2 as "ClientTypes"
WHERE
  NOT EXISTS (
    SELECT "Id" FROM "AWP_INFRA"."Applications" WHERE "Name"=current_setting('my.vars.name')::text
  );


INSERT INTO "AWP_INFRA"."ApplicationUserRole" (
  "ApplicationId",
  "UserRoleId"
)
  SELECT
    current_setting('my.vars.id')::uuid as "ApplicationId",
    u."Id" as "UserRoleId"
  FROM "AWP_INFRA"."UserRoles" u
  WHERE
    NOT EXISTS (
      SELECT "ApplicationId" FROM "AWP_INFRA"."ApplicationUserRole"
      WHERE "ApplicationId"=current_setting('my.vars.id')::uuid
    );


INSERT INTO "AWP_INFRA"."ApplicationInitializations"(
  "Id",
  "DisplayGroup",
  "AdapterInitialization",
  "Adapter_Assembly",
  "Adapter_TypeName",
  "StartAsync",
  "Expander_WrapInExpander",
  "Expander_InitiallyExpanded",
  "Expander_Header",
  "Expander_ExpandDirection",
  "Expander_ExpandDisplayMode",
  "HideInTaskbar",
  "HideOnStartup"
) 
SELECT
  current_setting('my.vars.id')::uuid as "Id",
  current_setting('my.vars.displayGroup')::text as "DisplayGroup",
  '' as "AdapterInitialization",
  '' as "Adapter_Assembly",
  '' as "Adapter_TypeName",
  true as "StartAsync",
  false as "Expander_WrapInExpander",
  false as "Expander_InitiallyExpanded",
  null as "Expander_Header",
  0 as "Expander_ExpandDirection",
  0 as "Expander_ExpandDisplayMode",
  true as "HideInTaskbar",
  true as "HideOnStartup"
WHERE
  NOT EXISTS (
    SELECT "Id" FROM "AWP_INFRA"."ApplicationInitializations"
    WHERE "Id"=current_setting('my.vars.id')::uuid
  );

INSERT INTO "AWP_INFRA"."AppInit_Web"(
	"Id",
  "StartUrl",
  "SuppressScriptErrors"
) 
  SELECT
    current_setting('my.vars.id')::uuid as "Id",
    '' as "StartUrl",
    false as "SuppressScriptErrors"
  WHERE
    NOT EXISTS (
      SELECT "Id" FROM "AWP_INFRA"."AppInit_Web"
      WHERE "Id"=current_setting('my.vars.id')::uuid
    );

INSERT INTO "AWP_INFRA"."WebClientAppInits"(
  "Id",
  "DisplayGroup",
  "ComponentInit",
  "ComponentName"
) 
  SELECT
    current_setting('my.vars.id')::uuid as "Id",
    current_setting('my.vars.displayGroup')::text as "DisplayGroup",
    '{"PendingStateCode": "InWork","ShowUnavailableStatuses": false,"ManualUnobtainableStatusCodes": ["Inactivity","Initial","Suspended","WorkInOtherClient"]}' as "ComponentInit",
    current_setting('my.vars.component')::text as "ComponentName"
  WHERE
    NOT EXISTS (
      SELECT "Id" FROM "AWP_INFRA"."WebClientAppInits"
      WHERE "Id"=current_setting('my.vars.id')::uuid
    );
