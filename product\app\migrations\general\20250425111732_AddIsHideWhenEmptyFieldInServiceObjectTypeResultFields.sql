DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "SERVICE_SESSIONS_CFG"."__ServiceSystemsConfigurationDbContext" WHERE "MigrationId" = '20250425111732_AddIsHideWhenEmptyFieldInServiceObjectTypeResultFields') THEN
    ALTER TABLE "SERVICE_SESSIONS_CFG"."ServiceObjectTypeResultFields" ADD "ShowIfEmpty" boolean NOT NULL DEFAULT FALSE;
    COMMENT ON COLUMN "SERVICE_SESSIONS_CFG"."ServiceObjectTypeResultFields"."ShowIfEmpty" IS 'Отображать поле когда значение пусто';
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "SERVICE_SESSIONS_CFG"."__ServiceSystemsConfigurationDbContext" WHERE "MigrationId" = '20250425111732_AddIsHideWhenEmptyFieldInServiceObjectTypeResultFields') THEN
    INSERT INTO "SERVICE_SESSIONS_CFG"."__ServiceSystemsConfigurationDbContext" ("MigrationId", "ProductVersion")
    VALUES ('20250425111732_AddIsHideWhenEmptyFieldInServiceObjectTypeResultFields', '9.0.4');
    END IF;
END $EF$;
