DO $EF$
    BEGIN
        IF NOT EXISTS(SELECT 1 FROM "__SendMessageDbContextMigrations" WHERE "MigrationId" = '20240828154921_AddErrorReason') THEN
            ALTER TABLE public."SEND_MESSAGE_STATE" ADD "ErrorReason" text NULL;
        END IF;
    END $EF$;

DO $EF$
    BEGIN
        IF NOT EXISTS(SELECT 1 FROM "__SendMessageDbContextMigrations" WHERE "MigrationId" = '20240828154921_AddErrorReason') THEN

            UPDATE "UCMM_CFG"."SIMPLE_SETTINGS"
            SET "CONFIGURATION"='<?xml version="1.0" encoding="utf-8"?>
	<CustomAttributesMap>
		<LongAttributes>
			<Mapping Code="MSGUID" Type="Long" Index="1" />
			<Mapping Code="SmsStatus" Type="Long" Index="2" />
			<Mapping Code="Rate" Type="Long" Index="3" />
			<Mapping Code="MessageStatus" Type="Long" Index="4" />
			<Mapping Code="PublicMessageType" Type="Long" Index="5" />
			<Mapping Code="Comments" Type="Long" Index="6" />
			<Mapping Code="Likes" Type="Long" Index="7" />
			<Mapping Code="Reposts" Type="Long" Index="8" />
			<Mapping Code="Views" Type="Long" Index="9" />
			<Mapping Code="SystemMessageType" Type="Long" Index="10" />
		</LongAttributes>
		<DecimalAttributes>
			<Mapping Code="Decimal" Type="Decimal" Index="1" />
		</DecimalAttributes>
		<DateTimeAttributes>
			<Mapping Code="SmsStatusChangeDate" Type="DateTime" Index="1" />
			<Mapping Code="TimeDelivered" Type="DateTime" Index="2" />
			<Mapping Code="TimeRead" Type="DateTime" Index="3" />
			<Mapping Code="TimeError" Type="DateTime" Index="4" />
		</DateTimeAttributes>
		<BoolAttributes>
			<Mapping Code="RateRequest" Type="Bool" Index="1" />
			<Mapping Code="IsRated" Type="Bool" Index="2" />
			<Mapping Code="IsHtml" Type="Bool" Index="3" />
		</BoolAttributes>
		<GuidAttributes>
			<Mapping Code="SenderId" Type="Guid" Index="2" />
		</GuidAttributes>
		<BinaryAttributes>
			<Mapping Code="ConversationIndex" Type="Binary" Index="1" />
		</BinaryAttributes>
		<StringAttributes>
			<Mapping Code="ConversationId" Type="String" Index="1" />
			<Mapping Code="ConversationTopic" Type="String" Index="2" />
			<Mapping Code="SenderType" Type="String" Index="3" />
			<Mapping Code="SenderName" Type="String" Index="4" />
			<Mapping Code="SenderLogin" Type="String" Index="5" />
			<Mapping Code="MSISDN" Type="String" Index="6" />
			<Mapping Code="ContactEmail" Type="String" Index="7" />
			<Mapping Code="ContactPhone" Type="String" Index="8" />
			<Mapping Code="ContactName" Type="String" Index="9" />
			<Mapping Code="RoomJid" Type="String" Index="10" />
			<Mapping Code="SenderJid" Type="String" Index="11" />
			<Mapping Code="IsAuthorized" Type="String" Index="12" />
			<Mapping Code="Nickname" Type="String" Index="13" />
			<Mapping Code="ChatSource" Type="String" Index="14" />
			<Mapping Code="IP" Type="String" Index="15" />
			<Mapping Code="Login" Type="String" Index="16" />
			<Mapping Code="SmscId" Type="String" Index="17" />
			<Mapping Code="RateComment" Type="String" Index="18" />
			<Mapping Code="ButtonContent" Type="String" Index="19" />
			<Mapping Code="Transcription" Type="String" Index="20" />
			<Mapping Code="GroupId" Type="String" Index="21" />
			<Mapping Code="ParentPostId" Type="String" Index="22" />
			<Mapping Code="ParentPostOwnerId" Type="String" Index="23" />
			<Mapping Code="ReceivingName" Type="String" Index="24" />
			<Mapping Code="Payload" Type="String" Index="25" />
			<Mapping Code="Buttons" Type="String" Index="26" />
			<Mapping Code="ImportanceCode" Type="String" Index="27" />
			<Mapping Code="NoteInfoPayload" Type="String" Index="28" />
			<Mapping Code="ErrorReason" Type="String" Index="29" />
		</StringAttributes>
		<Mappings></Mappings>
	</CustomAttributesMap>'
            WHERE "TITLE"='CustomAttributesMapForMessages';

        END IF;
    END $EF$;

DO $EF$
    BEGIN
        IF NOT EXISTS(SELECT 1 FROM "__SendMessageDbContextMigrations" WHERE "MigrationId" = '20240828154921_AddErrorReason') THEN
            INSERT INTO "__SendMessageDbContextMigrations" ("MigrationId", "ProductVersion")
            VALUES ('20240828154921_AddErrorReason', '8.0.4');
        END IF;
    END $EF$;