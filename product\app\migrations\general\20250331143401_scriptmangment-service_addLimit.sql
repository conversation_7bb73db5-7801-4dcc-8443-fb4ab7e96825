DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "SCRIPTS"."__EFMigrationsHistory" WHERE "MigrationId" = '20250326071942_AddLimit') THEN
    ALTER TABLE "SCRIPTS"."ScriptSteps" ADD "Limit" smallint;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "SCRIPTS"."__EFMigrationsHistory" WHERE "MigrationId" = '20250326071942_AddLimit') THEN
    ALTER TABLE "SCRIPTS"."ScriptSteps" ADD "LimitStepId" bigint;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "SCRIPTS"."__EFMigrationsHistory" WHERE "MigrationId" = '20250326071942_AddLimit') THEN
    ALTER TABLE "SCRIPTS"."ScriptRuns" ADD "CurrentStepPlayAttempts" integer NOT NULL DEFAULT 0;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "SCRIPTS"."__EFMigrationsHistory" WHERE "MigrationId" = '20250326071942_AddLimit') THEN
    CREATE INDEX "IX_ScriptSteps_LimitStepId" ON "SCRIPTS"."ScriptSteps" ("LimitStepId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "SCRIPTS"."__EFMigrationsHistory" WHERE "MigrationId" = '20250326071942_AddLimit') THEN
    ALTER TABLE "SCRIPTS"."ScriptSteps" ADD CONSTRAINT "FK_ScriptSteps_ScriptSteps_LimitStepId" FOREIGN KEY ("LimitStepId") REFERENCES "SCRIPTS"."ScriptSteps" ("Id");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "SCRIPTS"."__EFMigrationsHistory" WHERE "MigrationId" = '20250326071942_AddLimit') THEN
    INSERT INTO "SCRIPTS"."__EFMigrationsHistory" ("MigrationId", "ProductVersion")
    VALUES ('20250326071942_AddLimit', '9.0.1');
    END IF;
END $EF$;