-- Добавление нового обработчика для отправки сообщения о взятии в работу
INSERT INTO "CRPM_CFG"."RequestEventHandlers" ("Title", "Configuration", "TypeCode", "IsEnabled", "EventType", "StreamId", "SchemeId", "StateId", "StatusId", "BranchId", "CampaignId", "Channel", "QueueId")
SELECT 'Отправка сообщения о взятии в работу',
       '<?xml version="1.0" encoding="utf-16"?>
<InfoMessageEventHandlerConfig ServicesConfigName="ServicesConfiguration" SendMessageServiceKey="SendMessage" AutoReplyTemplatesAttributeCode="Queue.RequestInWorkAutoReplyWorker.ShouldSendAutoReply.Templates">
  <Channels>
    <SmsChannelConfiguration>
      <BotSenderName>Product bot</BotSenderName>
      <ChannelName>CHANNEL.SMS</ChannelName>
      <ChannelId>41</ChannelId>
    </SmsChannelConfiguration>
    <EmailChannelConfiguration>
      <BotSenderName>Product bot</BotSenderName>
      <ChannelName>CHANNEL.EXCHANGE</ChannelName>
      <ChannelId>1</ChannelId>
    </EmailChannelConfiguration>
    <EmailChannelConfiguration>
      <BotSenderName>Product bot</BotSenderName>
      <ChannelName>CHANNEL.FEEDBACKFORM</ChannelName>
      <ChannelId>1</ChannelId>
    </EmailChannelConfiguration>
    <WebChatChannelConfiguration>
      <BotSenderName>Product bot</BotSenderName>
      <Domain>conference.ejb.labs.shtorm</Domain>
      <ChannelName>CHANNEL.CHAT.</ChannelName>
      <ChannelId>210</ChannelId>
    </WebChatChannelConfiguration>
    <ChatChannelConfiguration>
      <BotSenderName>Product bot</BotSenderName>
      <ChannelName>CHANNEL.CHAT</ChannelName>
      <ChannelId>21</ChannelId>
    </ChatChannelConfiguration>
    <ViberChatChannelConfiguration>
      <BotSenderName>Product bot</BotSenderName>
      <ChannelName>CHANNEL.VIBER</ChannelName>
      <ChannelId>71</ChannelId>
    </ViberChatChannelConfiguration>
    <TelegramChatChannelConfiguration>
      <BotSenderName>Product bot</BotSenderName>
      <ChannelName>CHANNEL.TELEGRAM</ChannelName>
      <ChannelId>91</ChannelId>
    </TelegramChatChannelConfiguration>
    <VkChatChannelConfiguration>
      <BotSenderName>Product bot</BotSenderName>
      <ChannelName>CHANNEL.VK</ChannelName>
      <ChannelId>81</ChannelId>
    </VkChatChannelConfiguration>
    <FacebookChatChannelConfiguration>
      <BotSenderName>Product bot</BotSenderName>
      <ChannelName>CHANNEL.FACEBOOK</ChannelName>
      <ChannelId>31</ChannelId>
    </FacebookChatChannelConfiguration>
    <MfmsWhatsappChannelConfiguration>
      <BotSenderName>Product bot</BotSenderName>
      <ChannelName>CHANNEL.MFMS.WHATSAPP</ChannelName>
      <ChannelId>101</ChannelId>
    </MfmsWhatsappChannelConfiguration>
    <MfmsiMessageChannelConfiguration>
      <BotSenderName>Product bot</BotSenderName>
      <ChannelName>CHANNEL.MFMS.IMESSAGE</ChannelName>
      <ChannelId>51</ChannelId>
    </MfmsiMessageChannelConfiguration>
    <InfobipWhatsappChannelConfiguration>
      <BotSenderName>Product bot</BotSenderName>
      <ChannelName>CHANNEL.INFOBIP.WHATSAPP</ChannelName>
      <ChannelId>111</ChannelId>
    </InfobipWhatsappChannelConfiguration>
    <MFWhatsAppChannelConfiguration>
      <BotSenderName>Product bot</BotSenderName>
      <ChannelName>CHANNEL.MF.WHATSAPP</ChannelName>
      <ChannelId>141</ChannelId>
    </MFWhatsAppChannelConfiguration>
  </Channels>
  <QueueAttribute>Queue.RequestInWorkAutoReplyWorker.ShouldSendAutoReply</QueueAttribute>
</InfoMessageEventHandlerConfig>',
       'InfoMessageEventHandler',
       true,
       'WorkStatusChanged',
       1,
       NULL,
       (SELECT "Id" FROM "CRPM_CFG"."RequestStates" where "Code" = N'OPERATOR.PROCESSING'),
       NULL,
       NULL,
       NULL,
       NULL,
       NULL
    WHERE
NOT EXISTS (
  SELECT "Id" FROM "CRPM_CFG"."RequestEventHandlers"
  WHERE "TypeCode"='InfoMessageEventHandler' AND "EventType"='WorkStatusChanged'
);
