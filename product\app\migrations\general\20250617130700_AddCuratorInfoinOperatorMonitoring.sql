DO $EF$
BEGIN
DROP FUNCTION IF EXISTS "CALC"."F_MONITORING_OPERATORS"(smallint);

CREATE OR REPLACE FUNCTION "CALC"."F_MONITORING_OPERATORS"(_queueid smallint DEFAULT NULL::smallint, returnCuratorInfo boolean DEFAULT False)
 RETURNS TABLE("OPERATOR_ID" uuid, "FIO" text, "LOGIN" character varying, "CURATOR_ID" uuid, "CURATOR_FIO" character varying, "QUEUE_ID" smallint, "QUEUE_TITLE" text, "CURRENT_STATUS" text, "CURRENT_STATUS_CODE" text, "SECONDS_IN_CURRENT_STATUS" numeric, "MAX_SECONDS_IN_CURRENT_STATUS" numeric, "CLOSED_REQUESTS" bigint, "CLOSED_REQUESTS_LAST_HOUR" bigint, "PROCESSING_REQUESTS" bigint, "ASA" numeric, "ART" numeric, "ART_ALARM" numeric, "ART_WARNING" numeric, "MAX_RT" bigint, "RT_SUM" numeric, "RT_COUNT" bigint, "CALLS_MISSED_COUNT" bigint, "CALLS_MISSED_COUNT_LAST_HOUR" bigint, "AHT" numeric, "ACSI" numeric, "AHT_ALARM" numeric, "AHT_WARNING" numeric, "ACSI_ALARM" numeric, "ACSI_WARNING" NUMERIC, "OPERATOR_ROOT_GROUP_ID" uuid, "OPERATOR_ROOT_GROUP_NAME" text)
 LANGUAGE sql
AS $function$

WITH constants (_startDate, _endDate) as (
    values (timezone('UTC'::text, CURRENT_DATE::timestamp with time zone), timezone('UTC'::text, now()))
),

     v_operator_status_queue AS (
         SELECT
             ob."OPERATOR_ID" AS "OperatorId",
             queues."queueId" AS "QueueId",
             queues."title" as "Title",
             ob."STATUS",
             ob."SECONDS_IN_STATUS"
         FROM "CALC"."V_OPERATOR_STATUSES" ob
                  LEFT JOIN (
             SELECT
                 rq."Id" as "queueId",
                 erq."ExecutorId" AS operator_id,
                 rq."Title" as title
             FROM "CALC"."CFG_RequestQueues" rq
                      JOIN "CALC"."CFG_ExecutorRequestQueues" erq ON rq."Id" = erq."QueueId"
         ) queues ON ob."OPERATOR_ID" = queues.operator_id
     ),

     requests_closed_info AS (
         SELECT
             r_kpi."LastQueueId" AS "QueueId",
             r_kpi."ClosedById" AS "OperatorId",
             COUNT(r_kpi."Id") AS "CLOSED_COUNT",
             COUNT(r_kpi."Id") FILTER (WHERE r_kpi."TimeClosed" >= _endDate - '1 hour'::interval) AS "CLOSED_COUNT_LAST_HOUR"
         FROM constants, "KPI"."REQUEST_KPI" r_kpi
         WHERE r_kpi."TimeClosed" BETWEEN _startDate AND _endDate AND r_kpi."LastQueueId" = _queueId
         GROUP BY r_kpi."ClosedById", r_kpi."LastQueueId"
     ),

     requests_info AS (
         SELECT o_kpi."OperatorId",
                AVG(
                        CASE
                            WHEN (
                                o_kpi."ContactStartTime" BETWEEN _startDate AND _endDate
                                ) THEN o_kpi."HT"
                            ELSE NULL::BIGINT
                            END
                ) AS "AHT"
         FROM constants, "KPI"."OPERATOR_KPI" o_kpi
         WHERE o_kpi."QueueId" = _queueId AND o_kpi."ContactEndTime" BETWEEN _startDate AND _endDate
         GROUP BY o_kpi."OperatorId"
     ),

     requests_in_work AS (
         SELECT
             COUNT(wrk."RequestId") AS "ProccessingRequestsCount",
             wrk."ExecutorId"
         FROM "CRPM_DATA"."Works" wrk
                  JOIN "CRPM_DATA"."Requests" r ON wrk."RequestId" = r."Id" AND r."QueueId" = _queueId
                  JOIN "CRPM_CFG"."RequestStateStatuses" AS rss ON rss."Id" = r."Status" AND rss."Code"::text = 'OPERATOR.WORK'::character varying::text
         GROUP BY wrk."ExecutorId"
     ),

     requests_call_info AS (
         SELECT call_state."OperatorId" AS "OperatorId",
                COUNT(call_state."Id") FILTER (
                    WHERE
                    (call_state."Direction" IS NULL OR call_state."Direction" = 0)
                        AND call_state."TimeCallMissed" IS NOT NULL
                    ) "CALLS_MISSED_COUNT",
                COUNT(call_state."Id") FILTER (
                    WHERE
                    (call_state."Direction" IS NULL OR call_state."Direction" = 0)
                        AND call_state."TimeCallMissed" IS NOT NULL
                        AND call_state."TimeCallMissed" >= _enddate - '1 hour'::interval
                    ) "CALLS_MISSED_COUNT_LAST_HOUR"
         FROM constants, "KPI"."OPERATOR_CALL_STATE" call_state
                             JOIN "CRPM_DATA"."Requests" r ON call_state."RequestId" = r."Id" AND r."QueueId" = _queueId
         WHERE (
             call_state."TimeCallAccepted" BETWEEN _startDate AND _endDate
             )
            OR (
             call_state."TimeCallMissed" BETWEEN _startDate AND _endDate
             )
         GROUP BY call_state."OperatorId"
     ),

     csi_info_evaluation_pre AS (
         SELECT
             "RequestId",
             "OperatorId",
             "Score",
             RANK() OVER (PARTITION BY "RequestId" ORDER BY "Date" DESC) AS "Rank"
         FROM constants, "EXTERNAL_EVALUATIONS"."ExternalEvaluations"
         WHERE "Date" BETWEEN _startDate AND _endDate
     ),

     csi_info_evaluation_result AS (
         SELECT
             "RequestId",
             "OperatorId",
             "Score"
         FROM csi_info_evaluation_pre
         WHERE "Rank" = 1
     ),

     csi_info_rkpi_result AS (
         SELECT
             r_kpi."Id" AS "RequestId",
             r_kpi."ClosedById" AS "OperatorId",
             r_kpi."CSI" AS "Score"
         FROM constants, "KPI"."REQUEST_KPI" r_kpi
         WHERE
             r_kpi."ClosedById" IS NOT NULL
           AND r_kpi."TimeRegistered" BETWEEN _startDate AND _endDate
           AND r_kpi."LastQueueId" = _queueId
     ),

     requests_csi_info AS (
         SELECT
             r."OperatorId",
             AVG(COALESCE(e."Score", r."Score")) AS "ACSI"
         FROM csi_info_rkpi_result AS r
                  LEFT JOIN csi_info_evaluation_result AS e ON r."RequestId" = e."RequestId"
         GROUP BY r."OperatorId"
     ),

     operator_response_all AS (
         SELECT all_rt."OperatorId",
                AVG(all_rt."RT") AS "ART",
                MAX(all_rt."RT") AS "MAX_RT",
                SUM(all_rt."RT") AS "RT_SUM",
                count(
                        CASE
                            WHEN all_rt."RT" IS NULL
                                OR all_rt."RT" = 0 THEN NULL
                            ELSE all_rt."OperatorId"
                            END
                ) AS "RT_COUNT"
         FROM constants, "KPI"."OPERATOR_RESPONSE" all_rt
         WHERE all_rt."ReactionStartDate" BETWEEN _startDate AND _endDate AND all_rt."QueueId" = _queueId
         GROUP BY all_rt."OperatorId"
     ),
     
     operator_root_group AS (
         	SELECT operatorgroup."Name", operatorgroup."Id", rq."Id" as "QueueId"
    		FROM 
    		    "CRPM_CFG"."CustomAttributes" ca
    		    JOIN "CRPM_CFG"."RequestQueues" rq ON rq."Id" = ca."QueueId"
    		    JOIN "AWP_INFRA"."OperatorGroups" operatorgroup ON operatorgroup."Id"::character varying::text = ca."Value"::text
    		WHERE 
    		    ca."Key" = 'Division'::text),

    curator_custom as (
        select
            "OPERATOR_CUSTOM_ATTRIBUTES"."OPERATOR_ID",
            "OPERATOR_CUSTOM_ATTRIBUTES"."GUID_VALUE" as "CuratorId"
        from
            "AWP_INFRA"."OPERATOR_CUSTOM_ATTRIBUTES"
        where
            "OPERATOR_CUSTOM_ATTRIBUTES"."CODE"::text = 'CuratorId'::text
            )

SELECT
    operators."ID" AS "OPERATOR_ID",
    operators."FIO",
    operators."LOGIN",
    curators."ID" as "CURATOR_ID",
    curators."FIO" as "CURATOR_FIO",
    rq."Id" AS "QUEUE_ID",
    rq."Title" AS "QUEUE_TITLE",
    ob."STATUS" AS "CURRENT_STATUS",
    ob."STATUS_CODE" AS "CURRENT_STATUS_CODE",
    COALESCE(ob."SECONDS_IN_STATUS", 0::NUMERIC(10, 2)::DOUBLE PRECISION)::NUMERIC(10, 2) AS "SECONDS_IN_CURRENT_STATUS",
    0::numeric AS "MAX_SECONDS_IN_CURRENT_STATUS",
    COALESCE(requests_closed_info."CLOSED_COUNT", 0::BIGINT) AS "CLOSED_REQUESTS",
    COALESCE(requests_closed_info."CLOSED_COUNT_LAST_HOUR", 0::BIGINT) AS "CLOSED_REQUESTS_LAST_HOUR",
    COALESCE(requests_in_work."ProccessingRequestsCount", 0::BIGINT) AS "PROCESSING_REQUESTS",
    0::NUMERIC(10, 2) AS "ASA",
    COALESCE(operator_response_all."ART"::NUMERIC(15, 2), 0::NUMERIC) AS "ART",
    queueasa."AlarmART"::numeric AS "ART_ALARM",
    queueasa."WarningART"::numeric AS "ART_WARNING",
    COALESCE(operator_response_all."MAX_RT", 0::BIGINT) AS "MAX_RT",
    COALESCE(operator_response_all."RT_SUM", 0::NUMERIC)::NUMERIC(10, 2) AS "RT_SUM",
    COALESCE(operator_response_all."RT_COUNT"::BIGINT, 0::BIGINT) AS "RT_COUNT",
    COALESCE(requests_call_info."CALLS_MISSED_COUNT"::BIGINT, 0::BIGINT) AS "CALLS_MISSED_COUNT", -- Пропущено звонков (Входящих)
    COALESCE(requests_call_info."CALLS_MISSED_COUNT_LAST_HOUR"::BIGINT, 0::BIGINT) AS "CALLS_MISSED_COUNT_LAST_HOUR", -- Пропущено звонков (Входящих) за последний час
    COALESCE(requests_info."AHT", 0::NUMERIC)::NUMERIC(10, 2) AS "AHT",
    COALESCE(requests_csi_info."ACSI", 0::NUMERIC)::NUMERIC(10, 2) AS "ACSI",
    queueasa."AlarmAHT"::numeric AS "AHT_ALARM",
    queueasa."WarningAHT"::numeric AS "AHT_WARNING",
    queueasa."AlarmACSI"::numeric AS "ACSI_ALARM",
    queueasa."WarningACSI"::numeric AS "ACSI_WARNING",
    operator_root_group."Id" AS "OPERATOR_ROOT_GROUP_ID",
    operator_root_group."Name" AS "OPERATOR_ROOT_GROUP_NAME"
FROM
    "CALC"."INFRA_Operators_FIO" operators
        JOIN "CALC"."V_OPERATOR_STATUSES" ob ON ob."OPERATOR_ID" = operators."ID"
        JOIN "CALC"."CFG_RequestQueues" rq ON rq."Id" = _queueId
        LEFT JOIN requests_closed_info ON requests_closed_info."OperatorId" = operators."ID"
        LEFT JOIN requests_in_work ON requests_in_work."ExecutorId" = operators."ID"
        LEFT JOIN requests_call_info ON requests_call_info."OperatorId" = operators."ID"
        LEFT JOIN requests_info ON requests_info."OperatorId" = operators."ID"
        LEFT JOIN requests_csi_info ON requests_csi_info."OperatorId" = operators."ID"
        LEFT JOIN operator_response_all ON operator_response_all."OperatorId" = operators."ID"
        LEFT JOIN "CALC"."CFG_Queue_ASA" AS queueasa ON queueasa."Id" = _queueId
        LEFT JOIN v_operator_status_queue ON v_operator_status_queue."OperatorId" = operators."ID" AND v_operator_status_queue."QueueId" = _queueId
        LEFT JOIN operator_root_group ON operator_root_group."QueueId" = _queueId
        LEFT JOIN curator_custom on returnCuratorInfo AND curator_custom."OPERATOR_ID" = operators."ID"
        LEFT JOIN "CALC"."INFRA_Operators_FIO" curators on returnCuratorInfo AND curators."ID" = curator_custom."CuratorId"

WHERE
    requests_closed_info."OperatorId" IS NOT NULL
   OR requests_in_work."ExecutorId" IS NOT NULL
   OR requests_call_info."OperatorId" IS NOT NULL
   OR v_operator_status_queue."OperatorId" IS NOT NULL
   OR operator_response_all."OperatorId" IS NOT NULL
   OR requests_info."OperatorId" IS NOT NULL
   OR requests_csi_info."ACSI" IS NOT NULL
ORDER BY
    operators."ID";

$function$;

END $EF$;
