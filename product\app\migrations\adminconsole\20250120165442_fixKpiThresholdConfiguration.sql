UPDATE 
  "AWP_INFRA"."Configurations" 
SET 
  "Name" = 'KpiThresholdsDefaultValueSettings', 
  "Value" = '{
  "KpiThresholds": [
    {
      "Code": "CountProcessedRequests",
      "DisplayName": "Кол-во обработанных обращений",
      "IsInteger": true,
      "DefaultValue": 120,
      "MinValue": 0,
      "MaxValue": null
    },
    {
      "Code": "CountClosedRequests",
      "DisplayName": "Кол-во закрытых обращений",
      "IsInteger": true,
      "DefaultValue": 120,
      "MinValue": 0,
      "MaxValue": null
    },
    {
      "Code": "CountTransferRequests",
      "DisplayName": "Кол-во переведенных обращений",
      "IsInteger": true,
      "DefaultValue": 10,
      "MinValue": 0,
      "MaxValue": null
    },
    {
      "Code": "CountPostponeRequests",
      "DisplayName": "Кол-во отложенных обращений",
      "IsInteger": true,
      "DefaultValue": 5,
      "MinValue": 0,
      "MaxValue": 30
    },
    {
      "Code": "AverageReactionTime",
      "DisplayName": "Среднее время реакции (сек.)",
      "IsInteger": true,
      "DefaultValue": 20,
      "MinValue": 5,
      "MaxValue": 40
    },
    {
      "Code": "AHT",
      "DisplayName": "AHT (сек.)",
      "IsInteger": true,
      "DefaultValue": 180,
      "MinValue": 30,
      "MaxValue": 350
    },
    {
      "Code": "ACSI",
      "DisplayName": "ACSI",
      "IsInteger": false,
      "DefaultValue": 4.8,
      "MinValue": 1.0,
      "MaxValue": 5.0
    },
    {
      "Code": "ASSI",
      "DisplayName": "ASSI",
      "IsInteger": false,
      "DefaultValue": 4.7,
      "MinValue": 1.0,
      "MaxValue": 5.0
    }
  ]
}', 
  "Description" = 'Настройки пороговых значений KPI' 
WHERE 
  "Name" = 'KpiThresholdsDefaultValueSetting';

-- drop old XML settings
DELETE FROM 
  "AWP_INFRA"."Configurations" 
WHERE 
  "Name" = 'OperatorKpiThresholdsSettings';
