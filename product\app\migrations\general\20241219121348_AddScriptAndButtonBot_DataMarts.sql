DO $EF$
BEGIN

IF NOT EXISTS (SELECT 1 FROM pg_views WHERE schemaname = 'DATA_MARTS' AND viewname = 'V_SCRIPT_RUN_INFO') THEN

CREATE OR REPLACE VIEW "DATA_MARTS"."V_SCRIPT_RUN_INFO" AS
SELECT 
   o."Id" AS "OperatorId",
   o."FirstName" AS "OperatorFirstName",
   o."LastName" AS "OperatorLastName",
   o."MiddleName" AS "OperatorMiddleName",
   runs."ActivatedBy" AS "OperatorLogin",
   runs."RunId",
   runs."TimeActivated",
   runs."FinishedBy",
   runs."TimeFinished",
   runs."ScriptVersion",
   runs."ScriptId",
   runs."ScriptName",
   runs."ScriptCode",
   runs."RequestId",
   runs."RunStepId",
   runs."StepOperator",
   runs."StepTime",
   runs."StepName",
   runs."StepType",
   runs."StepAnswerValue",
   runs."StepVariableCode"
FROM "SCRIPTS"."V_RUN_INFO" runs
JOIN "AWP_INFRA"."Operators" o ON UPPER(o."UserName"::text) = UPPER(runs."ActivatedBy")
WHERE runs."OwnerSystemCode" = 'scripting';

-- ALTER TABLE "DATA_MARTS"."V_BUTTON_BOT_RUN_INFO" OWNER TO postgres;
COMMENT ON VIEW "DATA_MARTS"."V_SCRIPT_RUN_INFO" IS 'Информация по прохождению скриптов диалогов';
COMMENT ON COLUMN "DATA_MARTS"."V_SCRIPT_RUN_INFO"."OperatorId" IS 'Идентификатор оператора';
COMMENT ON COLUMN "DATA_MARTS"."V_SCRIPT_RUN_INFO"."OperatorFirstName" IS 'Фамилия оператора';
COMMENT ON COLUMN "DATA_MARTS"."V_SCRIPT_RUN_INFO"."OperatorLastName" IS 'Имя оператора';
COMMENT ON COLUMN "DATA_MARTS"."V_SCRIPT_RUN_INFO"."OperatorMiddleName" IS 'Отчество оператора';
COMMENT ON COLUMN "DATA_MARTS"."V_SCRIPT_RUN_INFO"."OperatorLogin" IS 'Логин оператора';
COMMENT ON COLUMN "DATA_MARTS"."V_SCRIPT_RUN_INFO"."RunId" IS 'Идентификатор запуска скрипта/сессии';
COMMENT ON COLUMN "DATA_MARTS"."V_SCRIPT_RUN_INFO"."TimeActivated" IS 'Дата и время запуска/сессии';
COMMENT ON COLUMN "DATA_MARTS"."V_SCRIPT_RUN_INFO"."FinishedBy" IS 'Логин оператора завершившего выполнение скрипта';
COMMENT ON COLUMN "DATA_MARTS"."V_SCRIPT_RUN_INFO"."TimeFinished" IS 'Дата и время завершения запуска';
COMMENT ON COLUMN "DATA_MARTS"."V_SCRIPT_RUN_INFO"."ScriptVersion" IS 'Вервися запущенного скрипта';
COMMENT ON COLUMN "DATA_MARTS"."V_SCRIPT_RUN_INFO"."ScriptId" IS 'Идентификатор скрипта';
COMMENT ON COLUMN "DATA_MARTS"."V_SCRIPT_RUN_INFO"."ScriptName" IS 'Наименование скрипта';
COMMENT ON COLUMN "DATA_MARTS"."V_SCRIPT_RUN_INFO"."ScriptCode" IS 'Код скрипта';
COMMENT ON COLUMN "DATA_MARTS"."V_SCRIPT_RUN_INFO"."RequestId" IS 'Идентификатор обращения';
COMMENT ON COLUMN "DATA_MARTS"."V_SCRIPT_RUN_INFO"."RunStepId" IS 'Идентификатор шага экземпларя скрипта';
COMMENT ON COLUMN "DATA_MARTS"."V_SCRIPT_RUN_INFO"."StepOperator" IS 'Логин оператора выполнившего шаг';
COMMENT ON COLUMN "DATA_MARTS"."V_SCRIPT_RUN_INFO"."StepTime" IS 'Дата и время прохождения шага';
COMMENT ON COLUMN "DATA_MARTS"."V_SCRIPT_RUN_INFO"."StepName" IS 'Название шага';
COMMENT ON COLUMN "DATA_MARTS"."V_SCRIPT_RUN_INFO"."StepType" IS 'Тип шага';
COMMENT ON COLUMN "DATA_MARTS"."V_SCRIPT_RUN_INFO"."StepAnswerValue" IS 'Значение выбранное на данном шаге';
COMMENT ON COLUMN "DATA_MARTS"."V_SCRIPT_RUN_INFO"."StepVariableCode" IS 'Код переменной ассоциированной с шагом';

END IF;

IF NOT EXISTS (SELECT 1 FROM pg_views WHERE schemaname = 'DATA_MARTS' AND viewname = 'V_BUTTON_BOT_RUN_INFO') THEN

CREATE OR REPLACE VIEW "DATA_MARTS"."V_BUTTON_BOT_RUN_INFO" AS
SELECT 
   o."Id" AS "OperatorId",
   o."FirstName" AS "OperatorFirstName",
   o."LastName" AS "OperatorLastName",
   o."MiddleName" AS "OperatorMiddleName",
   runs."ActivatedBy" AS "OperatorLogin",
   runs."RunId",
   runs."TimeActivated",
   runs."FinishedBy",
   runs."TimeFinished",
   runs."ScriptVersion",
   runs."ScriptId",
   runs."ScriptName",
   runs."ScriptCode",
   runs."RequestId",
   runs."RunStepId",
   runs."StepOperator",
   runs."StepTime",
   runs."StepName",
   runs."StepType",
   runs."StepAnswerValue",
   runs."StepVariableCode"
FROM "SCRIPTS"."V_RUN_INFO" runs
JOIN "AWP_INFRA"."Operators" o ON UPPER(o."UserName"::text) = UPPER(runs."ActivatedBy")
WHERE runs."OwnerSystemCode" = 'bot';

-- ALTER TABLE "DATA_MARTS"."V_BUTTON_BOT_RUN_INFO" OWNER TO postgres;
COMMENT ON VIEW "DATA_MARTS"."V_BUTTON_BOT_RUN_INFO" IS 'Информация по прохождению скриптов диалогов';
COMMENT ON COLUMN "DATA_MARTS"."V_BUTTON_BOT_RUN_INFO"."OperatorId" IS 'Идентификатор оператора';
COMMENT ON COLUMN "DATA_MARTS"."V_BUTTON_BOT_RUN_INFO"."OperatorFirstName" IS 'Фамилия оператора';
COMMENT ON COLUMN "DATA_MARTS"."V_BUTTON_BOT_RUN_INFO"."OperatorLastName" IS 'Имя оператора';
COMMENT ON COLUMN "DATA_MARTS"."V_BUTTON_BOT_RUN_INFO"."OperatorMiddleName" IS 'Отчество оператора';
COMMENT ON COLUMN "DATA_MARTS"."V_BUTTON_BOT_RUN_INFO"."OperatorLogin" IS 'Логин оператора';
COMMENT ON COLUMN "DATA_MARTS"."V_BUTTON_BOT_RUN_INFO"."RunId" IS 'Идентификатор запуска скрипта/сессии';
COMMENT ON COLUMN "DATA_MARTS"."V_BUTTON_BOT_RUN_INFO"."TimeActivated" IS 'Дата и время запуска/сессии';
COMMENT ON COLUMN "DATA_MARTS"."V_BUTTON_BOT_RUN_INFO"."FinishedBy" IS 'Логин оператора завершившего выполнение скрипта';
COMMENT ON COLUMN "DATA_MARTS"."V_BUTTON_BOT_RUN_INFO"."TimeFinished" IS 'Дата и время завершения запуска';
COMMENT ON COLUMN "DATA_MARTS"."V_BUTTON_BOT_RUN_INFO"."ScriptVersion" IS 'Вервися запущенного скрипта';
COMMENT ON COLUMN "DATA_MARTS"."V_BUTTON_BOT_RUN_INFO"."ScriptId" IS 'Идентификатор скрипта';
COMMENT ON COLUMN "DATA_MARTS"."V_BUTTON_BOT_RUN_INFO"."ScriptName" IS 'Наименование скрипта';
COMMENT ON COLUMN "DATA_MARTS"."V_BUTTON_BOT_RUN_INFO"."ScriptCode" IS 'Код скрипта';
COMMENT ON COLUMN "DATA_MARTS"."V_BUTTON_BOT_RUN_INFO"."RequestId" IS 'Идентификатор обращения';
COMMENT ON COLUMN "DATA_MARTS"."V_BUTTON_BOT_RUN_INFO"."RunStepId" IS 'Идентификатор шага экземпларя скрипта';
COMMENT ON COLUMN "DATA_MARTS"."V_BUTTON_BOT_RUN_INFO"."StepOperator" IS 'Логин оператора выполнившего шаг';
COMMENT ON COLUMN "DATA_MARTS"."V_BUTTON_BOT_RUN_INFO"."StepTime" IS 'Дата и время прохождения шага';
COMMENT ON COLUMN "DATA_MARTS"."V_BUTTON_BOT_RUN_INFO"."StepName" IS 'Название шага';
COMMENT ON COLUMN "DATA_MARTS"."V_BUTTON_BOT_RUN_INFO"."StepType" IS 'Тип шага';
COMMENT ON COLUMN "DATA_MARTS"."V_BUTTON_BOT_RUN_INFO"."StepAnswerValue" IS 'Значение выбранное на данном шаге';
COMMENT ON COLUMN "DATA_MARTS"."V_BUTTON_BOT_RUN_INFO"."StepVariableCode" IS 'Код переменной ассоциированной с шагом';

END IF;

END $EF$;
