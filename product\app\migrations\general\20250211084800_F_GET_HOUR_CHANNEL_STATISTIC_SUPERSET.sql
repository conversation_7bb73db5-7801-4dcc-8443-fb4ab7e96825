DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "CALC"."__CalcContextMigrations" WHERE "MigrationId" = '20250211084800_F_GET_HOUR_CHANNEL_STATISTIC_SUPERSET') THEN
	
CREATE OR REPLACE FUNCTION "CALC"."F_GET_HOUR_CHANNEL_STATISTIC_SUPERSET"(
	_startdate timestamp without time zone DEFAULT (CURRENT_DATE)::timestamp without time zone,
	_enddate timestamp without time zone DEFAULT (CURRENT_DATE + '1 day'::interval))
    RETURNS TABLE("DateTime" timestamp without time zone, "Total_Channel" character varying, "Total_Received" bigint, "Total_Lost" bigint, "Total_Skipped" bigint, "Total_Accepted" bigint, "Total_AHT" bigint, "Total_ASA" bigint, "Chat_Channel" character varying, "Chat_Received" bigint, "Chat_Lost" bigint, "Chat_Skipped" bigint, "Chat_Accepted" bigint, "Chat_AHT" bigint, "Chat_ASA" bigint, "Telegram_Channel" character varying, "Telegram_Received" bigint, "Telegram_Lost" bigint, "Telegram_Skipped" bigint, "Telegram_Accepted" bigint, "Telegram_AHT" bigint, "Telegram_ASA" bigint, "Viber_Channel" character varying, "Viber_Received" bigint, "Viber_Lost" bigint, "Viber_Skipped" bigint, "Viber_Accepted" bigint, "Viber_AHT" bigint, "Viber_ASA" bigint, "Videochat_Channel" character varying, "Videochat_Received" bigint, "Videochat_Lost" bigint, "Videochat_Skipped" bigint, "Videochat_Accepted" bigint, "Videochat_AHT" bigint, "Videochat_ASA" bigint, "Vk_Channel" character varying, "Vk_Received" bigint, "Vk_Lost" bigint, "Vk_Skipped" bigint, "Vk_Accepted" bigint, "Vk_AHT" bigint, "Vk_ASA" bigint, "Sms_Channel" character varying, "Sms_Received" bigint, "Sms_Lost" bigint, "Sms_Skipped" bigint, "Sms_Accepted" bigint, "Sms_AHT" bigint, "Sms_ASA" bigint, "Exchange_Channel" character varying, "Exchange_Received" bigint, "Exchange_Lost" bigint, "Exchange_Skipped" bigint, "Exchange_Accepted" bigint, "Exchange_AHT" bigint, "Exchange_ASA" bigint, "Feedbackform_Channel" character varying, "Feedbackform_Received" bigint, "Feedbackform_Lost" bigint, "Feedbackform_Skipped" bigint, "Feedbackform_Accepted" bigint, "Feedbackform_AHT" bigint, "Feedbackform_ASA" bigint, "Voice_Channel" character varying, "Voice_Received" bigint, "Voice_Lost" bigint, "Voice_Skipped" bigint, "Voice_Accepted" bigint, "Voice_AHT" bigint, "Voice_ASA" bigint, "Vkpublic_Channel" character varying, "Vkpublic_Received" bigint, "Vkpublic_Lost" bigint, "Vkpublic_Skipped" bigint, "Vkpublic_Accepted" bigint, "Vkpublic_AHT" bigint, "Vkpublic_ASA" bigint, "Facebook_Channel" character varying, "Facebook_Received" bigint, "Facebook_Lost" bigint, "Facebook_Skipped" bigint, "Facebook_Accepted" bigint, "Facebook_AHT" bigint, "Facebook_ASA" bigint, "Whatsapp_Channel" character varying, "Whatsapp_Received" bigint, "Whatsapp_Lost" bigint, "Whatsapp_Skipped" bigint, "Whatsapp_Accepted" bigint, "Whatsapp_AHT" bigint, "Whatsapp_ASA" bigint, "Imessage_Channel" character varying, "Imessage_Received" bigint, "Imessage_Lost" bigint, "Imessage_Skipped" bigint, "Imessage_Accepted" bigint, "Imessage_AHT" bigint, "Imessage_ASA" bigint, "Default_Channel" character varying, "Default_Received" bigint, "Default_Lost" bigint, "Default_Skipped" bigint, "Default_Accepted" bigint, "Default_AHT" bigint, "Default_ASA" bigint) 
    LANGUAGE 'sql'
    COST 100
    VOLATILE PARALLEL UNSAFE
    ROWS 1000

AS $BODY$
WITH
    date_hour AS(
        SELECT
             date_trunc('hour', start)::timestamp AS "DateTime"
        FROM (
                 SELECT generate_series(_startdate, (_enddate - '1 hour'::interval), '1 hour') AS start) x
    ),channels AS(
    Select ch."Channel" FROM "CALC"."V_CHANNELS_INFO" ch GROUP BY  ch."Channel"
),stub AS(
    Select * FROM date_hour
                      CROSS JOIN channels
    ORDER BY date_hour."DateTime", channels."Channel"
),received AS (
    SELECT
	      date_trunc('hour', r."TimeCreated" AT TIME ZONE 'UTC')::timestamp AS "DateTime"
         ,r."Channel"::character varying
         ,COUNT(distinct(r_kpi."Id")) as "Received" -- поступило по каналу за час
         ,SUM(r_kpi."HT") as "HT_SUM"
         ,COUNT(r_kpi."HT") as "HT_COUNT"
         ,SUM(r_kpi."SA") as "SA_SUM"
         ,COUNT(r_kpi."SA") as "SA_COUNT"
    FROM "CRPM_DATA"."Requests" r
             LEFT JOIN "KPI"."REQUEST_KPI" r_kpi ON r."Id" = r_kpi."Id"
             LEFT JOIN "KPI"."OPERATOR_CALL_STATE" callState ON callState."RequestId" = r."Id"
    WHERE r."TimeCreated" BETWEEN timezone('UTC', _startdate) AND timezone('UTC', _enddate)
    GROUP BY date_trunc('hour', r."TimeCreated" AT TIME ZONE 'UTC'), r."Channel" -- разрез одного часа
),accepted AS (
    SELECT
	     date_trunc('hour', r_kpi."TimeOperatorFirstDistribution" AT TIME ZONE 'UTC')::timestamp AS "DateTime"
         ,r."Channel"::character varying
         ,COUNT(r_kpi."TimeOperatorFirstDistribution") as "Accepted" -- принято по каналу за час
    FROM "CRPM_DATA"."Requests" r
             LEFT JOIN "KPI"."REQUEST_KPI" r_kpi ON r."Id" = r_kpi."Id"
             LEFT JOIN "KPI"."OPERATOR_CALL_STATE" callState ON callState."RequestId" = r."Id"
    WHERE r_kpi."TimeOperatorFirstDistribution" BETWEEN timezone('UTC', _startdate) AND timezone('UTC', _enddate)
    GROUP BY date_trunc('hour', r_kpi."TimeOperatorFirstDistribution" AT TIME ZONE 'UTC'), r."Channel" -- разрез одного часа
),lost AS (
    SELECT
		  date_trunc('hour', r_kpi."TimeLost" AT TIME ZONE 'UTC')::timestamp AS "DateTime"
         ,r."Channel"::character varying
         ,COUNT(r_kpi."TimeLost") as "Lost" -- потеряно по каналу за час
    FROM "CRPM_DATA"."Requests" r
             LEFT JOIN "KPI"."REQUEST_KPI" r_kpi ON r."Id" = r_kpi."Id"
             LEFT JOIN "KPI"."OPERATOR_CALL_STATE" callState ON callState."RequestId" = r."Id"
    WHERE r_kpi."TimeLost" BETWEEN timezone('UTC', _startdate) AND timezone('UTC', _enddate)
    GROUP BY date_trunc('hour', r_kpi."TimeLost" AT TIME ZONE 'UTC'), r."Channel" -- разрез одного часа
),skipped AS (
    SELECT
	     date_trunc('hour', callState."TimeCallMissed" AT TIME ZONE 'UTC')::timestamp AS "DateTime"
         ,r."Channel"::character varying
         ,COUNT(callState."TimeCallMissed") as "Skipped" -- пропущено по каналу за час
    FROM "CRPM_DATA"."Requests" r
             LEFT JOIN "KPI"."REQUEST_KPI" r_kpi ON r."Id" = r_kpi."Id"
             LEFT JOIN "KPI"."OPERATOR_CALL_STATE" callState ON callState."RequestId" = r."Id"
    WHERE callState."TimeCallMissed" BETWEEN timezone('UTC', _startdate) AND timezone('UTC', _enddate)
    GROUP BY date_trunc('hour', callState."TimeCallMissed" AT TIME ZONE 'UTC'), r."Channel" -- разрез одного часа
),real_data AS (
    SELECT
          COALESCE(received."DateTime", accepted."DateTime", lost."DateTime", skipped."DateTime") AS "DateTime"
         ,COALESCE(received."Channel", accepted."Channel", lost."Channel", skipped."Channel") AS "Channel"
         ,COALESCE(received."Received", 0) AS "Received"
         ,COALESCE(accepted."Accepted", 0) AS "Accepted"
         ,COALESCE(lost."Lost", 0) AS "Lost"
         ,COALESCE(skipped."Skipped", 0) AS "Skipped"
         ,received."HT_SUM" AS "HT_SUM"
         ,received."HT_COUNT" AS "HT_COUNT"
         ,received."SA_SUM" AS "SA_SUM"
         ,received."SA_COUNT" AS "SA_COUNT"
    FROM received
             FULL JOIN accepted ON received."DateTime" = accepted."DateTime" AND received."Channel" = accepted."Channel"
             FULL JOIN lost ON received."DateTime" = lost."DateTime" AND received."Channel" = lost."Channel"
             FULL JOIN skipped ON received."DateTime" = skipped."DateTime"  AND received."Channel" = skipped."Channel"
),real_data_distinct AS (
    Select
        real_data."DateTime",
        real_data."Channel",
        MAX(real_data."Received") AS "Received",
        MAX(real_data."Accepted") AS "Accepted",
        MAX(real_data."Lost") AS "Lost",
        MAX(real_data."Skipped") AS "Skipped",
        MAX(real_data."HT_SUM") AS "HT_SUM",
        MAX(real_data."HT_COUNT") AS "HT_COUNT",
        MAX(real_data."SA_SUM") AS "SA_SUM",
        MAX(real_data."SA_COUNT") AS "SA_COUNT"
    FROM real_data
    GROUP BY real_data."DateTime", real_data."Channel"
),real_data_with_stub AS (
    Select
        stub."DateTime" AS "DateTime"
         ,stub."Channel" AS "Channel"
         ,COALESCE(real_data_distinct."Received", 0) AS "Received"
         ,COALESCE(real_data_distinct."Lost", 0) AS "Lost"
         ,COALESCE(real_data_distinct."Skipped", 0) AS "Skipped"
         ,COALESCE(real_data_distinct."Accepted", 0) AS "Accepted"
         ,COALESCE(real_data_distinct."HT_SUM" / (CASE WHEN real_data_distinct."HT_COUNT" IS NOT NULL AND real_data_distinct."HT_COUNT" > 0 THEN real_data_distinct."HT_COUNT" ELSE NULL::bigint END), 0)::bigint AS "AHT" -- AHT за час по конкретному каналу
         ,COALESCE(real_data_distinct."SA_SUM" / (CASE WHEN real_data_distinct."SA_COUNT" IS NOT NULL AND real_data_distinct."SA_COUNT" > 0 THEN real_data_distinct."SA_COUNT" ELSE NULL::bigint END), 0)::bigint AS "ASA"
         ,COALESCE(real_data_distinct."HT_SUM", 0) AS "HT_SUM"
         ,COALESCE(real_data_distinct."HT_COUNT", 0) AS "HT_COUNT"
         ,COALESCE(real_data_distinct."SA_SUM", 0) AS "SA_SUM"
         ,COALESCE(real_data_distinct."SA_COUNT", 0) AS "SA_COUNT"
    FROM stub
             LEFT JOIN real_data_distinct ON real_data_distinct."DateTime" = stub."DateTime" AND real_data_distinct."Channel" = stub."Channel"
    ORDER BY stub."DateTime", stub."Channel"
)
--Select * FROM real_data_with_stub
   ,hour_group AS (
    select
		  real_data_with_stub."DateTime"
         ,'CHANNEL.ALL' AS "Channel"
         ,SUM(real_data_with_stub."Received")::bigint as "Received" -- поступило всего за час
         ,SUM(real_data_with_stub."Lost")::bigint as "Lost" -- потеряно всего за час
         ,SUM(real_data_with_stub."Skipped")::bigint as "Skipped" -- пропущено всего за час
         ,SUM(real_data_with_stub."Accepted")::bigint as "Accepted" -- принято всего за час
         ,COALESCE((SUM(real_data_with_stub."HT_SUM") / (CASE WHEN SUM(real_data_with_stub."HT_COUNT") > 0 THEN SUM(real_data_with_stub."HT_COUNT") ELSE NULL::bigint END)),0)::bigint as "AHT" -- AHT за час по всем каналам
         ,COALESCE((SUM(real_data_with_stub."SA_SUM") /  (CASE WHEN SUM(real_data_with_stub."SA_COUNT")  > 0 THEN  SUM(real_data_with_stub."SA_COUNT") ELSE NULL::bigint END)),0)::bigint as "ASA" -- "ASA" за час по всем каналам
         ,SUM(real_data_with_stub."HT_SUM") AS "HT_SUM"
         ,SUM(real_data_with_stub."HT_COUNT") AS "HT_COUNT"
         ,SUM(real_data_with_stub."SA_SUM") AS "SA_SUM"
         ,SUM(real_data_with_stub."SA_COUNT") AS "SA_COUNT"
    from real_data_with_stub
    group by real_data_with_stub."DateTime"
)
--Select * FROM hour_group
   ,hour_group_total AS (
    SELECT *
    FROM real_data_with_stub
    UNION SELECT * FROM hour_group
)
--Select * FROM hour_group_total ORDER BY hour_group_total."Date", hour_group_total."Hour"
   ,total AS (
    Select * FROM hour_group_total ORDER BY hour_group_total."Channel"
)
--Select * FROM total
Select
    total."DateTime"
     ,total."Channel" AS "Hour_Total_Channel"
     ,total."Received" AS "Hour_Total_Received"
     ,total."Lost" AS "Hour_Total_Lost"
     ,total."Skipped" AS "Hour_Total_Skipped"
     ,total."Accepted" AS "Hour_Total_Accepted"
     ,total."AHT" AS "Hour_Total_AHT"
     ,total."ASA" AS "Hour_Total_ASA"
     ,COALESCE(chat."Channel",'CHANNEL.CHAT') AS "Chat_Channel"
     ,COALESCE(chat."Received", 0) AS "Chat_Received"
     ,COALESCE(chat."Lost", 0) AS "Chat_Lost"
     ,COALESCE(chat."Skipped", 0) AS "Chat_Skipped"
     ,COALESCE(chat."Accepted", 0) AS "Chat_Accepted"
     ,COALESCE(chat."AHT", 0) AS "Chat_AHT"
     ,COALESCE(chat."ASA", 0) AS "Chat_ASA"
     ,COALESCE(telegram."Channel", 'CHANNEL.TELEGRAM') AS "Telegram_Channel"
     ,COALESCE(telegram."Received", 0) AS "Telegram_Received"
     ,COALESCE(telegram."Lost", 0) AS "Telegram_Lost"
     ,COALESCE(telegram."Skipped", 0) AS "Telegram_Skipped"
     ,COALESCE(telegram."Accepted", 0) AS "Telegram_Accepted"
     ,COALESCE(telegram."AHT", 0) AS "Telegram_AHT"
     ,COALESCE(telegram."ASA", 0) AS "Telegram_ASA"
     ,COALESCE(viber."Channel", 'CHANNEL.VIBER') AS "Viber_Channel"
     ,COALESCE(viber."Received", 0) AS "Viber_Received"
     ,COALESCE(viber."Lost", 0) AS "Viber_Lost"
     ,COALESCE(viber."Skipped", 0) AS "Viber_Skipped"
     ,COALESCE(viber."Accepted", 0) AS "Viber_Accepted"
     ,COALESCE(viber."AHT", 0) AS "Viber_AHT"
     ,COALESCE(viber."ASA", 0) AS "Viber_ASA"
     ,COALESCE(videochat."Channel", 'CHANNEL.VIDEOCHAT') AS "Videochat_Channel"
     ,COALESCE(videochat."Received", 0) AS "Videochat_Received"
     ,COALESCE(videochat."Lost", 0) AS "Videochat_Lost"
     ,COALESCE(videochat."Skipped", 0) AS "Videochat_Skipped"
     ,COALESCE(videochat."Accepted", 0) AS "Videochat_Accepted"
     ,COALESCE(videochat."AHT", 0) AS "Videochat_AHT"
     ,COALESCE(videochat."ASA", 0) AS "Videochat_ASA"
     ,COALESCE(vk."Channel", 'CHANNEL.VK') AS "Vk_Channel"
     ,COALESCE(vk."Received", 0) AS "Vk_Received"
     ,COALESCE(vk."Lost", 0) AS "Vk_Lost"
     ,COALESCE(vk."Skipped", 0) AS "Vk_Skipped"
     ,COALESCE(vk."Accepted", 0) AS "Vk_Accepted"
     ,COALESCE(vk."AHT", 0) AS "Vk_AHT"
     ,COALESCE(vk."ASA", 0) AS "Vk_ASA"
     ,COALESCE(sms."Channel", 'CHANNEL.SMS') AS "Sms_Channel"
     ,COALESCE(sms."Received", 0) AS "Sms_Received"
     ,COALESCE(sms."Lost", 0) AS "Sms_Lost"
     ,COALESCE(sms."Skipped", 0) AS "Sms_Skipped"
     ,COALESCE(sms."Accepted", 0) AS "Sms_Accepted"
     ,COALESCE(sms."AHT", 0) AS "Sms_AHT"
     ,COALESCE(sms."ASA", 0) AS "Sms_ASA"
     ,COALESCE(exchange."Channel", 'CHANNEL.EXCHANGE') AS "Exchange_Channel"
     ,COALESCE(exchange."Received", 0) AS "Exchange_Received"
     ,COALESCE(exchange."Lost", 0) AS "Exchange_Lost"
     ,COALESCE(exchange."Skipped", 0) AS "Exchange_Skipped"
     ,COALESCE(exchange."Accepted", 0) AS "Exchange_Accepted"
     ,COALESCE(exchange."AHT", 0) AS "Exchange_AHT"
     ,COALESCE(exchange."ASA", 0) AS "Exchange_ASA"
     ,COALESCE(feedbackform."Channel", 'CHANNEL.FEEDBACKFORM') AS "Feedbackform_Channel"
     ,COALESCE(feedbackform."Received", 0) AS "Feedbackform_Received"
     ,COALESCE(feedbackform."Lost", 0) AS "Feedbackform_Lost"
     ,COALESCE(feedbackform."Skipped", 0) AS "Feedbackform_Skipped"
     ,COALESCE(feedbackform."Accepted", 0) AS "Feedbackform_Accepted"
     ,COALESCE(feedbackform."AHT", 0) AS "Feedbackform_AHT"
     ,COALESCE(feedbackform."ASA", 0) AS "Feedbackform_ASA"
     ,COALESCE(voice."Channel", 'CHANNEL.VOICE') AS "Voice_Channel"
     ,COALESCE(voice."Received", 0) AS "Voice_Received"
     ,COALESCE(voice."Lost", 0) AS "Voice_Lost"
     ,COALESCE(voice."Skipped", 0) AS "Voice_Skipped"
     ,COALESCE(voice."Accepted", 0) AS "Voice_Accepted"
     ,COALESCE(voice."AHT", 0) AS "Voice_AHT"
     ,COALESCE(voice."ASA", 0) AS "Voice_ASA"
     ,COALESCE(vkpublic."Channel", 'CHANNEL.VKPUBLIC') AS "Vkpublic_Channel"
     ,COALESCE(vkpublic."Received", 0) AS "Vkpublic_Received"
     ,COALESCE(vkpublic."Lost", 0) AS "Vkpublic_Lost"
     ,COALESCE(vkpublic."Skipped", 0) AS "Vkpublic_Skipped"
     ,COALESCE(vkpublic."Accepted", 0) AS "Vkpublic_Accepted"
     ,COALESCE(vkpublic."AHT", 0) AS "Vkpublic_AHT"
     ,COALESCE(vkpublic."ASA", 0) AS "Vkpublic_ASA"
     ,COALESCE(facebook."Channel", 'CHANNEL.FACEBOOK') AS "Facebook_Channel"
     ,COALESCE(facebook."Received", 0) AS "Facebook_Received"
     ,COALESCE(facebook."Lost", 0) AS "Facebook_Lost"
     ,COALESCE(facebook."Skipped", 0) AS "Facebook_Skipped"
     ,COALESCE(facebook."Accepted", 0) AS "Facebook_Accepted"
     ,COALESCE(facebook."AHT", 0) AS "Facebook_AHT"
     ,COALESCE(facebook."ASA", 0) AS "Facebook_ASA"
     ,'CHANNEL.WHATSAPP' AS "Whatsapp_Channel"
     ,(COALESCE(mfms_whatsapp."Received", 0) + COALESCE(infobip_whatsapp."Received", 0) + COALESCE(megafon_whatsapp."Received", 0)) AS "Whatsapp_Received"
     ,(COALESCE(mfms_whatsapp."Lost", 0) + COALESCE(infobip_whatsapp."Lost", 0) + COALESCE(megafon_whatsapp."Lost", 0)) AS "Whatsapp_Lost"
     ,(COALESCE(mfms_whatsapp."Skipped", 0) + COALESCE(infobip_whatsapp."Skipped", 0) + COALESCE(megafon_whatsapp."Skipped", 0)) AS "Whatsapp_Skipped"
     ,(COALESCE(mfms_whatsapp."Accepted", 0) + COALESCE(infobip_whatsapp."Accepted", 0) + COALESCE(megafon_whatsapp."Accepted", 0)) AS "Whatsapp_Accepted"
     ,(COALESCE(mfms_whatsapp."AHT", 0) + COALESCE(infobip_whatsapp."AHT", 0) + COALESCE(megafon_whatsapp."AHT", 0)) AS "Whatsapp_AHT"
     ,(COALESCE(mfms_whatsapp."ASA", 0) + COALESCE(infobip_whatsapp."ASA", 0) + COALESCE(megafon_whatsapp."ASA", 0)) AS "Whatsapp_ASA"
     ,COALESCE(imessage."Channel", 'CHANNEL.IMESSAGE') AS "Imessage_Channel"
     ,COALESCE(imessage."Received", 0) AS "Imessage_Received"
     ,COALESCE(imessage."Lost", 0) AS "Imessage_Lost"
     ,COALESCE(imessage."Skipped", 0) AS "Imessage_Skipped"
     ,COALESCE(imessage."Accepted", 0) AS "Imessage_Accepted"
     ,COALESCE(imessage."AHT", 0) AS "Imessage_AHT"
     ,COALESCE(imessage."ASA", 0) AS "Imessage_ASA"
     ,COALESCE(_default."Channel", 'CHANNEL.DEFAULT') AS "Default_Channel"
     ,COALESCE(_default."Received", 0) AS "Default_Received"
     ,COALESCE(_default."Lost", 0) AS "Default_Lost"
     ,COALESCE(_default."Skipped", 0) AS "Default_Skipped"
     ,COALESCE(_default."Accepted", 0) AS "Default_Accepted"
     ,COALESCE(_default."AHT", 0) AS "Default_AHT"
     ,COALESCE(_default."ASA", 0) AS "Default_ASA"
FROM total
         LEFT JOIN total chat on total."DateTime" = chat."DateTime" AND chat."Channel" = 'CHANNEL.CHAT'
         LEFT JOIN total telegram on total."DateTime" = telegram."DateTime" AND telegram."Channel" = 'CHANNEL.TELEGRAM'
         LEFT JOIN total viber on total."DateTime" = viber."DateTime" AND viber."Channel" = 'CHANNEL.VIBER'
         LEFT JOIN total videochat on total."DateTime" = videochat."DateTime" AND videochat."Channel" = 'CHANNEL.VIDEOCHAT'
         LEFT JOIN total vk on total."DateTime" = vk."DateTime" AND vk."Channel" = 'CHANNEL.VK'
         LEFT JOIN total sms on total."DateTime" = sms."DateTime" AND sms."Channel" = 'CHANNEL.SMS'
         LEFT JOIN total exchange on total."DateTime" = exchange."DateTime" AND exchange."Channel" = 'CHANNEL.EXCHANGE'
         LEFT JOIN total feedbackform on total."DateTime" = feedbackform."DateTime" AND feedbackform."Channel" = 'CHANNEL.FEEDBACKFORM'
         LEFT JOIN total voice on total."DateTime" = voice."DateTime" AND voice."Channel" = 'CHANNEL.VOICE'
         LEFT JOIN total vkpublic on total."DateTime" = vkpublic."DateTime" AND vkpublic."Channel" = 'CHANNEL.VKPUBLIC'
         LEFT JOIN total facebook on total."DateTime" = facebook."DateTime" AND facebook."Channel" = 'CHANNEL.FACEBOOK'
         LEFT JOIN total mfms_whatsapp on total."DateTime" = mfms_whatsapp."DateTime" AND mfms_whatsapp."Channel" = 'CHANNEL.MFMS.WHATSAPP'
         LEFT JOIN total infobip_whatsapp on total."DateTime" = infobip_whatsapp."DateTime" AND infobip_whatsapp."Channel" = 'CHANNEL.INFOBIP.WHATSAPP'
         LEFT JOIN total megafon_whatsapp on total."DateTime" = megafon_whatsapp."DateTime" AND megafon_whatsapp."Channel" = 'CHANNEL.MF.WHATSAPP'
         LEFT JOIN total imessage on total."DateTime" = imessage."DateTime" AND imessage."Channel" = 'CHANNEL.MFMS.IMESSAGE'
         LEFT JOIN total _default on total."DateTime" = _default."DateTime" AND _default."Channel" = 'CHANNEL.DEFAULT'
WHERE total."Channel" = 'CHANNEL.ALL'
ORDER BY total."DateTime"
$BODY$;

ALTER FUNCTION "CALC"."F_GET_HOUR_CHANNEL_STATISTIC_SUPERSET"(timestamp without time zone, timestamp without time zone)
    OWNER TO postgres;

COMMENT ON FUNCTION "CALC"."F_GET_HOUR_CHANNEL_STATISTIC_SUPERSET"(timestamp without time zone, timestamp without time zone)
    IS 'Функция для подсчета статистики обращений по часам по всем каналам за заданный промежуток дат для суперсета';

    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "CALC"."__CalcContextMigrations" WHERE "MigrationId" = '20250211084800_F_GET_HOUR_CHANNEL_STATISTIC_SUPERSET') THEN
    INSERT INTO "CALC"."__CalcContextMigrations" ("MigrationId", "ProductVersion")
    VALUES ('20250211084800_F_GET_HOUR_CHANNEL_STATISTIC_SUPERSET', '9.0.1');
    END IF;
END $EF$;

