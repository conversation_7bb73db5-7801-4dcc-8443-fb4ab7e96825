-- снача<PERSON>а удаляем все вьюхи, где используется столбец

DROP VIEW "CALC"."V_OPERATORS_INFO";

DROP VIEW "CALC"."V_OPERATOR_REQUEST_INFO_V2";

DROP VIEW "DATA_MARTS"."V_OPERATOR_STATUSES";

-- меняем тип столбца

ALTER TABLE IF EXISTS "KPI"."OPERATOR_STATUSES" ALTER COLUMN "StatusDuration" TYPE double precision;

-- пересоздаем вьюхи

CREATE OR REPLACE VIEW "CALC"."V_OPERATORS_INFO"
AS SELECT op."OperatorId",
    op."FIO",
    op."LOGIN"::character varying(300) AS "LOGIN",
    op."CURATOR_ID" AS "CURATOR_ID",    
    op."CURATOR_FIO" AS "CURATOR_FIO",  
    op."QUEUES_LIST",
    op."CURRENT_STATUS",
    op."SECONDS_IN_CURRENT_STATUS"::numeric(10,2) AS "SECONDS_IN_CURRENT_STATUS",
    op."START_TIME",
    op."PAUSE_DURATION",
    op."IN_WORK_DURATION",
    op."IN_LINE_DURATION",
    op."UTILIZATION",
    op."OCCUPANCY",
    op."CONTACTS_COUNT",
    op."CLOSED_REQUESTS",
    op."REDIRECTED_REQUESTS",
    op."POSTPONED_REQUESTS",
    op."ASA"::numeric(10,2) AS "ASA",
    op."AHT"::numeric(10,2) AS "AHT",
    op."ACW"::numeric(10,2) AS "ACW",
    op."ACSI"::numeric(10,2) AS "ACSI",
    op."ASSI"::numeric(10,2) AS "ASSI",
    op."ART"::numeric(10,2) AS "ART",
    op."A1RT"::numeric(10,2) AS "A1RT",
    op."VOICE_ART"::numeric(10,2) AS "VOICE_ART",
    op."CALLS_RECEIVED_COUNT",
    op."CALLS_ACCEPTED_COUNT",
    op."CALLS_MISSED_COUNT",
    op."CALLS_OUTBOUND_COUNT",
    op."CONVERSATION_TIME",
    op."WAIT_TIME_DURATION",
    op."DROPPED_REQUESTS",
    op."CURRENT_STATUS_CODE",
    curr."DURATION_CURRENT_STATUS_TODAY",
    op."CW_SUM"::numeric(10,2) AS "CWSUM",
    op."ON_BREAK",
    op."DIVISION_NAME",
    op."DIVISION_ID",
    op."SERVICE_SESSIONS_COUNT" -- [Контакты и клиенты] Количество сессий обслуживания
   FROM "CALC"."F_OPERATORS_KPI_INFO"() op
     LEFT JOIN LATERAL ( SELECT sum(COALESCE(os."StatusDuration"::double precision, date_part('epoch'::text, timezone('UTC'::text, CURRENT_TIMESTAMP) - os."StartDate")))::numeric(20,0) AS "DURATION_CURRENT_STATUS_TODAY"
           FROM "KPI"."OPERATOR_STATUSES" os
          WHERE os."OperatorId" = op."OperatorId" AND os."Status" = op."CURRENT_STATUS_CODE" AND (os."EndDate" >= timezone('UTC'::text, CURRENT_DATE::timestamp with time zone) OR os."EndDate" IS NULL)) curr ON true
  WHERE op."CURRENT_STATUS" IS NOT NULL;

CREATE OR REPLACE VIEW "CALC"."V_OPERATOR_REQUEST_INFO_V2"
AS
WITH rq AS (
    SELECT rq."Id",
           rq."Title"::text || '; '::text AS raw_title
    FROM "CALC"."CFG_RequestQueues" rq
    WHERE rq."TimeDeleted" IS NULL
), queues AS (
    SELECT erq."ExecutorId" AS "OPERATOR_ID",
           string_agg(rq.raw_title, ''::text ORDER BY erq."ExecutorId") AS raw_skill_list
    FROM rq
             JOIN "CALC"."CFG_ExecutorRequestQueues" erq ON rq."Id" = erq."QueueId"
    GROUP BY erq."ExecutorId"
), a AS (
    SELECT ogo."OperatorId",
           og."Name",
           og."Name" || '; '::text AS raw_name
    FROM "CALC"."INFRA_OperatorGroupOperator" ogo
             LEFT JOIN "CALC"."INFRA_OperatorGroups" og ON ogo."OperatorGroupId" = og."Id"
), group_list AS (
    SELECT a."OperatorId",
           string_agg(a.raw_name, ''::text ORDER BY a."OperatorId") AS raw_groups_list
    FROM a
    GROUP BY a."OperatorId"
), oca AS (
    SELECT ocas."OPERATOR_ID",
           ocas."DATETIME_VALUE" AS "AvailablePersManagerDateTime"
    FROM "AWP_INFRA"."OPERATOR_CUSTOM_ATTRIBUTES" ocas
    WHERE ocas."CODE"::text = 'AvailablePersManager'::text
), cca AS (
         SELECT "OPERATOR_CUSTOM_ATTRIBUTES"."OPERATOR_ID",
            "OPERATOR_CUSTOM_ATTRIBUTES"."GUID_VALUE" AS "CuratorId"
           FROM "AWP_INFRA"."OPERATOR_CUSTOM_ATTRIBUTES"
          WHERE "OPERATOR_CUSTOM_ATTRIBUTES"."CODE"::text = 'CuratorId'::text
 ), ri AS (
    SELECT count(*) AS "RequestsInWorkCount",
           string_agg(r_1."Id"::text, ';'::text ORDER BY r_1."Id") AS "RequestsInWorkIds",
           r_1."ExecutorId" AS "OPERATOR_ID"
    FROM "CRPM_DATA"."Requests" r_1
             JOIN "CRPM_CFG"."RequestStateStatuses" status ON r_1."Status" = status."Id"
    WHERE status."Code"::text = ANY (ARRAY['OPERATOR.WORK'::text, 'OPERATOR.PAUSE'::text, 'VOICE'::text, 'REQUEST.PROCESSING'::text, 'CLAIM.OPERATOR.WORK'::text])
    GROUP BY r_1."ExecutorId"
), r AS (
    SELECT r_1."Id" AS request_id,
           r_1."Channel",
           r_1."QueueId",
           que."Title" AS queue,
           r_1."TimeRegistered",
           r_1."Priority",
           r_1."BranchId",
           sa."Name" AS branch_name,
           r_1."ExecutorId" AS "OPERATOR_ID",
           max(w."TimeActivated") OVER (PARTITION BY w."ExecutorId") AS max_time,
           w."TimeActivated"
    FROM "CRPM_DATA"."Requests" r_1
             LEFT JOIN "CALC"."CFG_RequestQueues" que ON que."Id" = r_1."QueueId"
             LEFT JOIN "CALC"."INFRA_ServiceAreas" sa ON sa."Id" = r_1."BranchId"
             LEFT JOIN "CRPM_DATA"."Works" w ON w."RequestId" = r_1."Id"
    WHERE r_1."State" = 2 AND r_1."Status" = 1
), clienttype_cte AS (
    SELECT DISTINCT ON ("WorkSessions"."OperatorId") "WorkSessions"."OperatorId",
                                                     "WorkSessions"."ClientType"
    FROM "AWP_INFRA"."WorkSessions"
    WHERE "WorkSessions"."EndDate" IS NULL AND "WorkSessions"."VALID_UNTIL" IS NOT NULL AND "WorkSessions"."ClientType" IS NOT NULL
    ORDER BY "WorkSessions"."OperatorId", "WorkSessions"."VALID_UNTIL" DESC
), operator_root_group AS (
    SELECT og."Name",
           og."Id",
           ogo."OperatorId",
           count(*) OVER (PARTITION BY ogo."OperatorId") AS cnt
    FROM "AWP_INFRA"."OperatorGroups" og
             LEFT JOIN "AWP_INFRA"."OperatorGroupOperator" ogo ON ogo."OperatorGroupId" = og."Id"
    WHERE og."ParentId" IS NULL
), call_control_agent_ready_cte AS (
    SELECT DISTINCT "SessionState"."OperatorId"
    FROM "CALL_CONTROL"."SessionState"
    WHERE "SessionState"."ActiveCall" IS NULL AND "SessionState"."CurrentAgentStatus" = 'ready'::"CALL_CONTROL".agent_status_type
), pending_status_cte AS (
    SELECT COALESCE(os."Name", '-'::text) AS "PendingStatusName",
           os."Code" AS "PendingStatusCode",
           aos."OperatorId"
    FROM "AWP_INFRA"."ActualOperatorStatuses" aos
             LEFT JOIN "AWP_INFRA"."OperatorStatuses" os ON os."Id" = aos."ActualPendingStatusId"
), res AS (
    SELECT ob."OPERATOR_ID" AS "OperatorId",
           operator_logins."FIO",
           operator_logins."LOGIN",
           curator_logins."FIO" AS "CURATOR_FIO",
           curator_logins."ID" AS "CURATOR_ID",     
           ob."STATUS",
           ob."STATUS_CODE",
           ob."SECONDS_IN_STATUS",
           rtrim(queues.raw_skill_list, '; '::text) AS "QUEUES_LIST",
           rtrim(group_list.raw_groups_list, '; '::text) AS "GROUPS_LIST",
           ri."RequestsInWorkCount",
           ri."RequestsInWorkIds",
           r."Channel",
           r.request_id AS "REQUEST_ID",
           oca."AvailablePersManagerDateTime" IS NOT NULL AND oca."AvailablePersManagerDateTime" > now() AS "CAN_BE_PERSONAL_MANAGER",
           opi."CurrentLogin" AS "CurrentSoftphoneLogin",
           opi."CallRequestId" AS "ActiveCallRequestId",
           ws."ClientType",
           curr."DURATION_CURRENT_STATUS_TODAY",
           CASE
               WHEN operator_root_group.cnt > 1 THEN NULL::text
               ELSE operator_root_group."Name"
               END AS "OperatorRootGroupName",
           CASE
               WHEN operator_root_group.cnt > 1 THEN NULL::uuid
               ELSE operator_root_group."Id"
               END AS "OperatorRootGroupId",
           ccar."OperatorId" IS NOT NULL AS "ReadyForCall",
           pending_status_cte."PendingStatusName",
           pending_status_cte."PendingStatusCode"
    FROM "CALC"."V_OPERATOR_STATUSES" ob
             JOIN "CALC"."INFRA_Operators_FIO" operator_logins ON operator_logins."ID" = ob."OPERATOR_ID"
             LEFT JOIN queues ON ob."OPERATOR_ID" = queues."OPERATOR_ID"
             LEFT JOIN group_list ON ob."OPERATOR_ID" = group_list."OperatorId"
             LEFT JOIN ri ON ob."OPERATOR_ID" = ri."OPERATOR_ID"
             LEFT JOIN r ON ob."OPERATOR_ID" = r."OPERATOR_ID"
             LEFT JOIN oca ON ob."OPERATOR_ID" = oca."OPERATOR_ID"
             JOIN "CALC"."V_MM_OPERATORS_OF_MM_GROUP" mm ON mm."OperatorId" = ob."OPERATOR_ID"
             LEFT JOIN "SOFTPHONE"."OperatorInfo" opi ON opi."OperatorId" = ob."OPERATOR_ID"
             LEFT JOIN clienttype_cte ws ON ws."OperatorId" = ob."OPERATOR_ID"
             LEFT JOIN operator_root_group ON operator_root_group."OperatorId" = ob."OPERATOR_ID"
             LEFT JOIN LATERAL ( SELECT sum(COALESCE(os."StatusDuration"::double precision, date_part('epoch'::text, timezone('UTC'::text, CURRENT_TIMESTAMP) - os."StartDate")))::numeric(20,0) AS "DURATION_CURRENT_STATUS_TODAY"
                                 FROM "KPI"."OPERATOR_STATUSES" os
                                 WHERE os."OperatorId" = ob."OPERATOR_ID" AND os."Status" = ob."STATUS_CODE"::text AND (os."EndDate" >= timezone('UTC'::text, CURRENT_DATE::timestamp with time zone) OR os."EndDate" IS NULL)) curr ON true
             LEFT JOIN call_control_agent_ready_cte ccar ON ccar."OperatorId" = ob."OPERATOR_ID"
             LEFT JOIN pending_status_cte ON pending_status_cte."OperatorId" = ob."OPERATOR_ID"
             LEFT JOIN cca ON cca."OPERATOR_ID" = ob."OPERATOR_ID"             
             LEFT JOIN "CALC"."INFRA_Operators_FIO" curator_logins ON curator_logins."ID" = cca."CuratorId"       
)
SELECT DISTINCT ON ("OperatorId") "OperatorId",
                                  "FIO",
                                  "LOGIN",
                                  "STATUS",
                                  "STATUS_CODE",
                                  "SECONDS_IN_STATUS",
                                  "QUEUES_LIST",
                                  "GROUPS_LIST",
                                  "RequestsInWorkCount",
                                  "RequestsInWorkIds",
                                  "Channel",
                                  "REQUEST_ID",
                                  "CAN_BE_PERSONAL_MANAGER",
                                  "CurrentSoftphoneLogin",
                                  "ActiveCallRequestId",
                                  "ClientType",
                                  "DURATION_CURRENT_STATUS_TODAY",
                                  "OperatorRootGroupId",
                                  "OperatorRootGroupName",
                                  "ReadyForCall",
                                  "PendingStatusName",
                                  "PendingStatusCode",
                                  "CURATOR_FIO",
                                  "CURATOR_ID" 
FROM res;

CREATE OR REPLACE VIEW "DATA_MARTS"."V_OPERATOR_STATUSES"
    AS
SELECT "OPERATOR_STATUSES"."OperatorId",
        "OPERATOR_STATUSES"."Status",
        "OPERATOR_STATUSES"."StartDate",
        "OPERATOR_STATUSES"."EndDate",
        "OPERATOR_STATUSES"."StatusDuration",
        "OPERATOR_STATUSES"."IsInitial"
FROM "KPI"."OPERATOR_STATUSES";

comment on view "DATA_MARTS"."V_OPERATOR_STATUSES" is 'Статистика по статусам оператора';
comment on column "DATA_MARTS"."V_OPERATOR_STATUSES"."OperatorId" is 'Идентификатор оператора';
comment on column "DATA_MARTS"."V_OPERATOR_STATUSES"."Status" is 'Код статуса';
comment on column "DATA_MARTS"."V_OPERATOR_STATUSES"."StatusDuration" is 'Время пребывания в статусе';
comment on column "DATA_MARTS"."V_OPERATOR_STATUSES"."IsInitial" is 'Признак начального статуса';
comment on column "DATA_MARTS"."V_OPERATOR_STATUSES"."StartDate" is 'Дата начала пребывания в статусе';
comment on column "DATA_MARTS"."V_OPERATOR_STATUSES"."EndDate" is 'Дата окончания пребывания в статусе';
