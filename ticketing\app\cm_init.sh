#!/bin/bash

echo -e "[$(date '+%d.%m.%Y %T.%3N')] Create service account user mappings" && tput sgr0

#includes
. /.config.sh || exit 1
. /.config-ticketing.sh || exit 1

#Export database password
export PGPASSWORD=${DBPASSWORD}

#Sleep
sleep 10

#Data init
# Заполняем для учётки service-account-product.service - d8009ea8-4408-43b3-b9ae-0a10e8a467f2
# Получить токен из KeyCloack
echo -e "[$(date '+%d.%m.%Y %T.%3N')] Get token from URL 'https://{transform:kcUrl}/realms/{transform:kcRealm}/protocol/openid-connect/token'." && tput sgr0

auth_token=$(curl -L 'https://{transform:kcUrl}/realms/{transform:kcRealm}/protocol/openid-connect/token' -H 'Content-Type: application/x-www-form-urlencoded' -d 'client_id=product.service' -d 'grant_type=client_credentials' -d 'client_secret=TMFVYLhrwwBQbYmGjK3zt53EYPzMiSha' | grep -Po '"access_token": *"\K(?:\\"|[^"])*') || exit 1
echo -e "[$(date '+%d.%m.%Y %T.%3N')] Get token operation complete" && tput sgr0

#ticketing_email
curl -X 'POST' 'https://{transform:AppHostname}/credman/utilization/CredentialsManaging/SetCredentials?applicationName=ticketing_email' -H 'Content-Type: application/json' -H "Authorization: Bearer ${auth_token}" -d '{"Password": "{transform:Ticketing.Hooks.Email.Password}"}' || exit 1
echo -e "[$(date '+%d.%m.%Y %T.%3N')] Set service account credentials for application ticketing_email complete" && tput sgr0

echo -e " Инициализация модуля Credential Manager успешно выполнена" && tput sgr0
exit 1