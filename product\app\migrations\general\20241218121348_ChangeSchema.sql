DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__KpiNotificationDbContextMigrations" WHERE "MigrationId" = '20241218121348_ChangeSchema') THEN
        IF NOT EXISTS(SELECT 1 FROM pg_namespace WHERE nspname = 'MAILINGS') THEN
            CREATE SCHEMA "MAILINGS";
        END IF;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__KpiNotificationDbContextMigrations" WHERE "MigrationId" = '20241218121348_ChangeSchema') THEN
    ALTER TABLE "PERIODIC_NOTIFICATION_STATE" SET SCHEMA "MAILINGS";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__KpiNotificationDbContextMigrations" WHERE "MigrationId" = '20241218121348_ChangeSchema') THEN
    ALTER TABLE "KPI_NOTIFICATION_STATE_PERIODICAL_NOTIFICATION_PARAMETERS" SET SCHEMA "MAILINGS";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__KpiNotificationDbContextMigrations" WHERE "MigrationId" = '20241218121348_ChangeSchema') THEN
    ALTER TABLE "KPI_NOTIFICATION_STATE_PERIODICAL_DELIVERY_SETTINGS" SET SCHEMA "MAILINGS";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__KpiNotificationDbContextMigrations" WHERE "MigrationId" = '20241218121348_ChangeSchema') THEN
    ALTER TABLE "KPI_NOTIFICATION_STATE_PERIODICAL_DELIVERY_CHANNEL_SETTINGS" SET SCHEMA "MAILINGS";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__KpiNotificationDbContextMigrations" WHERE "MigrationId" = '20241218121348_ChangeSchema') THEN
    ALTER TABLE "KPI_NOTIFICATION_STATE_PERIODICAL_DELIVERY_CHANNEL_RECEIVERS_SETTINGS" SET SCHEMA "MAILINGS";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__KpiNotificationDbContextMigrations" WHERE "MigrationId" = '20241218121348_ChangeSchema') THEN
    ALTER TABLE "KPI_NOTIFICATION_STATE_CRITICAL_NOTIFICATION_PARAMETERS" SET SCHEMA "MAILINGS";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__KpiNotificationDbContextMigrations" WHERE "MigrationId" = '20241218121348_ChangeSchema') THEN
    ALTER TABLE "KPI_NOTIFICATION_STATE_CRITICAL_DELIVERY_SETTINGS" SET SCHEMA "MAILINGS";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__KpiNotificationDbContextMigrations" WHERE "MigrationId" = '20241218121348_ChangeSchema') THEN
    ALTER TABLE "KPI_NOTIFICATION_STATE_CRITICAL_DELIVERY_CHANNEL_SETTINGS" SET SCHEMA "MAILINGS";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__KpiNotificationDbContextMigrations" WHERE "MigrationId" = '20241218121348_ChangeSchema') THEN
    ALTER TABLE "KPI_NOTIFICATION_STATE_CRITICAL_DELIVERY_CHANNEL_RECEIVERS_SETTINGS" SET SCHEMA "MAILINGS";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__KpiNotificationDbContextMigrations" WHERE "MigrationId" = '20241218121348_ChangeSchema') THEN
    ALTER TABLE "KPI_NOTIFICATION_STATE" SET SCHEMA "MAILINGS";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__KpiNotificationDbContextMigrations" WHERE "MigrationId" = '20241218121348_ChangeSchema') THEN
    UPDATE "MAILINGS"."KPI_NOTIFICATION_STATE_PERIODICAL_NOTIFICATION_PARAMETERS" SET "StringFormat" = '' WHERE "StringFormat" IS NULL;
    ALTER TABLE "MAILINGS"."KPI_NOTIFICATION_STATE_PERIODICAL_NOTIFICATION_PARAMETERS" ALTER COLUMN "StringFormat" SET NOT NULL;
    ALTER TABLE "MAILINGS"."KPI_NOTIFICATION_STATE_PERIODICAL_NOTIFICATION_PARAMETERS" ALTER COLUMN "StringFormat" SET DEFAULT '';
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__KpiNotificationDbContextMigrations" WHERE "MigrationId" = '20241218121348_ChangeSchema') THEN
    UPDATE "MAILINGS"."KPI_NOTIFICATION_STATE_PERIODICAL_NOTIFICATION_PARAMETERS" SET "QueueTitle" = '' WHERE "QueueTitle" IS NULL;
    ALTER TABLE "MAILINGS"."KPI_NOTIFICATION_STATE_PERIODICAL_NOTIFICATION_PARAMETERS" ALTER COLUMN "QueueTitle" SET NOT NULL;
    ALTER TABLE "MAILINGS"."KPI_NOTIFICATION_STATE_PERIODICAL_NOTIFICATION_PARAMETERS" ALTER COLUMN "QueueTitle" SET DEFAULT '';
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__KpiNotificationDbContextMigrations" WHERE "MigrationId" = '20241218121348_ChangeSchema') THEN
    UPDATE "MAILINGS"."KPI_NOTIFICATION_STATE_PERIODICAL_NOTIFICATION_PARAMETERS" SET "ContentType" = '' WHERE "ContentType" IS NULL;
    ALTER TABLE "MAILINGS"."KPI_NOTIFICATION_STATE_PERIODICAL_NOTIFICATION_PARAMETERS" ALTER COLUMN "ContentType" SET NOT NULL;
    ALTER TABLE "MAILINGS"."KPI_NOTIFICATION_STATE_PERIODICAL_NOTIFICATION_PARAMETERS" ALTER COLUMN "ContentType" SET DEFAULT '';
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__KpiNotificationDbContextMigrations" WHERE "MigrationId" = '20241218121348_ChangeSchema') THEN
    UPDATE "MAILINGS"."KPI_NOTIFICATION_STATE_PERIODICAL_NOTIFICATION_PARAMETERS" SET "Alias" = '' WHERE "Alias" IS NULL;
    ALTER TABLE "MAILINGS"."KPI_NOTIFICATION_STATE_PERIODICAL_NOTIFICATION_PARAMETERS" ALTER COLUMN "Alias" SET NOT NULL;
    ALTER TABLE "MAILINGS"."KPI_NOTIFICATION_STATE_PERIODICAL_NOTIFICATION_PARAMETERS" ALTER COLUMN "Alias" SET DEFAULT '';
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__KpiNotificationDbContextMigrations" WHERE "MigrationId" = '20241218121348_ChangeSchema') THEN
    UPDATE "MAILINGS"."KPI_NOTIFICATION_STATE_PERIODICAL_DELIVERY_SETTINGS" SET "Name" = '' WHERE "Name" IS NULL;
    ALTER TABLE "MAILINGS"."KPI_NOTIFICATION_STATE_PERIODICAL_DELIVERY_SETTINGS" ALTER COLUMN "Name" SET NOT NULL;
    ALTER TABLE "MAILINGS"."KPI_NOTIFICATION_STATE_PERIODICAL_DELIVERY_SETTINGS" ALTER COLUMN "Name" SET DEFAULT '';
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__KpiNotificationDbContextMigrations" WHERE "MigrationId" = '20241218121348_ChangeSchema') THEN
    UPDATE "MAILINGS"."KPI_NOTIFICATION_STATE_PERIODICAL_DELIVERY_SETTINGS" SET "CreatorName" = '' WHERE "CreatorName" IS NULL;
    ALTER TABLE "MAILINGS"."KPI_NOTIFICATION_STATE_PERIODICAL_DELIVERY_SETTINGS" ALTER COLUMN "CreatorName" SET NOT NULL;
    ALTER TABLE "MAILINGS"."KPI_NOTIFICATION_STATE_PERIODICAL_DELIVERY_SETTINGS" ALTER COLUMN "CreatorName" SET DEFAULT '';
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__KpiNotificationDbContextMigrations" WHERE "MigrationId" = '20241218121348_ChangeSchema') THEN
    UPDATE "MAILINGS"."KPI_NOTIFICATION_STATE_PERIODICAL_DELIVERY_CHANNEL_SETTINGS" SET "Sender" = '' WHERE "Sender" IS NULL;
    ALTER TABLE "MAILINGS"."KPI_NOTIFICATION_STATE_PERIODICAL_DELIVERY_CHANNEL_SETTINGS" ALTER COLUMN "Sender" SET NOT NULL;
    ALTER TABLE "MAILINGS"."KPI_NOTIFICATION_STATE_PERIODICAL_DELIVERY_CHANNEL_SETTINGS" ALTER COLUMN "Sender" SET DEFAULT '';
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__KpiNotificationDbContextMigrations" WHERE "MigrationId" = '20241218121348_ChangeSchema') THEN
    UPDATE "MAILINGS"."KPI_NOTIFICATION_STATE_PERIODICAL_DELIVERY_CHANNEL_SETTINGS" SET "DeliveryChannel" = '' WHERE "DeliveryChannel" IS NULL;
    ALTER TABLE "MAILINGS"."KPI_NOTIFICATION_STATE_PERIODICAL_DELIVERY_CHANNEL_SETTINGS" ALTER COLUMN "DeliveryChannel" SET NOT NULL;
    ALTER TABLE "MAILINGS"."KPI_NOTIFICATION_STATE_PERIODICAL_DELIVERY_CHANNEL_SETTINGS" ALTER COLUMN "DeliveryChannel" SET DEFAULT '';
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__KpiNotificationDbContextMigrations" WHERE "MigrationId" = '20241218121348_ChangeSchema') THEN
    UPDATE "MAILINGS"."KPI_NOTIFICATION_STATE_PERIODICAL_DELIVERY_CHANNEL_RECEIVERS_SETTINGS" SET "Address" = '' WHERE "Address" IS NULL;
    ALTER TABLE "MAILINGS"."KPI_NOTIFICATION_STATE_PERIODICAL_DELIVERY_CHANNEL_RECEIVERS_SETTINGS" ALTER COLUMN "Address" SET NOT NULL;
    ALTER TABLE "MAILINGS"."KPI_NOTIFICATION_STATE_PERIODICAL_DELIVERY_CHANNEL_RECEIVERS_SETTINGS" ALTER COLUMN "Address" SET DEFAULT '';
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__KpiNotificationDbContextMigrations" WHERE "MigrationId" = '20241218121348_ChangeSchema') THEN
    UPDATE "MAILINGS"."KPI_NOTIFICATION_STATE_CRITICAL_NOTIFICATION_PARAMETERS" SET "StringFormat" = '' WHERE "StringFormat" IS NULL;
    ALTER TABLE "MAILINGS"."KPI_NOTIFICATION_STATE_CRITICAL_NOTIFICATION_PARAMETERS" ALTER COLUMN "StringFormat" SET NOT NULL;
    ALTER TABLE "MAILINGS"."KPI_NOTIFICATION_STATE_CRITICAL_NOTIFICATION_PARAMETERS" ALTER COLUMN "StringFormat" SET DEFAULT '';
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__KpiNotificationDbContextMigrations" WHERE "MigrationId" = '20241218121348_ChangeSchema') THEN
    UPDATE "MAILINGS"."KPI_NOTIFICATION_STATE_CRITICAL_NOTIFICATION_PARAMETERS" SET "QueueTitle" = '' WHERE "QueueTitle" IS NULL;
    ALTER TABLE "MAILINGS"."KPI_NOTIFICATION_STATE_CRITICAL_NOTIFICATION_PARAMETERS" ALTER COLUMN "QueueTitle" SET NOT NULL;
    ALTER TABLE "MAILINGS"."KPI_NOTIFICATION_STATE_CRITICAL_NOTIFICATION_PARAMETERS" ALTER COLUMN "QueueTitle" SET DEFAULT '';
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__KpiNotificationDbContextMigrations" WHERE "MigrationId" = '20241218121348_ChangeSchema') THEN
    UPDATE "MAILINGS"."KPI_NOTIFICATION_STATE_CRITICAL_NOTIFICATION_PARAMETERS" SET "ContentType" = '' WHERE "ContentType" IS NULL;
    ALTER TABLE "MAILINGS"."KPI_NOTIFICATION_STATE_CRITICAL_NOTIFICATION_PARAMETERS" ALTER COLUMN "ContentType" SET NOT NULL;
    ALTER TABLE "MAILINGS"."KPI_NOTIFICATION_STATE_CRITICAL_NOTIFICATION_PARAMETERS" ALTER COLUMN "ContentType" SET DEFAULT '';
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__KpiNotificationDbContextMigrations" WHERE "MigrationId" = '20241218121348_ChangeSchema') THEN
    UPDATE "MAILINGS"."KPI_NOTIFICATION_STATE_CRITICAL_NOTIFICATION_PARAMETERS" SET "Alias" = '' WHERE "Alias" IS NULL;
    ALTER TABLE "MAILINGS"."KPI_NOTIFICATION_STATE_CRITICAL_NOTIFICATION_PARAMETERS" ALTER COLUMN "Alias" SET NOT NULL;
    ALTER TABLE "MAILINGS"."KPI_NOTIFICATION_STATE_CRITICAL_NOTIFICATION_PARAMETERS" ALTER COLUMN "Alias" SET DEFAULT '';
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__KpiNotificationDbContextMigrations" WHERE "MigrationId" = '20241218121348_ChangeSchema') THEN
    UPDATE "MAILINGS"."KPI_NOTIFICATION_STATE_CRITICAL_DELIVERY_SETTINGS" SET "Name" = '' WHERE "Name" IS NULL;
    ALTER TABLE "MAILINGS"."KPI_NOTIFICATION_STATE_CRITICAL_DELIVERY_SETTINGS" ALTER COLUMN "Name" SET NOT NULL;
    ALTER TABLE "MAILINGS"."KPI_NOTIFICATION_STATE_CRITICAL_DELIVERY_SETTINGS" ALTER COLUMN "Name" SET DEFAULT '';
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__KpiNotificationDbContextMigrations" WHERE "MigrationId" = '20241218121348_ChangeSchema') THEN
    UPDATE "MAILINGS"."KPI_NOTIFICATION_STATE_CRITICAL_DELIVERY_SETTINGS" SET "CreatorName" = '' WHERE "CreatorName" IS NULL;
    ALTER TABLE "MAILINGS"."KPI_NOTIFICATION_STATE_CRITICAL_DELIVERY_SETTINGS" ALTER COLUMN "CreatorName" SET NOT NULL;
    ALTER TABLE "MAILINGS"."KPI_NOTIFICATION_STATE_CRITICAL_DELIVERY_SETTINGS" ALTER COLUMN "CreatorName" SET DEFAULT '';
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__KpiNotificationDbContextMigrations" WHERE "MigrationId" = '20241218121348_ChangeSchema') THEN
    UPDATE "MAILINGS"."KPI_NOTIFICATION_STATE_CRITICAL_DELIVERY_CHANNEL_SETTINGS" SET "Sender" = '' WHERE "Sender" IS NULL;
    ALTER TABLE "MAILINGS"."KPI_NOTIFICATION_STATE_CRITICAL_DELIVERY_CHANNEL_SETTINGS" ALTER COLUMN "Sender" SET NOT NULL;
    ALTER TABLE "MAILINGS"."KPI_NOTIFICATION_STATE_CRITICAL_DELIVERY_CHANNEL_SETTINGS" ALTER COLUMN "Sender" SET DEFAULT '';
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__KpiNotificationDbContextMigrations" WHERE "MigrationId" = '20241218121348_ChangeSchema') THEN
    UPDATE "MAILINGS"."KPI_NOTIFICATION_STATE_CRITICAL_DELIVERY_CHANNEL_SETTINGS" SET "DeliveryChannel" = '' WHERE "DeliveryChannel" IS NULL;
    ALTER TABLE "MAILINGS"."KPI_NOTIFICATION_STATE_CRITICAL_DELIVERY_CHANNEL_SETTINGS" ALTER COLUMN "DeliveryChannel" SET NOT NULL;
    ALTER TABLE "MAILINGS"."KPI_NOTIFICATION_STATE_CRITICAL_DELIVERY_CHANNEL_SETTINGS" ALTER COLUMN "DeliveryChannel" SET DEFAULT '';
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__KpiNotificationDbContextMigrations" WHERE "MigrationId" = '20241218121348_ChangeSchema') THEN
    UPDATE "MAILINGS"."KPI_NOTIFICATION_STATE_CRITICAL_DELIVERY_CHANNEL_RECEIVERS_SETTINGS" SET "Address" = '' WHERE "Address" IS NULL;
    ALTER TABLE "MAILINGS"."KPI_NOTIFICATION_STATE_CRITICAL_DELIVERY_CHANNEL_RECEIVERS_SETTINGS" ALTER COLUMN "Address" SET NOT NULL;
    ALTER TABLE "MAILINGS"."KPI_NOTIFICATION_STATE_CRITICAL_DELIVERY_CHANNEL_RECEIVERS_SETTINGS" ALTER COLUMN "Address" SET DEFAULT '';
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__KpiNotificationDbContextMigrations" WHERE "MigrationId" = '20241218121348_ChangeSchema') THEN
    INSERT INTO "__KpiNotificationDbContextMigrations" ("MigrationId", "ProductVersion")
    VALUES ('20241218121348_ChangeSchema', '8.0.2');
    END IF;
END $EF$;
