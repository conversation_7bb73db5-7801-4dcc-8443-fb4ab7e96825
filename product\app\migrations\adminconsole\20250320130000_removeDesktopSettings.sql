
--	Модули
DELETE FROM "AWP_INFRA"."Modules"
WHERE "ClientTypes" = 1;

UPDATE "AWP_INFRA"."Modules"
SET "ClientTypes" = 2
WHERE "ClientTypes" = 3;

--	Приложения
DELETE FROM "AWP_INFRA"."AppInit_Control"
WHERE "Id" in (SELECT "Id" FROM "AWP_INFRA"."Applications" WHERE "ClientTypes" = 1);

DELETE FROM "AWP_INFRA"."AppInit_External"
WHERE "Id" in (SELECT "Id" FROM "AWP_INFRA"."Applications" WHERE "ClientTypes" = 1);

DELETE FROM "AWP_INFRA"."AppInit_Web"
WHERE "Id" in (SELECT "Id" FROM "AWP_INFRA"."Applications" WHERE "ClientTypes" = 1);

DELETE FROM "AWP_INFRA"."ApplicationInitializations"
WHERE "Id" in (SELECT "Id" FROM "AWP_INFRA"."Applications" WHERE "ClientTypes" = 1);

DELETE FROM "AWP_INFRA"."Applications"
WHERE "ClientTypes" = 1;

UPDATE "AWP_INFRA"."Applications"
SET "ClientTypes" = 2
WHERE "ClientTypes" = 3;

--	Разметки
DELETE FROM "AWP_INFRA"."Layouts";

--	и нафиг вообще эту вкладку в КА
DELETE FROM "AWP_INFRA"."AdministrationPermissions"
WHERE "AdministrationObjectId" in (SELECT "Id" FROM "AWP_INFRA"."AdministrationObjects" WHERE "ModuleName" = 'LayoutsModule');

DELETE FROM "AWP_INFRA"."AdministrationObjects"
WHERE "ModuleName" = 'LayoutsModule';

--	Сценарии автоматизации
DELETE FROM "AWP_INFRA"."Workflows"
WHERE "ClientTypes" = 1;

-- Настройки
DELETE FROM "AWP_INFRA"."Configurations"
WHERE "Name" in
(
	N'ApplicationConfiguration',
	N'CjmSettings',
	N'CorrespondenceConfiguration',
	N'FileManagerSettings.Product',
	N'InfoRequestGridSettings',
	N'OperatorTimelineSiteUrl',
	N'RequestProcessingRequestGridSettings',
	N'RequestSupervisingOperatorGridSettings',
	N'ScreenWatcherUrl',
	N'ServicesConfiguration',
	N'StatusTransitionSettings',
	N'SupervisorMonitoringUrl',
	N'TimeOffset',
	N'UnloadRequestsDialogsCount',
	N'Тематики обращения',
	N'Настройки черных списков для каналов',
	N'Настройка загрузки вложений',
	N'FilterDialogScriptsByDuration',
	N'DialogScriptsEnabled',
	N'RequestOperationTabsOrder',
	N'Черный список'
);
