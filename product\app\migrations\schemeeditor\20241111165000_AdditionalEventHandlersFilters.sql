ALTER TABLE "CRPM_CFG"."RequestEventHandlers" ADD "BranchId" uuid;

ALTER TABLE "CRPM_CFG"."RequestEventHandlers" ADD "CampaignId" smallint;

ALTER TABLE "CRPM_CFG"."RequestEventHandlers" ADD "Channel" character varying(500);

ALTER TABLE "CRPM_CFG"."RequestEventHandlers" ADD "QueueId" smallint;

CREATE INDEX "IX_RequestEventHandlers_CampaignId" ON "CRPM_CFG"."RequestEventHandlers" ("CampaignId");

CREATE INDEX "IX_RequestEventHandlers_QueueId" ON "CRPM_CFG"."RequestEventHandlers" ("QueueId");

ALTER TABLE "CRPM_CFG"."RequestEventHandlers" ADD CONSTRAINT "FK_RequestEventHandlers_RequestCampaigns_CampaignId" FOREIGN KEY ("CampaignId") REFERENCES "CRPM_CFG"."RequestCampaigns" ("Id");

ALTER TABLE "CRPM_CFG"."RequestEventHandlers" ADD CONSTRAINT "FK_RequestEventHandlers_RequestQueues_QueueId" FOREIGN KEY ("QueueId") REFERENCES "CRPM_CFG"."RequestQueues" ("Id");

INSERT INTO "CRPM_CFG"."__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241111103512_AdditionalEventHandlersFilters', '8.0.2');

ALTER TABLE "CRPM_CFG_AUD"."REQUEST_EVENT_HANDLER_CHANGES" ADD "BRANCH_ID" uuid;

ALTER TABLE "CRPM_CFG_AUD"."REQUEST_EVENT_HANDLER_CHANGES" ADD "CAMPAIGN_ID" smallint;

ALTER TABLE "CRPM_CFG_AUD"."REQUEST_EVENT_HANDLER_CHANGES" ADD "CHANNEL" character varying(500);

ALTER TABLE "CRPM_CFG_AUD"."REQUEST_EVENT_HANDLER_CHANGES" ADD "QUEUE_ID" smallint;

INSERT INTO "CRPM_CFG_AUD"."__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241111105059_AdditionalEventHandlersFilters', '8.0.2');
