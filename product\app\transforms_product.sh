#!/bin/bash

echo "executing product transforms file..."

. /.config.sh || exit 1

if [ -z ${LBDNS} ]; then
  APPDNS=${HOSTNAMELOCAL}.${FULLDOMAIN}
else
  APPDNS=${LBDNS}
fi

cd /app
inject_variables_sed=$(mktemp inject_variables_sed.XXXXXXXXXX)
configs_folder=./.
transforms=(
"s#{transform:serviceAccount}#${SERVICEACCOUNT}#g"
"s#{transform:AppHostname}#${APPDNS}#g"
"s#{transform:kcUrl}#${KCDNS}#g"
"s#{transform:kcRealm}#${KCREALM}#g"
"s#{transform:DbServer}#${DBSERVER}#g"
"s#{transform:DbServerPort}#${DBSERVERPORT}#g"
"s#{transform:DbName}#${DBNAMEPRODUCT}#g"
"s#{transform:DbUser}#${DBUSER}#g"
"s#{transform:DbPassword}#${DBPASSWORD}#g"
"s#{transform:RedisServer}#${REDISSERVER}#g"
"s#{transform:RedisServerPort}#${REDISPORT}#g"
"s#{transform:RedisServerPassword}#${REDISPASS}#g"
"s#{transform:rabbitDmzHost}#${RABBITSERVERDMZ}#g"
"s#{transform:rabbitDmzHostPort}#${RABBITPORTDMZ}#g"
"s#{transform:rabbitDmzLogin}#${RABBITUSERDMZ}#g"
"s#{transform:rabbitDmzPassword}#${RABBITPASSDMZ}#g"
"s#{transform:rabbitDmzVh}#${RABBITVHDMZ}#g"
"s#{transform:rabbitHost}#${RABBITSERVER}#g"
"s#{transform:rabbitHostPort}#${RABBITPORT}#g"
"s#{transform:rabbitLogin}#${RABBITUSER}#g"
"s#{transform:rabbitPassword}#${RABBITPASS}#g"
"s#{transform:rabbitVh}#${RABBITVH}#g"
"s#{transform:FullDomainName}#${FULLDOMAIN}#g"
"s#{transform:AdAdress}#${ADADRESS}#g"
"s#{transform:DmzDNS}#${DMZAPPADRESS}#g"
"s#{transform:FSDNS}#${FSDNS}#g"
"s#{transform:FileShare}#${FILESHARE}#g"
"s#{transform:FileShareVideo}#${FILESHAREVIDEO}#g"
"s#{transform:certpath}#${CERTPATH}#g"
"s#{transform:certkey}#${KEYPATH}#g"
"s#{transform:adminUserGuid}#${ADMINGUID}#g"
"s#{transform:ServiceUserGuid}#${SERVICEACCOUNTGUID}#g"
"s#{transform:firstAdmin}#${FIRSTADMIN}#g"
"s#{transform:supersetserver}#${SUPERSETSERVER}#g"
"s#{transform:serviceAccount}#${SERVICEACCOUNT}@${FULLDOMAIN}#g"
"s#{transform:serviceAccountPassword}#${SERVICEACCOUNTPASSWORD}#g"
"s#{transform:serviceAccountKinit}#${KINITACCOUNT}#g"
)
for transform in ${transforms[@]}; 
    do
        echo "$transform" >> $inject_variables_sed
    done
find $configs_folder \( -type f -name '*.json' -o -name '*.sh' -o -name '*.config' -o -name '*.sql' -o -name '*.conf' -o -name '*.service' \) -exec sed -i -f $inject_variables_sed {} \;
rm -rf $inject_variables_sed
