-- Удалить из настроек возможность настройки автообработчиков для:
-- Возврат из отложенных
UPDATE "AWP_INFRA"."Configurations"
SET "Value" = (
    SELECT jsonb_pretty(jsonb_set(
            "Value"::jsonb,
            '{AutoHandlers}',
            (
                SELECT jsonb_agg(elem)
                FROM jsonb_array_elements(("Value"::jsonb)->'AutoHandlers') elem
                WHERE elem->>'IsEnabledValueAttributeCode' <> 'Queue.PostponeReturned'
        )::jsonb
    ))::text
)
WHERE "Name" = 'AdministrationServiceConfiguration';

-- Перевод на оператора
UPDATE "AWP_INFRA"."Configurations"
SET "Value" = (
    SELECT jsonb_pretty(jsonb_set(
            "Value"::jsonb,
            '{AutoHandlers}',
            (
                SELECT jsonb_agg(elem)
                FROM jsonb_array_elements(("Value"::jsonb)->'AutoHandlers') elem
                WHERE elem->>'IsEnabledValueAttributeCode' <> 'Queue.RedirectToOperator.ShouldSendAutoReply'
        )::jsonb
    ))::text
)
WHERE "Name" = 'AdministrationServiceConfiguration';

-- Перевод в другую очередь
UPDATE "AWP_INFRA"."Configurations"
SET "Value" = (
    SELECT jsonb_pretty(jsonb_set(
            "Value"::jsonb,
            '{AutoHandlers}',
            (
                SELECT jsonb_agg(elem)
                FROM jsonb_array_elements(("Value"::jsonb)->'AutoHandlers') elem
                WHERE elem->>'IsEnabledValueAttributeCode' <> 'Queue.RedirectToQueue.ShouldSendAutoReply'
        )::jsonb
    ))::text
)
WHERE "Name" = 'AdministrationServiceConfiguration';

-- Если уже есть очереди в которых это настроено - выключить отправку
UPDATE "CRPM_CFG"."CustomAttributes"
SET "Value"='False'
WHERE "Key"='Queue.PostponeReturned';

UPDATE "CRPM_CFG"."CustomAttributes"
SET "Value"='False'
WHERE "Key"='Queue.RedirectToOperator.ShouldSendAutoReply';

UPDATE "CRPM_CFG"."CustomAttributes"
SET "Value"='False'
WHERE "Key"='Queue.RedirectToQueue.ShouldSendAutoReply';
