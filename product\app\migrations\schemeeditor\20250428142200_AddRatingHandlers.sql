UPDATE "CRPM_CFG"."RequestStateWorks"
SET "GenerateEvents" = true
WHERE  "RequestStateId" = (SELECT "Id" FROM "CRPM_CFG"."RequestStates" WHERE "Code"='CHATBOT' AND "SchemeId"=1) 
OR "RequestStateId" = (SELECT "Id" FROM "CRPM_CFG"."RequestStates" WHERE "Code"='SCRIPTBOT' AND "SchemeId"=1);



INSERT INTO "CRPM_CFG"."RequestEventHandlers"
("RowVersion", "Title", "Configuration", "TypeCode", "IsEnabled", "EventType", "StreamId", "SchemeId", "StateId", "StatusId", "BranchId", "CampaignId", "Channel", "QueueId")
SELECT 638676185683784973, '[RatingBot] Простановка CompleteResultCode для интеллектуального бота', '<?xml version="1.0" encoding="utf-16"?>
<SaveCloseResultCodeEventHandlerConfig />', 'SaveCloseResultCodeEventHandler', true, 'WorkStatusChanged', NULL, NULL, (SELECT "Id" FROM "CRPM_CFG"."RequestStates" WHERE "Code"='CHATBOT' AND "SchemeId"=1), NULL, NULL, NULL, NULL, NULL
WHERE NOT EXISTS(SELECT "Id" FROM "CRPM_CFG"."RequestEventHandlers" WHERE "TypeCode" = 'SaveCloseResultCodeEventHandler' AND "EventType" = 'WorkStatusChanged' AND "Title" = '[RatingBot] Простановка CompleteResultCode для интеллектуального бота' );

INSERT INTO "CRPM_CFG"."RequestEventHandlers"
("RowVersion", "Title", "Configuration", "TypeCode", "IsEnabled", "EventType", "StreamId", "SchemeId", "StateId", "StatusId", "BranchId", "CampaignId", "Channel", "QueueId")
SELECT 638676185683784973, '[RatingBot] Простановка CompleteResultCode для сценарного бота', '<?xml version="1.0" encoding="utf-16"?>
<SaveCloseResultCodeEventHandlerConfig />', 'SaveCloseResultCodeEventHandler', true, 'WorkStatusChanged', NULL, NULL, (SELECT "Id" FROM "CRPM_CFG"."RequestStates" WHERE "Code"='SCRIPTBOT' AND "SchemeId"=1), NULL, NULL, NULL, NULL, NULL
WHERE NOT EXISTS(SELECT "Id" FROM "CRPM_CFG"."RequestEventHandlers" WHERE "TypeCode" = 'SaveCloseResultCodeEventHandler' AND "EventType" = 'WorkStatusChanged' AND "Title" = '[RatingBot] Простановка CompleteResultCode для сценарного бота' );