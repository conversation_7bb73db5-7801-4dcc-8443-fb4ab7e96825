include:
  - project: 'products/avelana/infrastructure/pipelines'
    file: 'common.yml'
  - project: 'products/avelana/infrastructure/pipelines'
    file: 'read_set_release_parameters.yml'

image: hub-proxy.avelana.ru/docker:stable

stages:
  - backup
  - prepare
  - build

variables:
    INITIAL_RELEASE_DBSERVER:
        value: "*********"
        description: "Хост сервера с БД INITIAL_RELEASE"
    INITIAL_RELEASE_DBPORT:
        value: "5433"
        description: "Порт сервера с БД INITIAL_RELEASE"
    INITIAL_RELEASE_DBUSER:
        value: "postgres"
        description: "Логин пользователя для сервера БД INITIAL_RELEASE"
    INITIAL_RELEASE_DBPASSWORD:
        description: "Пароль пользователя для сервера БД INITIAL_RELEASE"
    INITIAL_RELEASE_PRODUCT_DBNAME:
        value: "INITIAL_RELEASE_product"
        description: "Имя БД INITIAL-RELEASE продукта"
    INITIAL_RELEASE_TICKETING_DBNAME:
        value: "INITIAL_RELEASE_ticketing"
        description: "Имя БД INITIAL-RELEASE тикетинга"
    BASE_REGISTRY_URL: hub.avelana.ru



backup product:
  stage: backup
  rules:
    - if: $CI_COMMIT_REF_PROTECTED == "true"
      when: on_success
    - if: $CI_PIPELINE_SOURCE == "web"
      when: on_success
    - when: never
  image: hub-proxy.avelana.ru/postgres:16
  before_script:
    - apt -y update && apt -y install curl
  script:
    - export PGPASSWORD=${INITIAL_RELEASE_DBPASSWORD}
    - mkdir -p backups
    - |
      pg_dump -h ${INITIAL_RELEASE_DBSERVER} -p ${INITIAL_RELEASE_DBPORT} -U ${INITIAL_RELEASE_DBUSER} --no-owner -Fp ${INITIAL_RELEASE_PRODUCT_DBNAME} > backups/product.sql

  artifacts:
    paths:
      - backups

backup ticketing:
 stage: backup
 rules:
    - if: $CI_COMMIT_REF_PROTECTED == "true"
      when: on_success
    - if: $CI_PIPELINE_SOURCE == "web"
      when: on_success
    - when: never
 image: hub-proxy.avelana.ru/postgres:16
 before_script:
   - apt -y update && apt -y install curl
 script:
   - export PGPASSWORD=${INITIAL_RELEASE_DBPASSWORD}
   - mkdir -p backups
   - |
     pg_dump -h ${INITIAL_RELEASE_DBSERVER} -p ${INITIAL_RELEASE_DBPORT} -U ${INITIAL_RELEASE_DBUSER} --no-owner -Fp ${INITIAL_RELEASE_TICKETING_DBNAME} > backups/ticketing.sql

 artifacts:
   paths:
     - backups


publish docker image - product:
  stage: build
  rules:
    - if: $CI_COMMIT_REF_PROTECTED == "true"
      when: on_success
    - if: $CI_PIPELINE_SOURCE == "web"
      when: on_success
    - when: never
  variables:
    DOCKER_TLS_CERTDIR: ""
    #DOCKER_HOST: "tcp://docker:2375"
    BASE_REGISTRY_URL: hub.avelana.ru
    SERVICE_REGISTRY_PATH: infrastructure
    SERVICE_REGISTRY_NAME: database
    PRODUCT_REGISTRY_PATH: products/avelana
    PRODUCT_RELEASE_DOCKER_IMAGES_PATH: $PRODUCT_REGISTRY_PATH/$RELEASE_DOCKER_IMAGES_PATH
    DOCKER_IMAGE_NAME: $BASE_REGISTRY_URL/$PRODUCT_RELEASE_DOCKER_IMAGES_PATH/$SERVICE_REGISTRY_PATH/$SERVICE_REGISTRY_NAME:$RELEASE_DOCKER_IMAGES_TAG
    DOCKER_IMAGE_NAME_LATEST: $BASE_REGISTRY_URL/$PRODUCT_RELEASE_DOCKER_IMAGES_PATH/$SERVICE_REGISTRY_PATH/$SERVICE_REGISTRY_NAME:latest
  services:
    - hub-proxy.avelana.ru/docker:dind
  before_script:
    - echo "$BASE_REGISTRY_PASSWORD" | docker login -u "$BASE_REGISTRY_USER" --password-stdin  $BASE_REGISTRY_URL
  script:
    - cp -rf ./backups ./product/
    - cd ./product
    - ls -lah
    - docker build -t "$DOCKER_IMAGE_NAME" .
    - docker push "$DOCKER_IMAGE_NAME"
    - docker tag "$DOCKER_IMAGE_NAME" "$DOCKER_IMAGE_NAME_LATEST"
    - docker push "$DOCKER_IMAGE_NAME_LATEST"

  needs:
    - job: backup product
    - job: read_set_release_parameters

publish docker image - ticketing:
  stage: build
  rules:
    - if: $CI_COMMIT_REF_PROTECTED == "true"
      when: on_success
    - if: $CI_PIPELINE_SOURCE == "web"
      when: on_success
    - when: never
  variables:
    DOCKER_TLS_CERTDIR: ""
    #DOCKER_HOST: "tcp://docker:2375"
    BASE_REGISTRY_URL: hub.avelana.ru
    PRODUCT_REGISTRY_NAME: products/avelana
    SERVICE_REGISTRY_PATH: infrastructure
    SERVICE_REGISTRY_NAME: database-ticketing
    PRODUCT_REGISTRY_PATH: products/avelana
    PRODUCT_RELEASE_DOCKER_IMAGES_PATH: $PRODUCT_REGISTRY_PATH/$RELEASE_DOCKER_IMAGES_PATH
    DOCKER_IMAGE_NAME: $BASE_REGISTRY_URL/$PRODUCT_RELEASE_DOCKER_IMAGES_PATH/$SERVICE_REGISTRY_PATH/$SERVICE_REGISTRY_NAME:$RELEASE_DOCKER_IMAGES_TAG
    DOCKER_IMAGE_NAME_LATEST: $BASE_REGISTRY_URL/$PRODUCT_RELEASE_DOCKER_IMAGES_PATH/$SERVICE_REGISTRY_PATH/$SERVICE_REGISTRY_NAME:latest
  services:
    - hub-proxy.avelana.ru/docker:dind
  before_script:
    - echo "$BASE_REGISTRY_PASSWORD" | docker login -u "$BASE_REGISTRY_USER" --password-stdin  $BASE_REGISTRY_URL
  script:
    - cp -rf ./backups/ ./ticketing/
    - cd ./ticketing
    - docker build -t "$DOCKER_IMAGE_NAME" .
    - docker push "$DOCKER_IMAGE_NAME"
    - docker tag "$DOCKER_IMAGE_NAME" "$DOCKER_IMAGE_NAME_LATEST"
    - docker push "$DOCKER_IMAGE_NAME_LATEST"

  needs:
    - job: backup ticketing
    - job: read_set_release_parameters

format_check:
  stage: .pre
  script:
    - echo "Проверка наличия пакетных менеджеров и установка утилиты file"
    - |
      if command -v apt-get >/dev/null; then
        echo "Найден apt-get, установка file"
        apt-get update && apt-get install -y file
      elif command -v apk >/dev/null; then
        echo "Найден apk, установка file"
        apk add --no-cache file
      elif command -v yum >/dev/null; then
        echo "Найден yum, установка file"
        yum install -y file
      elif command -v dnf >/dev/null; then
        echo "Найден dnf, установка file"
        dnf install -y file
      else
        echo "Ошибка: Не найден ни один пакетный менеджер (apt-get, apk, yum, dnf)"
        exit 1
      fi
    - find . -type f -name "*.sql" -exec file {} \;
    - echo "Проверка кодировки файлов (*.sql) на UTF-8 без BOM"
    - find . -type f -name "*.sql" -exec file {} \; | (grep "BOM" || grep -v -E "UTF-8|ASCII|exported SGML document") && exit 1 || echo "Все SQL-файлы в кодировке UTF-8 без BOM"
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
