DO $addForceOperatorAssignmentDefaultValue$
BEGIN
RAISE NOTICE '[addForceOperatorAssignmentDefaultValue] STARTED';
INSERT INTO "AWP_INFRA"."Configurations" ("Id", "Name", "Value", "Description")
SELECT uuid_generate_v4()::uuid, 'ForceOperatorAssignmentDefaultValue', 'false', 'Значение по умолчанию для чекбокса "Распределить вне очереди"'
WHERE NOT EXISTS (
  SELECT "Name" FROM "AWP_INFRA"."Configurations"
  WHERE "Name"='ForceOperatorAssignmentDefaultValue'
);
RAISE NOTICE '[addForceOperatorAssignmentDefaultValue] FINISHED';
END $addForceOperatorAssignmentDefaultValue$;
