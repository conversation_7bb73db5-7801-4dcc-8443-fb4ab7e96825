#!/bin/bash

#includes
. /.config.sh || exit 1

shopt -s nocasematch
echo -e "[$(date '+%d.%m.%Y %T.%3N')] Restoring databases" && tput sgr0

#Export database password
export PGPASSWORD=${DBPASSWORD}

#Database creation
psql -h ${DBSERVER} -p ${DBSERVERPORT} -U ${DBUSER} -d postgres -c "CREATE DATABASE ${DBNAMEPRODUCT};" || exit 1
echo -e "[$(date '+%d.%m.%Y %T.%3N')] Creating database ${DBNAMEPRODUCT} complete" && tput sgr0

psql -h ${DBSERVER} -p ${DBSERVERPORT} -U ${DBUSER} -d ${DBNAMEPRODUCT} -f /backups/product.sql || exit 1
echo -e "[$(date '+%d.%m.%Y %T.%3N')] Restoring database ${DBNAMEPRODUCT} complete" && tput sgr0

echo -e "[$(date '+%d.%m.%Y %T.%3N')] Restoring databases complete" && tput sgr0
echo -e "[$(date '+%d.%m.%Y %T.%3N')] Applying databases changes" && tput sgr0

#Apply scripts
cd /app/db_scripts

psql -h ${DBSERVER} -p ${DBSERVERPORT} -U ${DBUSER} -d ${DBNAMEPRODUCT} -f Credman_app_user_mappings.sql || exit 1
echo -e "[$(date '+%d.%m.%Y %T.%3N')] Apply sql-script Credman_app_user_mappings complete" && tput sgr0

psql -h ${DBSERVER} -p ${DBSERVERPORT} -U ${DBUSER} -d ${DBNAMEPRODUCT} -f Transform_AUTOMATION_SERVICES_RestProperties.sql || exit 1
echo -e "[$(date '+%d.%m.%Y %T.%3N')] Apply sql-script Transform_AUTOMATION_SERVICES_RestProperties complete" && tput sgr0

psql -h ${DBSERVER} -p ${DBSERVERPORT} -U ${DBUSER} -d ${DBNAMEPRODUCT} -f Transform_AWP_AppInit_Web_StartUrl.sql || exit 1
echo -e "[$(date '+%d.%m.%Y %T.%3N')] Apply sql-script Transform_AWP_AppInit_Web_StartUrl complete" && tput sgr0

psql -h ${DBSERVER} -p ${DBSERVERPORT} -U ${DBUSER} -d ${DBNAMEPRODUCT} -f Transform_AWP_Configuration_Value.sql || exit 1
echo -e "[$(date '+%d.%m.%Y %T.%3N')] Apply sql-script Transform_AWP_Configuration_Value complete" && tput sgr0

psql -h ${DBSERVER} -p ${DBSERVERPORT} -U ${DBUSER} -d ${DBNAMEPRODUCT} -f Transform_AWP_Modules_Initialization.sql || exit 1
echo -e "[$(date '+%d.%m.%Y %T.%3N')] Apply sql-script Transform_AWP_Modules_Initialization complete" && tput sgr0

psql -h ${DBSERVER} -p ${DBSERVERPORT} -U ${DBUSER} -d ${DBNAMEPRODUCT} -f Transform_AWP_WebClientAppInits_ComponentInit.sql || exit 1
echo -e "[$(date '+%d.%m.%Y %T.%3N')] Apply sql-script Transform_AWP_WebClientAppInits_ComponentInit complete" && tput sgr0

psql -h ${DBSERVER} -p ${DBSERVERPORT} -U ${DBUSER} -d ${DBNAMEPRODUCT} -f Transform_AWP_Workflows_SerializedValue.sql || exit 1
echo -e "[$(date '+%d.%m.%Y %T.%3N')] Apply sql-script Transform_AWP_Workflows_SerializedValue complete" && tput sgr0

psql -h ${DBSERVER} -p ${DBSERVERPORT} -U ${DBUSER} -d ${DBNAMEPRODUCT} -f Transform_RIS_VALUES_CUSTOM_ATTRIBUTES.sql || exit 1
echo -e "[$(date '+%d.%m.%Y %T.%3N')] Apply sql-script Transform_RIS_VALUES_CUSTOM_ATTRIBUTES complete" && tput sgr0

psql -h ${DBSERVER} -p ${DBSERVERPORT} -U ${DBUSER} -d postgres -f MakePostgresJob.sql || exit 1
echo -e "[$(date '+%d.%m.%Y %T.%3N')] Apply sql-script MakePostgresJob complete" && tput sgr0

echo -e "[$(date '+%d.%m.%Y %T.%3N')] Applying database changes completed" && tput sgr0
echo -e " Инициализация базы данных успешно выполнена" && tput sgr0
exit 1