DROP VIEW "CALC"."V_OPERATORS_INFO";
DROP VIEW "CALC"."V_OPERATOR_REQUEST_INFO_V2";
DROP FUNCTION "CALC"."F_OPERATORS_KPI_INFO"(timestamp, timestamp);

CREATE OR REPLACE FUNCTION "CALC"."F_OPERATORS_KPI_INFO"(_startdate timestamp without time zone DEFAULT timezone('UTC'::text, (CURRENT_DATE)::timestamp with time zone), _enddate timestamp without time zone DEFAULT (timezone('UTC'::text, (CURRENT_DATE)::timestamp with time zone) + '1 day'::interval))
 RETURNS TABLE("OperatorId" uuid, "FIO" text, "LOGIN" character varying, "CURATOR_ID" uuid, "CURATOR_FIO" text, "QUEUES_LIST" text, "CURRENT_STATUS" text, "SECONDS_IN_CURRENT_STATUS" numeric, "START_TIME" timestamp without time zone, "PAUSE_DURATION" bigint, "ONLINE_DURATION" bigint, "ONLINE_DURATION_TEXT" character varying, "IN_WORK_DURATION" bigint, "IN_LINE_DURATION" bigint, "UTILIZATION" numeric, "OCCUPANCY" numeric, "CONTACTS_COUNT" bigint, "CLOSED_REQUESTS" bigint, "REDIRECTED_REQUESTS" bigint, "POSTPONED_REQUESTS" bigint, "DROPPED_REQUESTS" bigint, "ASA" numeric, "AHT" numeric, "ACW" numeric, "ACSI" numeric, "ASSI" numeric, "ART" numeric, "A1RT" numeric, "VOICE_ART" numeric, "SALES_PERCENT" character varying, "NOW" timestamp with time zone, "HT_SUM" numeric, "CW_SUM" numeric, "CSI_SUM" numeric, "SSI_SUM" numeric, "RT_SUM" numeric, "1RT_SUM" numeric, "VOICE_RT_SUM" numeric, "HT_COUNT" bigint, "CW_COUNT" bigint, "CSI_COUNT" bigint, "SSI_COUNT" bigint, "RT_COUNT" bigint, "1RT_COUNT" bigint, "VOICE_RT_COUNT" bigint, "CALLS_RECEIVED_COUNT" bigint, "CALLS_ACCEPTED_COUNT" bigint, "CALLS_MISSED_COUNT" bigint, "CONVERSATION_TIME" bigint, "WAIT_TIME_DURATION" bigint, "CALLS_OUTBOUND_COUNT" bigint, "CURRENT_STATUS_CODE" text, "ON_BREAK" bigint, "DIVISION_NAME" text, "DIVISION_ID" uuid, "SERVICE_SESSIONS_COUNT" bigint)
 LANGUAGE sql
 STABLE
AS $function$
    WITH v_operator_status_queue AS (
        SELECT
            vosq."OperatorId",
            vosq."QUEUES_LIST",
            vosq."STATUS",
            vosq."SECONDS_IN_STATUS",
            vosq."STATUS_CODE"
        FROM
            "CALC"."V_OPERATOR_STATUS_QUEUES" vosq
    ),

         work_session AS (
             SELECT
                 initial_statuses."OperatorId",
                 min(initial_statuses."StartDate") AS "START_TIME"
             FROM
                 "KPI"."OPERATOR_STATUSES" initial_statuses
             WHERE
                 initial_statuses."IsInitial" = true
               AND initial_statuses."StartDate" BETWEEN _startDate AND _endDate
             GROUP BY
                 initial_statuses."OperatorId"
         ),

         oper_status_statistic AS (
             SELECT s."OperatorId",
                    SUM(
                            CASE
                                WHEN s."Status" <> 'InWork'::TEXT
                                    AND s."Status" <> 'Ready'::TEXT
                                    AND s."Status" <> 'Offline'::TEXT
                                    AND s."Status" <> 'Initial'::TEXT THEN s."StatusDuration"
                                ELSE 0
                                END
                    ) AS "PAUSE_DURATION",
                    SUM(
                            CASE
                                WHEN (
                                    s."Status" = 'InWork'::TEXT
                                        OR s."Status" = 'Ready'::TEXT
                                    ) THEN s."StatusDuration"
                                ELSE 0
                                END
                    ) AS "ONLINE_DURATION",
                    SUM(
                            CASE
                                WHEN s."Status" = 'InWork'::TEXT THEN s."StatusDuration"
                                ELSE 0
                                END
                    ) AS "IN_WORK_DURATION",
                    SUM(
                            CASE
                                WHEN s."Status" = 'InWork'::TEXT
                                    OR s."Status" = 'Ready'::TEXT THEN s."StatusDuration"
                                ELSE 0
                                END
                    ) AS "IN_LINE_DURATION",
                    COALESCE(
                            SUM(
                                    CASE
                                        WHEN s."Status" = 'InWork'::TEXT
                                            OR s."Status" = 'Ready'::TEXT THEN s."StatusDuration"
                                        ELSE 0
                                        END
                            )::DOUBLE PRECISION / NULLIF(
                                    SUM(
                                            CASE
                                                WHEN s."Status" <> 'Offline'::TEXT
                                                    AND s."Status" <> 'Initial'::TEXT THEN s."StatusDuration"
                                                ELSE 0
                                                END
                                    )::DOUBLE PRECISION,
                                    0::DOUBLE PRECISION
                                                  ) * 100::DOUBLE PRECISION,
                            0::DOUBLE PRECISION
                    )::NUMERIC(10, 2) AS "UTILIZATION",
                    COALESCE(
                            SUM(
                                    CASE
                                        WHEN s."Status" = 'InWork'::TEXT THEN s."StatusDuration"
                                        ELSE 0
                                        END
                            )::DOUBLE PRECISION / NULLIF(
                                    SUM(
                                            CASE
                                                WHEN s."Status" = 'InWork'::TEXT
                                                    OR s."Status" = 'Ready'::TEXT THEN s."StatusDuration"
                                                ELSE 0
                                                END
                                    )::DOUBLE PRECISION,
                                    0::DOUBLE PRECISION
                                                  ) * 100::DOUBLE PRECISION,
                            0::DOUBLE PRECISION
                    )::NUMERIC(10, 2) AS "OCCUPANCY"
             FROM "KPI"."OPERATOR_STATUSES" s
             WHERE s."StartDate" BETWEEN _startDate AND _endDate
             GROUP BY s."OperatorId"
         ),

         requests_info AS (
             SELECT request_info_1."OperatorId", request_info_2."AHT", request_info_1."HT_SUM", request_info_1."HT_COUNT", request_info_1."CONTACTS_COUNT" FROM (SELECT o_kpi."OperatorId",
                                                                                                                                                                        SUM(
                                                                                                                                                                                CASE
                                                                                                                                                                                    WHEN (
                                                                                                                                                                                        o_kpi."ContactStartTime" BETWEEN _startDate AND _endDate
                                                                                                                                                                                        ) THEN o_kpi."HT"
                                                                                                                                                                                    ELSE NULL::BIGINT
                                                                                                                                                                                    END
                                                                                                                                                                       	) AS "HT_SUM",
                                                                                                                                                                        COUNT(
                                                                                                                                                                                CASE
                                                                                                                                                                                    WHEN (
                                                                                                                                                                                             o_kpi."ContactStartTime" BETWEEN _startDate AND _endDate
                                                                                                                                                                                             )
                                                                                                                                                                                        AND (
                                                                                                                                                                                             o_kpi."HT" IS NULL
                                                                                                                                                                                                 OR o_kpi."HT" = 0
                                                                                                                                                                                             ) THEN NULL
                                                                                                                                                                                    ELSE o_kpi."OperatorId"
                                                                                                                                                                                    END
                                                                                                                                                                        ) AS "HT_COUNT",
                                                                                                                                                                        COUNT(
                                                                                                                                                                                DISTINCT CASE
                                                                                                                                                                                             WHEN (
                                                                                                                                                                                                 o_kpi."ContactStartTime" BETWEEN _startDate AND _endDate
                                                                                                                                                                                                 ) THEN o_kpi."RequestId"
                                                                                                                                                                                             ELSE NULL::BIGINT
                                                                                                                                                                            END
                                                                                                                                                                        ) AS "CONTACTS_COUNT"
                                                                                                                                                                 FROM "KPI"."OPERATOR_KPI" o_kpi
                                                                                                                                                                 WHERE o_kpi."ContactEndTime" BETWEEN _startDate AND _endDate
                                                                                                                                                                 GROUP BY o_kpi."OperatorId") as request_info_1
                                                                                                                                                                    LEFT JOIN
                                                                                                                                                                (SELECT stat."OperatorId", AVG (stat."HT_SUM") as "AHT" FROM (SELECT o_kpi."OperatorId",
                                                                                                                                                                                                                                     SUM(
                                                                                                                                                                                                                                             CASE
                                                                                                                                                                                                                                                 WHEN (
                                                                                                                                                                                                                                                     o_kpi."ContactStartTime" BETWEEN _startDate AND _endDate
                                                                                                                                                                                                                                                     ) THEN o_kpi."HT"
                                                                                                                                                                                                                                                 ELSE NULL::BIGINT
                                                                                                                                                                                                                                                 END
                                                                                                                                                                                                                                     ) AS "HT_SUM"
                                                                                                                                                                                                                              FROM "KPI"."OPERATOR_KPI" o_kpi
                                                                                                                                                                                                                              WHERE o_kpi."ContactEndTime" BETWEEN _startDate AND _endDate
                                                                                                                                                                                                                              GROUP BY o_kpi."RequestId",o_kpi."OperatorId") as stat
                                                                                                                                                                 GROUP BY stat."OperatorId") as request_info_2 ON request_info_1."OperatorId" = request_info_2."OperatorId"
         ),

         csi_info_evaluation_pre AS ( -- Присваиваем ранг каждой записи на базе Date, чтобы выбрать последнюю оценку (rank = 1)
             SELECT
                 eval."OperatorId",
                 eval."Score",
                 RANK() OVER (PARTITION BY eval."RequestId", eval."OperatorId" ORDER BY eval."Date" DESC) AS "Rank"
             FROM
                 "EXTERNAL_EVALUATIONS"."ExternalEvaluations" AS eval
                     LEFT JOIN "KPI"."REQUEST_KPI" r_kpi ON r_kpi."Id" = eval."RequestId"
             WHERE
                 eval."OperatorId" IS NOT NULL
               AND r_kpi."TimeRegistered" BETWEEN _startDate AND _endDate
         ),

         csi_info_evaluation_result AS (
             SELECT
                 "OperatorId",
                 "Score"
             FROM csi_info_evaluation_pre
             WHERE "Rank" = 1
         ),

         requests_csi_info AS ( -- Подсчёт CSI
             SELECT
                 eval."OperatorId",
                 AVG(eval."Score") AS "ACSI",
                 SUM(eval."Score") AS "CSI_SUM",
                 COUNT(eval."Score") AS "CSI_COUNT"
             FROM csi_info_evaluation_result AS eval
             GROUP BY eval."OperatorId" -- Проверить корректность группировки из rkpi
         ),

         requests_ssi_info_from_rkpi AS (
             SELECT
                 r_kpi."ClosedById" AS "OperatorId",
                 r_kpi."SSI",
                 r_kpi."Id" AS "RequestId"
             FROM "KPI"."REQUEST_KPI" r_kpi
             WHERE
                 r_kpi."TimeRegistered" BETWEEN _startDate AND _endDate
               AND r_kpi."ClosedById" IS NOT NULL
         ),

         operator_ssi_info_from_eval AS (
             SELECT
                 e."RunForOperatorId" AS "OperatorId",
                 e."ScoreResult" AS "SSI",
                 (e."Context"->>'requestId')::bigint AS "RequestId"
             FROM "CALC"."V_EVALUATIONS" AS e
             WHERE e."PublishedAt" BETWEEN _startDate AND _endDate
         ),

         pre_ssi_info_result AS (
             SELECT
                 r."OperatorId",
                 COALESCE(eval."SSI", r."SSI") AS "SSI"
             FROM requests_ssi_info_from_rkpi AS r
                      LEFT JOIN operator_ssi_info_from_eval AS eval ON r."RequestId" = eval."RequestId"
         ),

         ssi_info_result AS (
             SELECT
                 "OperatorId",
                 AVG("SSI") AS "ASSI",
                 SUM("SSI") AS "SSI_SUM",
                 COUNT("SSI") AS "SSI_COUNT"
             FROM pre_ssi_info_result
             GROUP BY "OperatorId"
         ),

         requests_closed_info AS (
             SELECT r_kpi."ClosedById" AS "OperatorId",
                    COUNT(r_kpi."Id") AS "CLOSED_COUNT"
             FROM "KPI"."REQUEST_KPI" r_kpi
             WHERE r_kpi."TimeClosed" BETWEEN _startDate AND _endDate
             GROUP BY r_kpi."ClosedById"
         ),

         service_sessions_info AS (
             SELECT
                 r_kpi."ClosedById" AS "OperatorId",
                 COUNT(ss."Id") AS "SERVICE_SESSIONS_COUNT"
             FROM "KPI"."REQUEST_KPI" r_kpi
             INNER JOIN "SERVICE_SESSIONS"."ServiceSessions" ss ON ss."RequestId" = r_kpi."Id"
             WHERE r_kpi."TimeClosed" BETWEEN _startDate AND _endDate
             GROUP BY r_kpi."ClosedById"
         ),

         requests_call_info AS (
             SELECT call_state."OperatorId" AS "OperatorId",
                    COUNT(
                            CASE
                                WHEN (
                                    (call_state."Direction" IS NULL OR call_state."Direction" = 0)
                                        AND
                                    (
                                        call_state."TimeCallAccepted" IS NOT NULL
                                            OR call_state."TimeCallMissed" IS NOT NULL
                                        )
                                    ) THEN call_state."Id"
                                ELSE NULL
                                END
                    ) AS "CALLS_RECEIVED_COUNT",
                    COUNT(
                            CASE
                                WHEN (
                                    (call_state."Direction" IS NULL OR call_state."Direction" = 0)
                                        AND
                                    (
                                        call_state."TimeCallAccepted" IS NOT NULL
                                        )
                                    ) THEN call_state."Id"
                                ELSE NULL
                                END
                    ) AS "CALLS_ACCEPTED_COUNT",
                    COUNT(
                            CASE
                                WHEN (
                                    (call_state."Direction" IS NULL OR call_state."Direction" = 0)
                                        AND
                                    (
                                        call_state."TimeCallMissed" IS NOT NULL
                                        )
                                    ) THEN call_state."Id"
                                ELSE NULL
                                END
                    ) AS "CALLS_MISSED_COUNT",
                    COALESCE(SUM(call_state."Duration"), 0) AS "CONVERSATION_TIME",
                    AVG(call_state."ACW") AS "ACW",
                    SUM(call_state."ACW") AS "CW_SUM",
                    COUNT(call_state."ACW") AS "CW_COUNT",
                    COUNT(
                            CASE
                                WHEN call_state."Direction" = 1 THEN 1
                                ELSE NULL
                                END
                    ) AS "CALLS_OUTBOUND_COUNT"
             FROM "KPI"."OPERATOR_CALL_STATE" call_state
             WHERE (
                 call_state."TimeCallAccepted" BETWEEN _startDate AND _endDate
                 )
                OR (
                 call_state."TimeCallMissed" BETWEEN _startDate AND _endDate
                 )
             GROUP BY call_state."OperatorId"
         ),

         redirected_postponed_info AS (
             SELECT "REQUEST_EVENT"."OperatorId",
                    count(
                            CASE
                                WHEN "REQUEST_EVENT"."IsRedirected" = true THEN "REQUEST_EVENT"."RequestId"
                                ELSE NULL::BIGINT
                                END
                    ) AS "REDIRECTED_COUNT",
                    count(
                            DISTINCT CASE
                                         WHEN "REQUEST_EVENT"."IsPostponed" = true THEN "REQUEST_EVENT"."RequestId"
                                         ELSE NULL::BIGINT
                        END
                    ) AS "POSTPONED_COUNT",
                    count(
                            CASE
                                WHEN "REQUEST_EVENT"."IsDropped" = true THEN "REQUEST_EVENT"."RequestId"
                                ELSE NULL::BIGINT
                                END
                    ) AS "DROPPED_COUNT"
             FROM "KPI"."REQUEST_EVENT"
             WHERE "REQUEST_EVENT"."EventTime" BETWEEN _startDate AND _endDate
             GROUP BY "REQUEST_EVENT"."OperatorId"
         ),

         first_reaction_dates AS (
             SELECT "OPERATOR_RESPONSE"."RequestId",
                    min("OPERATOR_RESPONSE"."ReactionStartDate") AS "FirstReactionStartDate"
             FROM "KPI"."OPERATOR_RESPONSE"
             WHERE "OPERATOR_RESPONSE"."ReactionStartDate" BETWEEN _startDate AND _endDate
             GROUP BY "OPERATOR_RESPONSE"."RequestId"
         ),

         first_rt AS (
             SELECT all_rt."OperatorId",
                    AVG(all_rt."RT") AS "A1RT",
                    SUM(all_rt."RT") AS "1RT_SUM",
                    count(
                            CASE
                                WHEN all_rt."RT" IS NULL
                                    OR all_rt."RT" = 0 THEN NULL
                                ELSE all_rt."OperatorId"
                                END
                    ) AS "1RT_COUNT"
             FROM "KPI"."OPERATOR_RESPONSE" all_rt
                      RIGHT JOIN first_reaction_dates ON first_reaction_dates."RequestId" = all_rt."RequestId"
                 AND first_reaction_dates."FirstReactionStartDate" = all_rt."ReactionStartDate"
             GROUP BY all_rt."OperatorId"
         ),

         operator_response_all AS (
             SELECT all_rt."OperatorId",
                    AVG(all_rt."RT") AS "ART",
                    SUM(all_rt."RT") AS "RT_SUM",
                    count(
                            CASE
                                WHEN all_rt."RT" IS NULL
                                    OR all_rt."RT" = 0 THEN NULL
                                ELSE all_rt."OperatorId"
                                END
                    ) AS "RT_COUNT"
             FROM "KPI"."OPERATOR_RESPONSE" all_rt
             WHERE all_rt."ReactionStartDate" BETWEEN _startDate AND _endDate
             GROUP BY all_rt."OperatorId"
         ),

         operator_voice_response AS (
             SELECT all_rt."OperatorId",
                    AVG(all_rt."RT") AS "VOICE_ART",
                    SUM(all_rt."RT") AS "VOICE_RT_SUM",
                    count(
                            CASE
                                WHEN all_rt."RT" IS NULL
                                    OR all_rt."RT" = 0 THEN NULL
                                ELSE all_rt."OperatorId"
                                END
                    ) AS "VOICE_RT_COUNT"
             FROM "KPI"."OPERATOR_RESPONSE" all_rt
             WHERE all_rt."ChannelType" = 10
               AND all_rt."ReactionStartDate" BETWEEN _startDate AND _endDate
             GROUP BY all_rt."OperatorId"
         ),

         operator_wait_custom AS (
             SELECT
                 "OPERATOR_ID" AS "OperatorId",
                 extract(
                         epoch
                         from ((now() at time zone 'utc') - "DATETIME_VALUE")
                 )::BIGINT as wait_time_duration
             FROM
                 "AWP_INFRA"."OPERATOR_CUSTOM_ATTRIBUTES"
             WHERE
                 "CODE" = 'LastWorkTime'
         ),
         on_break as (with onBreak as (select "OperatorStatusId" as "StatusId"
                                       from "AWP_INFRA"."CustomAttributes"
                                       where "Name" like 'OnBreak'),
                           previos as (select "UID",
                                              "OPERATOR_ID",
                                              "ACTUAL_STATUS_ID",
                                              "ACTUAL_STATUS_SET_CODE",
                                              "PREVIOUS_STATUS_ID",
                                              "DATE_FROM",
                                              lag("DATE_FROM") over (PARTITION BY "OPERATOR_ID" ORDER BY "DATE_FROM")       as lag,
                                              lag("DATE_FROM") over (PARTITION BY "OPERATOR_ID" ORDER BY "DATE_FROM" desc ) as lagDesc
                                       from "AWP_INFRA_AUD"."ACTUAL_OPERATOR_STAT_CHANGES"
                                       where "DATE_FROM" BETWEEN now() - interval '1 day' AND now()),
                           res as (select "OPERATOR_ID",
                                          "ACTUAL_STATUS_ID",
                                          "PREVIOUS_STATUS_ID",
                                          "DATE_FROM",
                                          lag,
                                          lagDesc
                                   from previos
                                   where "PREVIOUS_STATUS_ID" IN (select "StatusId" from onBreak)
                                   -- and lag is not null
                                   union all
                                   select "OPERATOR_ID", "ACTUAL_STATUS_ID", "PREVIOUS_STATUS_ID", now(), "DATE_FROM", lagDesc
                                   from previos
                                   where "ACTUAL_STATUS_ID" IN (select "StatusId" from onBreak)
                                     and lagDesc is null)

                      select "OPERATOR_ID", sum(EXTRACT(EPOCH FROM "DATE_FROM") - EXTRACT(EPOCH FROM lag)) as diff
                      from res
                      group by "OPERATOR_ID"),

           operator_root_group as (
    		    SELECT og."Name",
				   og."Id",
				   ogo."OperatorId",
				   COUNT(*) OVER (PARTITION BY ogo."OperatorId") AS cnt
				FROM "AWP_INFRA"."OperatorGroups" og
				LEFT JOIN "AWP_INFRA"."OperatorGroupOperator" ogo ON ogo."OperatorGroupId" = og."Id"
				WHERE og."ParentId" IS NULL),
		   curator_custom AS (
		        SELECT "OPERATOR_CUSTOM_ATTRIBUTES"."OPERATOR_ID", "OPERATOR_CUSTOM_ATTRIBUTES"."GUID_VALUE" AS "CuratorId"
		        FROM "AWP_INFRA"."OPERATOR_CUSTOM_ATTRIBUTES"
		        WHERE "OPERATOR_CUSTOM_ATTRIBUTES"."CODE"::text = 'CuratorId'::text
		   )
    SELECT DISTINCT ON ("OperatorId")
        operators."ID" AS "OperatorId",
        operators."FIO",
        operators."LOGIN",
	    curator_logins."ID" AS "CURATOR_ID",
	    curator_logins."FIO" AS "CURATOR_FIO",
        v_operator_status_queue."QUEUES_LIST",
        v_operator_status_queue."STATUS" AS "CURRENT_STATUS",
        COALESCE(v_operator_status_queue."SECONDS_IN_STATUS", 0::NUMERIC(10, 2)::DOUBLE PRECISION)::NUMERIC(10, 2) AS "SECONDS_IN_CURRENT_STATUS",
        work_session."START_TIME" AS "START_TIME",
        COALESCE(oper_status_statistic."PAUSE_DURATION", 0::BIGINT) AS "PAUSE_DURATION",
        COALESCE(oper_status_statistic."ONLINE_DURATION", 0::BIGINT) AS "ONLINE_DURATION",
        to_char(
                make_interval(
                        secs => COALESCE(
                                oper_status_statistic."ONLINE_DURATION",
                                0::BIGINT
                                )
                ),
                'HH24:MI:SS'
        ) AS "ONLINE_DURATION_TEXT",
        COALESCE(oper_status_statistic."IN_WORK_DURATION", 0::BIGINT) AS "IN_WORK_DURATION",
        COALESCE(oper_status_statistic."IN_LINE_DURATION", 0::BIGINT) AS "IN_LINE_DURATION",
        COALESCE(oper_status_statistic."UTILIZATION", 0::NUMERIC) AS "UTILIZATION",
        COALESCE(oper_status_statistic."OCCUPANCY", 0::NUMERIC) AS "OCCUPANCY",
        COALESCE(requests_info."CONTACTS_COUNT", 0::BIGINT) AS "CONTACTS_COUNT",
        COALESCE(requests_closed_info."CLOSED_COUNT", 0::BIGINT) AS "CLOSED_REQUESTS",
        COALESCE(redirected_postponed_info."REDIRECTED_COUNT", 0::BIGINT) AS "REDIRECTED_REQUESTS",
        COALESCE(redirected_postponed_info."POSTPONED_COUNT", 0::BIGINT) AS "POSTPONED_REQUESTS",
        COALESCE(redirected_postponed_info."DROPPED_COUNT", 0::BIGINT) AS "DROPPED_REQUESTS",
        0::NUMERIC(10, 2) AS "ASA",
        COALESCE(requests_info."AHT", 0::NUMERIC)::NUMERIC(10, 2) AS "AHT",
        COALESCE(requests_call_info."ACW", 0::NUMERIC)::NUMERIC(10, 2) AS "ACW",
        COALESCE(requests_csi_info."ACSI", 0::NUMERIC)::NUMERIC(10, 2) AS "ACSI",
        COALESCE(ssi_info_result."ASSI", 0::NUMERIC)::NUMERIC(10, 2) AS "ASSI",
        COALESCE(operator_response_all."ART"::NUMERIC(15, 2), 0::NUMERIC) AS "ART",
        COALESCE(first_rt."A1RT"::NUMERIC(15, 2), 0::NUMERIC) AS "A1RT",
        COALESCE(operator_voice_response."VOICE_ART", 0::NUMERIC)::NUMERIC(10, 2) AS "VOICE_ART",
        '-' AS "SALES_PERCENT",
        timezone('UTC'::TEXT, now()::TIMESTAMP WITH TIME zone)::TIMESTAMP WITH TIME zone AS "NOW",
        COALESCE(requests_info."HT_SUM", 0::NUMERIC)::NUMERIC(10, 2) AS "HT_SUM",
        COALESCE(requests_call_info."CW_SUM", 0::NUMERIC)::NUMERIC(10, 2) AS "CW_SUM",
        COALESCE(requests_csi_info."CSI_SUM", 0::NUMERIC)::NUMERIC(10, 2) AS "CSI_SUM",
        COALESCE(ssi_info_result."SSI_SUM", 0::NUMERIC)::NUMERIC(10, 2) AS "SSI_SUM",
        COALESCE(operator_response_all."RT_SUM", 0::NUMERIC)::NUMERIC(10, 2) AS "RT_SUM",
        COALESCE(first_rt."1RT_SUM", 0::NUMERIC)::NUMERIC(10, 2) AS "1RT_SUM",
        COALESCE(operator_voice_response."VOICE_RT_SUM", 0::NUMERIC)::NUMERIC(10, 2) AS "VOICE_RT_SUM",
        COALESCE(requests_info."HT_COUNT"::BIGINT, 0::BIGINT) AS "HT_COUNT",
        COALESCE(requests_call_info."CW_COUNT"::BIGINT, 0::BIGINT) AS "CW_COUNT",
        COALESCE(requests_csi_info."CSI_COUNT"::BIGINT, 0::BIGINT) AS "CSI_COUNT",
        COALESCE(ssi_info_result."SSI_COUNT"::BIGINT, 0::BIGINT) AS "SSI_COUNT",
        COALESCE(operator_response_all."RT_COUNT"::BIGINT, 0::BIGINT) AS "RT_COUNT",
        COALESCE(first_rt."1RT_COUNT"::BIGINT, 0::BIGINT) AS "1RT_COUNT",
        COALESCE(operator_voice_response."VOICE_RT_COUNT"::BIGINT, 0::BIGINT) AS "VOICE_RT_COUNT",
        COALESCE(requests_call_info."CALLS_RECEIVED_COUNT"::BIGINT, 0::BIGINT) AS "CALLS_RECEIVED_COUNT", -- Поступило звонков (Входящих)
        COALESCE(requests_call_info."CALLS_ACCEPTED_COUNT"::BIGINT, 0::BIGINT) AS "CALLS_ACCEPTED_COUNT", -- Принято звонков (Входящих)
        COALESCE(requests_call_info."CALLS_MISSED_COUNT"::BIGINT, 0::BIGINT) AS "CALLS_MISSED_COUNT", -- Пропущено звонков (Входящих)
        COALESCE(requests_call_info."CONVERSATION_TIME"::BIGINT, 0::BIGINT) AS "CONVERSATION_TIME",
        COALESCE(operator_wait_custom.wait_time_duration, 0::BIGINT) AS "WAIT_TIME_DURATION",
        COALESCE(requests_call_info."CALLS_OUTBOUND_COUNT"::BIGINT, 0::BIGINT) AS "CALLS_OUTBOUND_COUNT",
        v_operator_status_queue."STATUS_CODE" AS "CURRENT_STATUS_CODE",
        COALESCE(on_break.diff::BIGINT, 0::BIGINT) AS "ON_BREAK",
        CASE
	        WHEN operator_root_group.cnt > 1 THEN NULL
	        ELSE operator_root_group."Name"
	    END AS "DIVISION_NAME",
        CASE
	        WHEN operator_root_group.cnt > 1 THEN NULL
	        ELSE operator_root_group."Id"
	    END AS "DIVISION_ID",
        COALESCE(service_sessions_info."SERVICE_SESSIONS_COUNT", 0::BIGINT) AS "SERVICE_SESSIONS_COUNT" -- [Контакты и клиенты] Количество сессий обслуживания
    FROM
        "CALC"."INFRA_Operators_FIO" operators
            LEFT JOIN v_operator_status_queue ON v_operator_status_queue."OperatorId" = operators."ID"
            LEFT JOIN oper_status_statistic ON oper_status_statistic."OperatorId" = operators."ID"
            LEFT JOIN work_session ON work_session."OperatorId" = operators."ID"
            LEFT JOIN requests_info ON requests_info."OperatorId" = operators."ID"
            LEFT JOIN requests_csi_info ON requests_csi_info."OperatorId" = operators."ID"
            LEFT JOIN ssi_info_result ON ssi_info_result."OperatorId" = operators."ID"
            LEFT JOIN requests_closed_info ON requests_closed_info."OperatorId" = operators."ID"
            LEFT JOIN service_sessions_info ON service_sessions_info."OperatorId" = operators."ID"
            LEFT JOIN requests_call_info ON requests_call_info."OperatorId" = operators."ID"
            LEFT JOIN redirected_postponed_info ON redirected_postponed_info."OperatorId" = operators."ID"
            LEFT JOIN operator_response_all ON operator_response_all."OperatorId" = operators."ID"
            LEFT JOIN first_rt ON first_rt."OperatorId" = operators."ID"
            LEFT JOIN operator_voice_response ON operator_voice_response."OperatorId" = operators."ID"
            LEFT JOIN operator_wait_custom ON operator_wait_custom."OperatorId" = operators."ID"
            LEFT JOIN on_break on on_break."OPERATOR_ID" = operators."ID"
            LEFT JOIN operator_root_group ON operator_root_group."OperatorId" = operators."ID"
       		LEFT JOIN curator_custom ON curator_custom."OPERATOR_ID" = operators."ID"
        	LEFT JOIN "CALC"."INFRA_Operators_FIO" curator_logins ON curator_logins."ID" = curator_custom."CuratorId"
    ORDER BY
        operators."ID";
    $function$
;

CREATE OR REPLACE VIEW "CALC"."V_OPERATOR_REQUEST_INFO_V2"
AS WITH rq AS (
         SELECT rq."Id",
            rq."Title"::text || '; '::text AS raw_title
           FROM "CALC"."CFG_RequestQueues" rq
          WHERE rq."TimeDeleted" IS NULL
        ), queues AS (
         SELECT erq."ExecutorId" AS "OPERATOR_ID",
            string_agg(rq.raw_title, ''::text ORDER BY erq."ExecutorId") AS raw_skill_list
           FROM rq
             JOIN "CALC"."CFG_ExecutorRequestQueues" erq ON rq."Id" = erq."QueueId"
          GROUP BY erq."ExecutorId"
        ), a AS (
         SELECT ogo."OperatorId",
            og."Name",
            og."Name" || '; '::text AS raw_name
           FROM "CALC"."INFRA_OperatorGroupOperator" ogo
             LEFT JOIN "CALC"."INFRA_OperatorGroups" og ON ogo."OperatorGroupId" = og."Id"
        ), group_list AS (
         SELECT a."OperatorId",
            string_agg(a.raw_name, ''::text ORDER BY a."OperatorId") AS raw_groups_list
           FROM a
          GROUP BY a."OperatorId"
        ), oca AS (
         SELECT ocas."OPERATOR_ID",
            ocas."DATETIME_VALUE" AS "AvailablePersManagerDateTime"
           FROM "AWP_INFRA"."OPERATOR_CUSTOM_ATTRIBUTES" ocas
          WHERE ocas."CODE"::text = 'AvailablePersManager'::text
        ),
		cca AS (
         SELECT "OPERATOR_CUSTOM_ATTRIBUTES"."OPERATOR_ID",
            "OPERATOR_CUSTOM_ATTRIBUTES"."GUID_VALUE" AS "CuratorId"
           FROM "AWP_INFRA"."OPERATOR_CUSTOM_ATTRIBUTES"
          WHERE "OPERATOR_CUSTOM_ATTRIBUTES"."CODE"::text = 'CuratorId'::text
        ), ri AS (
         SELECT count(*) AS "RequestsInWorkCount",
            string_agg(r_1."Id"::text, ';'::text ORDER BY r_1."Id") AS "RequestsInWorkIds",
            r_1."ExecutorId" AS "OPERATOR_ID"
           FROM "CRPM_DATA"."Requests" r_1
             JOIN "CRPM_CFG"."RequestStateStatuses" status ON r_1."Status" = status."Id"
          WHERE status."Code"::text = ANY (ARRAY['OPERATOR.WORK'::text, 'OPERATOR.PAUSE'::text, 'VOICE'::text, 'REQUEST.PROCESSING'::text, 'CLAIM.OPERATOR.WORK'::text])
          GROUP BY r_1."ExecutorId"
        ), r AS (
         SELECT r_1."Id" AS request_id,
            r_1."Channel",
            r_1."QueueId",
            que."Title" AS queue,
            r_1."TimeRegistered",
            r_1."Priority",
            r_1."BranchId",
            sa."Name" AS branch_name,
            r_1."ExecutorId" AS "OPERATOR_ID",
            max(w."TimeActivated") OVER (PARTITION BY w."ExecutorId") AS max_time,
            w."TimeActivated"
           FROM "CRPM_DATA"."Requests" r_1
             LEFT JOIN "CALC"."CFG_RequestQueues" que ON que."Id" = r_1."QueueId"
             LEFT JOIN "CALC"."INFRA_ServiceAreas" sa ON sa."Id" = r_1."BranchId"
             LEFT JOIN "CRPM_DATA"."Works" w ON w."RequestId" = r_1."Id"
          WHERE r_1."State" = 2 AND r_1."Status" = 1
        ), clienttype_cte AS (
         SELECT DISTINCT ON ("WorkSessions"."OperatorId") "WorkSessions"."OperatorId",
            "WorkSessions"."ClientType"
           FROM "AWP_INFRA"."WorkSessions"
          WHERE "WorkSessions"."EndDate" IS NULL AND "WorkSessions"."VALID_UNTIL" IS NOT NULL AND "WorkSessions"."ClientType" IS NOT NULL
          ORDER BY "WorkSessions"."OperatorId", "WorkSessions"."VALID_UNTIL" DESC
        ), operator_root_group AS (
         SELECT og."Name",
            og."Id",
            ogo."OperatorId",
            count(*) OVER (PARTITION BY ogo."OperatorId") AS cnt
           FROM "AWP_INFRA"."OperatorGroups" og
             LEFT JOIN "AWP_INFRA"."OperatorGroupOperator" ogo ON ogo."OperatorGroupId" = og."Id"
          WHERE og."ParentId" IS NULL
        ), call_control_agent_ready_cte AS (
         SELECT DISTINCT "SessionState"."OperatorId"
           FROM "CALL_CONTROL"."SessionState"
          WHERE "SessionState"."ActiveCall" IS NULL AND "SessionState"."CurrentAgentStatus" = 'ready'::"CALL_CONTROL".agent_status_type
        ), pending_status_cte AS (
         SELECT COALESCE(os."Name", '-'::text) AS "PendingStatusName",
            os."Code" AS "PendingStatusCode",
            aos."OperatorId"
           FROM "AWP_INFRA"."ActualOperatorStatuses" aos
             LEFT JOIN "AWP_INFRA"."OperatorStatuses" os ON os."Id" = aos."ActualPendingStatusId"
        ), res AS (
         SELECT ob."OPERATOR_ID" AS "OperatorId",
            operator_logins."FIO",
            operator_logins."LOGIN",
            curator_logins."FIO" AS "CURATOR_FIO",
            curator_logins."ID" AS "CURATOR_ID",
            ob."STATUS",
            ob."STATUS_CODE",
            ob."SECONDS_IN_STATUS",
            rtrim(queues.raw_skill_list, '; '::text) AS "QUEUES_LIST",
            rtrim(group_list.raw_groups_list, '; '::text) AS "GROUPS_LIST",
            ri."RequestsInWorkCount",
            ri."RequestsInWorkIds",
            r."Channel",
            r.request_id AS "REQUEST_ID",
            oca."AvailablePersManagerDateTime" IS NOT NULL AND oca."AvailablePersManagerDateTime" > now() AS "CAN_BE_PERSONAL_MANAGER",
            opi."CurrentLogin" AS "CurrentSoftphoneLogin",
            opi."CallRequestId" AS "ActiveCallRequestId",
            ws."ClientType",
            curr."DURATION_CURRENT_STATUS_TODAY",
                CASE
                    WHEN operator_root_group.cnt > 1 THEN NULL::text
                    ELSE operator_root_group."Name"
                END AS "OperatorRootGroupName",
                CASE
                    WHEN operator_root_group.cnt > 1 THEN NULL::uuid
                    ELSE operator_root_group."Id"
                END AS "OperatorRootGroupId",
            ccar."OperatorId" IS NOT NULL AS "ReadyForCall",
            pending_status_cte."PendingStatusName",
            pending_status_cte."PendingStatusCode"
           FROM "CALC"."V_OPERATOR_STATUSES" ob
             JOIN "CALC"."INFRA_Operators_FIO" operator_logins ON operator_logins."ID" = ob."OPERATOR_ID"
             LEFT JOIN queues ON ob."OPERATOR_ID" = queues."OPERATOR_ID"
             LEFT JOIN group_list ON ob."OPERATOR_ID" = group_list."OperatorId"
             LEFT JOIN ri ON ob."OPERATOR_ID" = ri."OPERATOR_ID"
             LEFT JOIN r ON ob."OPERATOR_ID" = r."OPERATOR_ID"
             LEFT JOIN oca ON ob."OPERATOR_ID" = oca."OPERATOR_ID"
             JOIN "CALC"."V_MM_OPERATORS_OF_MM_GROUP" mm ON mm."OperatorId" = ob."OPERATOR_ID"
             LEFT JOIN "SOFTPHONE"."OperatorInfo" opi ON opi."OperatorId" = ob."OPERATOR_ID"
             LEFT JOIN clienttype_cte ws ON ws."OperatorId" = ob."OPERATOR_ID"
             LEFT JOIN operator_root_group ON operator_root_group."OperatorId" = ob."OPERATOR_ID"
             LEFT JOIN LATERAL ( SELECT sum(COALESCE(os."StatusDuration"::double precision, date_part('epoch'::text, timezone('UTC'::text, CURRENT_TIMESTAMP) - os."StartDate")))::numeric(20,0) AS "DURATION_CURRENT_STATUS_TODAY"
                   FROM "KPI"."OPERATOR_STATUSES" os
                  WHERE os."OperatorId" = ob."OPERATOR_ID" AND os."Status" = ob."STATUS_CODE"::text AND (os."EndDate" >= timezone('UTC'::text, CURRENT_DATE::timestamp with time zone) OR os."EndDate" IS NULL)) curr ON true
             LEFT JOIN call_control_agent_ready_cte ccar ON ccar."OperatorId" = ob."OPERATOR_ID"
             LEFT JOIN pending_status_cte ON pending_status_cte."OperatorId" = ob."OPERATOR_ID"
             LEFT JOIN cca ON cca."OPERATOR_ID" = ob."OPERATOR_ID"
             LEFT JOIN "CALC"."INFRA_Operators_FIO" curator_logins ON curator_logins."ID" = cca."CuratorId"
        )
 SELECT DISTINCT ON ("OperatorId") "OperatorId",
    "FIO",
    "LOGIN",
    "STATUS",
    "STATUS_CODE",
    "SECONDS_IN_STATUS",
    "QUEUES_LIST",
    "GROUPS_LIST",
    "RequestsInWorkCount",
    "RequestsInWorkIds",
    "Channel",
    "REQUEST_ID",
    "CAN_BE_PERSONAL_MANAGER",
    "CurrentSoftphoneLogin",
    "ActiveCallRequestId",
    "ClientType",
    "DURATION_CURRENT_STATUS_TODAY",
    "OperatorRootGroupId",
    "OperatorRootGroupName",
    "ReadyForCall",
    "PendingStatusName",
    "PendingStatusCode",
    "CURATOR_FIO",
    "CURATOR_ID"
   FROM res;

CREATE OR REPLACE VIEW "CALC"."V_OPERATORS_INFO"
AS SELECT op."OperatorId",
    op."FIO",
    op."LOGIN"::character varying(300) AS "LOGIN",
    op."CURATOR_ID" AS "CURATOR_ID",
    op."CURATOR_FIO" AS "CURATOR_FIO",
    op."QUEUES_LIST",
    op."CURRENT_STATUS",
    op."SECONDS_IN_CURRENT_STATUS"::numeric(10,2) AS "SECONDS_IN_CURRENT_STATUS",
    op."START_TIME",
    op."PAUSE_DURATION",
    op."IN_WORK_DURATION",
    op."IN_LINE_DURATION",
    op."UTILIZATION",
    op."OCCUPANCY",
    op."CONTACTS_COUNT",
    op."CLOSED_REQUESTS",
    op."REDIRECTED_REQUESTS",
    op."POSTPONED_REQUESTS",
    op."ASA"::numeric(10,2) AS "ASA",
    op."AHT"::numeric(10,2) AS "AHT",
    op."ACW"::numeric(10,2) AS "ACW",
    op."ACSI"::numeric(10,2) AS "ACSI",
    op."ASSI"::numeric(10,2) AS "ASSI",
    op."ART"::numeric(10,2) AS "ART",
    op."A1RT"::numeric(10,2) AS "A1RT",
    op."VOICE_ART"::numeric(10,2) AS "VOICE_ART",
    op."CALLS_RECEIVED_COUNT",
    op."CALLS_ACCEPTED_COUNT",
    op."CALLS_MISSED_COUNT",
    op."CALLS_OUTBOUND_COUNT",
    op."CONVERSATION_TIME",
    op."WAIT_TIME_DURATION",
    op."DROPPED_REQUESTS",
    op."CURRENT_STATUS_CODE",
    curr."DURATION_CURRENT_STATUS_TODAY",
    op."CW_SUM"::numeric(10,2) AS "CWSUM",
    op."ON_BREAK",
    op."DIVISION_NAME",
    op."DIVISION_ID",
    op."SERVICE_SESSIONS_COUNT"
   FROM "CALC"."F_OPERATORS_KPI_INFO"() op("OperatorId", "FIO", "LOGIN", "CURATOR_ID", "CURATOR_FIO", "QUEUES_LIST", "CURRENT_STATUS", "SECONDS_IN_CURRENT_STATUS", "START_TIME", "PAUSE_DURATION", "ONLINE_DURATION", "ONLINE_DURATION_TEXT", "IN_WORK_DURATION", "IN_LINE_DURATION", "UTILIZATION", "OCCUPANCY", "CONTACTS_COUNT", "CLOSED_REQUESTS", "REDIRECTED_REQUESTS", "POSTPONED_REQUESTS", "DROPPED_REQUESTS", "ASA", "AHT", "ACW", "ACSI", "ASSI", "ART", "A1RT", "VOICE_ART", "SALES_PERCENT", "NOW", "HT_SUM", "CW_SUM", "CSI_SUM", "SSI_SUM", "RT_SUM", "1RT_SUM", "VOICE_RT_SUM", "HT_COUNT", "CW_COUNT", "CSI_COUNT", "SSI_COUNT", "RT_COUNT", "1RT_COUNT", "VOICE_RT_COUNT", "CALLS_RECEIVED_COUNT", "CALLS_ACCEPTED_COUNT", "CALLS_MISSED_COUNT", "CONVERSATION_TIME", "WAIT_TIME_DURATION", "CALLS_OUTBOUND_COUNT", "CURRENT_STATUS_CODE", "ON_BREAK", "DIVISION_NAME", "DIVISION_ID", "SERVICE_SESSIONS_COUNT")
     LEFT JOIN LATERAL ( SELECT sum(COALESCE(os."StatusDuration"::double precision, date_part('epoch'::text, timezone('UTC'::text, CURRENT_TIMESTAMP) - os."StartDate")))::numeric(20,0) AS "DURATION_CURRENT_STATUS_TODAY"
           FROM "KPI"."OPERATOR_STATUSES" os
          WHERE os."OperatorId" = op."OperatorId" AND os."Status" = op."CURRENT_STATUS_CODE" AND (os."EndDate" >= timezone('UTC'::text, CURRENT_DATE::timestamp with time zone) OR os."EndDate" IS NULL)) curr ON true
  WHERE op."CURRENT_STATUS" IS NOT NULL;