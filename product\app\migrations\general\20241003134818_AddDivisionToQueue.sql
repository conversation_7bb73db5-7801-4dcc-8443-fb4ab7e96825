DO $EF$
    BEGIN
        IF NOT EXISTS(SELECT 1 FROM "CALC"."__CalcContextMigrations" WHERE "MigrationId" = '20241003134818_AddDivisionToQueue') THEN
            CREATE OR REPLACE VIEW "CALC"."V_MM_QUEUES_INFO"
            AS WITH opened_requests AS (
                SELECT r."QueueId",
                       count(1) FILTER (WHERE r."Status" = (( SELECT st."Id"
                                                              FROM "CRPM_CFG"."RequestStateStatuses" st
                                                              WHERE st."Code"::text = 'OPERATOR.WAIT'::text))) AS awaiting_requests
                FROM "CRPM_DATA"."Requests" r
                WHERE COALESCE(r."TimeClosed", '0001-01-01 00:00:00'::timestamp without time zone) = '0001-01-01 00:00:00'::timestamp without time zone
                GROUP BY r."QueueId"
            ), online_opers AS (
                SELECT erq."QueueId",
                       count(DISTINCT erq."ExecutorId") AS online_operators
                FROM "CALC"."CFG_ExecutorRequestQueues" erq
                         JOIN "CALC"."INFRA_ActualOperatorStatuses" aos ON aos."OperatorId" = erq."ExecutorId"
                         JOIN "CALC"."INFRA_OperatorStatuses" ostatuses ON aos."ActualStatusId" = ostatuses."Id" AND (upper(ostatuses."Code"::text) = upper('Ready'::text) OR upper(ostatuses."Code"::text) = upper('ReadyToWork'::text) OR upper(ostatuses."Code"::text) = upper('InWork'::text))
                GROUP BY erq."QueueId"
            ), division_info AS (
                SELECT reqq."Id" as "QueueId", og."Id" AS "OperatorRootGroupId", og."Name" AS "OperatorRootGroupName"
                FROM "AWP_INFRA"."OperatorGroups" og
                         JOIN "CRPM_CFG"."CustomAttributes" ca ON og."Id" = ca."Value"::uuid
                         JOIN "CALC"."CFG_RequestQueues" reqq ON ca."QueueId" = reqq."Id"
                WHERE ca."Key" = 'Division'
            )
               SELECT rq."Id",
                      rq."Title",
                      opened_requests.awaiting_requests AS "OPENED_REQUESTS",
                      online_opers.online_operators AS "ONLINE_OPERATORS",
                      rq."IsService",
                      rq."TimeDeleted",
                      division_info."OperatorRootGroupId",
                      division_info."OperatorRootGroupName"
               FROM "CALC"."CFG_RequestQueues" rq
                        LEFT JOIN opened_requests ON opened_requests."QueueId" = rq."Id"
                        LEFT JOIN online_opers ON online_opers."QueueId" = rq."Id"
                        LEFT JOIN division_info ON division_info."QueueId" = rq."Id";
        END IF;
    END $EF$;

DO $EF$
    BEGIN
        IF NOT EXISTS(SELECT 1 FROM "CALC"."__CalcContextMigrations" WHERE "MigrationId" = '20241003134818_AddDivisionToQueue') THEN
            INSERT INTO "CALC"."__CalcContextMigrations" ("MigrationId", "ProductVersion")
            VALUES ('20241003134818_AddDivisionToQueue', '8.0.2');
        END IF;
    END $EF$;
