DO $PRIORITIZATION_REQ_ID$
BEGIN -- Правим RightPartType для RepeatedRequestId
    RAISE NOTICE '[PrioritizationRepeatedReqId] STARTED RightPartType update';

    UPDATE "AWP_INFRA"."Configurations" 
    SET 
        "Value" = jsonb_pretty(jsonb_set(
            "Value"::jsonb,
            ARRAY[
                'PrioritizationRuleAttributes', 
                (sub.idx - 1)::text, 
                'RightPartType'
            ],
            '"None"',
            false
        ))
    FROM (
        SELECT c.ctid, a.idx
        FROM "AWP_INFRA"."Configurations" c,
             jsonb_array_elements(c."Value"::jsonb->'PrioritizationRuleAttributes')
             WITH ORDINALITY AS a(elem, idx)
        WHERE c."Name" = 'AdministrationServiceConfiguration'
          AND a.elem->>'Code' = 'RepeatedRequestId'
          AND a.elem->>'RightPartType' = 'String'
    ) sub
    WHERE "AWP_INFRA"."Configurations".ctid = sub.ctid;

    RAISE NOTICE '[PrioritizationRepeatedReqId] COMPLETED update';
END $PRIORITIZATION_REQ_ID$;
