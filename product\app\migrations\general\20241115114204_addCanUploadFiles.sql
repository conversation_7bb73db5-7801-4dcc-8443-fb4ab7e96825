DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "AUTOMATION_SERVICES"."__EFMigrationsHistory" WHERE "MigrationId" = '20241115114204_addCanUploadFiles') THEN
    ALTER TABLE "AUTOMATION_SERVICES"."RestProperties" ADD "CanUploadFiles" boolean NOT NULL DEFAULT FALSE;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "AUTOMATION_SERVICES"."__EFMigrationsHistory" WHERE "MigrationId" = '20241115114204_addCanUploadFiles') THEN
    INSERT INTO "AUTOMATION_SERVICES"."__EFMigrationsHistory"  ("MigrationId", "ProductVersion")
    VALUES ('20241115114204_addCanUploadFiles', '8.0.2');
    END IF;
END $EF$;