DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "KPI"."__KpiServiceDbContextMigrations" WHERE "MigrationId" = '20250316135647_RefactorOperatorSlots') THEN
    ALTER TABLE "KPI"."OPERATOR_SLOT_TIME" DROP COLUMN "CurrentState";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "KPI"."__KpiServiceDbContextMigrations" WHERE "MigrationId" = '20250316135647_RefactorOperatorSlots') THEN
    ALTER TABLE "KPI"."OPERATOR_SLOT_TIME" DROP COLUMN "RowVersion";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "KPI"."__KpiServiceDbContextMigrations" WHERE "MigrationId" = '20250316135647_RefactorOperatorSlots') THEN
    ALTER TABLE "KPI"."OPERATOR_SLOT_TIME" RENAME COLUMN "CorrelationId" TO "WorkId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "KPI"."__KpiServiceDbContextMigrations" WHERE "MigrationId" = '20250316135647_RefactorOperatorSlots') THEN
    CREATE INDEX "IX_OPERATOR_SLOT_TIME_OperatorId_RequestId_WorkId" ON "KPI"."OPERATOR_SLOT_TIME" ("OperatorId", "RequestId", "WorkId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "KPI"."__KpiServiceDbContextMigrations" WHERE "MigrationId" = '20250316135647_RefactorOperatorSlots') THEN
    INSERT INTO "KPI"."__KpiServiceDbContextMigrations" ("MigrationId", "ProductVersion")
    VALUES ('20250316135647_RefactorOperatorSlots', '9.0.3');
    END IF;
END $EF$;

