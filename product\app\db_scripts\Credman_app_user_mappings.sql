/* Create users mappings */

INSERT INTO "CRED_MAN"."CREDENTIAL_MAPPINGS"(
	"APPLICATION_NAME", "IDENTITY_ID", "ENABLED", "USER_ID", "EXPIRATION_DATE", "ORDER")
	VALUES ('CALC_DB', 'd8009ea8-4408-43b3-b9ae-0a10e8a467f2', true, '{transform:DbUser}', null, 0);
/*
INSERT INTO "CRED_MAN"."CREDENTIAL_MAPPINGS"(
	"APPLICATION_NAME", "IDENTITY_ID", "ENABLED", "USER_ID", "EXPIRATION_DATE", "ORDER")
	VALUES ('Cascana.Api', 'd8009ea8-4408-43b3-b9ae-0a10e8a467f2', true, '{transform:serviceAccount}@{transform:FullDomainName}', null, 0);
INSERT INTO "CRED_MAN"."CREDENTIAL_MAPPINGS"(
	"APPLICATION_NAME", "IDENTITY_ID", "ENABLED", "USER_ID", "EXPIRATION_DATE", "ORDER")
	VALUES ('ChatBackend', 'd8009ea8-4408-43b3-b9ae-0a10e8a467f2', true, 'Setting', null, 0);
INSERT INTO "CRED_MAN"."CREDENTIAL_MAPPINGS"(
	"APPLICATION_NAME", "IDENTITY_ID", "ENABLED", "USER_ID", "EXPIRATION_DATE", "ORDER")
	VALUES ('ExternalApiServerSecret', 'd8009ea8-4408-43b3-b9ae-0a10e8a467f2', true, '{transform:serviceAccount}@{transform:FullDomainName}', null, 0);
INSERT INTO "CRED_MAN"."CREDENTIAL_MAPPINGS"(
	"APPLICATION_NAME", "IDENTITY_ID", "ENABLED", "USER_ID", "EXPIRATION_DATE", "ORDER")
	VALUES ('ExternalServices', 'd8009ea8-4408-43b3-b9ae-0a10e8a467f2', true, '{transform:serviceAccount}@{transform:FullDomainName}', null, 0);
INSERT INTO "CRED_MAN"."CREDENTIAL_MAPPINGS"(
	"APPLICATION_NAME", "IDENTITY_ID", "ENABLED", "USER_ID", "EXPIRATION_DATE", "ORDER")
	VALUES ('ExternalSystem.VK', 'd8009ea8-4408-43b3-b9ae-0a10e8a467f2', true, '{transform:serviceAccount}@{transform:FullDomainName}', null, 0);
INSERT INTO "CRED_MAN"."CREDENTIAL_MAPPINGS"(
	"APPLICATION_NAME", "IDENTITY_ID", "ENABLED", "USER_ID", "EXPIRATION_DATE", "ORDER")
	VALUES ('FeedbackService', 'd8009ea8-4408-43b3-b9ae-0a10e8a467f2', true, '{transform:serviceAccount}@{transform:FullDomainName}', null, 0);
INSERT INTO "CRED_MAN"."CREDENTIAL_MAPPINGS"(
	"APPLICATION_NAME", "IDENTITY_ID", "ENABLED", "USER_ID", "EXPIRATION_DATE", "ORDER")
	VALUES ('InternalOperatorChat.Api', 'd8009ea8-4408-43b3-b9ae-0a10e8a467f2', true, '{transform:serviceAccount}@{transform:FullDomainName}', null, 0);
INSERT INTO "CRED_MAN"."CREDENTIAL_MAPPINGS"(
	"APPLICATION_NAME", "IDENTITY_ID", "ENABLED", "USER_ID", "EXPIRATION_DATE", "ORDER")
	VALUES ('MFWhatsApp', 'd8009ea8-4408-43b3-b9ae-0a10e8a467f2', true, '{transform:serviceAccount}@{transform:FullDomainName}', null, 0);	
*/
INSERT INTO "CRED_MAN"."CREDENTIAL_MAPPINGS"(
	"APPLICATION_NAME", "IDENTITY_ID", "ENABLED", "USER_ID", "EXPIRATION_DATE", "ORDER")
	VALUES ('KPI_DB', 'd8009ea8-4408-43b3-b9ae-0a10e8a467f2', true, '{transform:DbUser}', null, 0);
INSERT INTO "CRED_MAN"."CREDENTIAL_MAPPINGS"(
	"APPLICATION_NAME", "IDENTITY_ID", "ENABLED", "USER_ID", "EXPIRATION_DATE", "ORDER")
	VALUES ('PSA_DB', 'd8009ea8-4408-43b3-b9ae-0a10e8a467f2', true, '{transform:DbUser}', null, 0);
INSERT INTO "CRED_MAN"."CREDENTIAL_MAPPINGS"(
	"APPLICATION_NAME", "IDENTITY_ID", "ENABLED", "USER_ID", "EXPIRATION_DATE", "ORDER")
	VALUES ('PSA_PG', 'd8009ea8-4408-43b3-b9ae-0a10e8a467f2', true, '{transform:DbUser}', null, 0);
INSERT INTO "CRED_MAN"."CREDENTIAL_MAPPINGS"(
	"APPLICATION_NAME", "IDENTITY_ID", "ENABLED", "USER_ID", "EXPIRATION_DATE", "ORDER")
	VALUES ('rabbit', 'd8009ea8-4408-43b3-b9ae-0a10e8a467f2', true, '{transform:rabbitLogin}', null, 0);
INSERT INTO "CRED_MAN"."CREDENTIAL_MAPPINGS"(
	"APPLICATION_NAME", "IDENTITY_ID", "ENABLED", "USER_ID", "EXPIRATION_DATE", "ORDER")
	VALUES ('rabbitDmz', 'd8009ea8-4408-43b3-b9ae-0a10e8a467f2', true, '{transform:rabbitDmzLogin}', null, 0);

/* for groups */
/* product-admins */
INSERT INTO "CRED_MAN"."CREDENTIAL_MAPPINGS"(
	"APPLICATION_NAME", "IDENTITY_ID", "ENABLED", "USER_ID", "EXPIRATION_DATE", "ORDER")
	VALUES ('rabbit', 'ec8b41c1-c261-4422-80b5-9c52a8d312d0', true, '{transform:rabbitLogin}', null, 0);
INSERT INTO "CRED_MAN"."CREDENTIAL_MAPPINGS"(
	"APPLICATION_NAME", "IDENTITY_ID", "ENABLED", "USER_ID", "EXPIRATION_DATE", "ORDER")
	VALUES ('rabbitDmz', 'ec8b41c1-c261-4422-80b5-9c52a8d312d0', true, '{transform:rabbitDmzLogin}', null, 0);
/* product-users */
INSERT INTO "CRED_MAN"."CREDENTIAL_MAPPINGS"(
	"APPLICATION_NAME", "IDENTITY_ID", "ENABLED", "USER_ID", "EXPIRATION_DATE", "ORDER")
	VALUES ('rabbit', 'acf981c2-6add-4160-9f97-610aad738cb9', true, '{transform:rabbitLogin}', null, 0);
INSERT INTO "CRED_MAN"."CREDENTIAL_MAPPINGS"(
	"APPLICATION_NAME", "IDENTITY_ID", "ENABLED", "USER_ID", "EXPIRATION_DATE", "ORDER")
	VALUES ('rabbitDmz', 'acf981c2-6add-4160-9f97-610aad738cb9', true, '{transform:rabbitDmzLogin}', null, 0);	
/* product-supervisor */
INSERT INTO "CRED_MAN"."CREDENTIAL_MAPPINGS"(
	"APPLICATION_NAME", "IDENTITY_ID", "ENABLED", "USER_ID", "EXPIRATION_DATE", "ORDER")
	VALUES ('rabbit', 'dc2535c6-bbed-4814-b2b4-c7d2a237c500', true, '{transform:rabbitLogin}', null, 0);
INSERT INTO "CRED_MAN"."CREDENTIAL_MAPPINGS"(
	"APPLICATION_NAME", "IDENTITY_ID", "ENABLED", "USER_ID", "EXPIRATION_DATE", "ORDER")
	VALUES ('rabbitDmz', 'dc2535c6-bbed-4814-b2b4-c7d2a237c500', true, '{transform:rabbitDmzLogin}', null, 0);		