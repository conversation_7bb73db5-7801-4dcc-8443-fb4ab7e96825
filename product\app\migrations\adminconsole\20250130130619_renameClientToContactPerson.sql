-- MarketingGridSettings
UPDATE "AWP_INFRA"."Configurations"
SET "Value" = REPLACE("Value", 'Тип клиента', 'Тип контактного лица')
WHERE "Name"='MarketingGridSettings';

-- RequestSupervisingRequestGridSettings
UPDATE "AWP_INFRA"."Configurations"
SET "Value" = REPLACE("Value", 'ID клиента', 'ID контактного лица')
WHERE "Name"='RequestSupervisingRequestGridSettings';

UPDATE "AWP_INFRA"."Configurations"
SET "Value" = REPLACE("Value", 'Тип клиента', 'Тип контактного лица')
WHERE "Name"='RequestSupervisingRequestGridSettings';

UPDATE "AWP_INFRA"."Configurations"
SET "Value" = REPLACE("Value", 'ФИО\Наименование клиента', 'ФИО\Наименование контактного лица')
WHERE "Name"='RequestSupervisingRequestGridSettings';

-- RequestProcessingRequestGridSettings
UPDATE "AWP_INFRA"."Configurations"
SET "Value" = REPLACE("Value", 'ID клиента', 'ID контактного лица')
WHERE "Name"='RequestProcessingRequestGridSettings';

UPDATE "AWP_INFRA"."Configurations"
SET "Value" = REPLACE("Value", 'Тип клиента', 'Тип контактного лица')
WHERE "Name"='RequestProcessingRequestGridSettings';

UPDATE "AWP_INFRA"."Configurations"
SET "Value" = REPLACE("Value", 'ФИО клиента', 'ФИО контактного лица')
WHERE "Name"='RequestProcessingRequestGridSettings';

-- InfoRequestGridSettings
UPDATE "AWP_INFRA"."Configurations"
SET "Value" = REPLACE("Value", 'ID клиента', 'ID контактного лица')
WHERE "Name"='InfoRequestGridSettings';

UPDATE "AWP_INFRA"."Configurations"
SET "Value" = REPLACE("Value", 'ФИО клиента', 'ФИО контактного лица')
WHERE "Name"='InfoRequestGridSettings';

UPDATE "AWP_INFRA"."Configurations"
SET "Value" = REPLACE("Value", 'Тип клиента', 'Тип контактного лица')
WHERE "Name"='InfoRequestGridSettings';
