#!/bin/bash

. /.config.sh || exit 1

shopt -s nocasematch

# Directory containing migration scripts
MIGRATIONS_DIR="/app/migrations"

# Export database password
export PGPASSWORD=${DBPASSWORD}

# Function to log messages
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# Function to find all SQL migration files recursively and sort them globally
find_migration_files() {
    find "${MIGRATIONS_DIR}" -type f -name "*.sql" -printf "%P\n" | sort | sed "s|^|${MIGRATIONS_DIR}/|"
}

# Ensure the migration history table exists
psql -h ${DBSERVER} -p ${DBSERVERPORT} -U ${DBUSER} -d ${DBNAMEPRODUCT} -c "CREATE TABLE IF NOT EXISTS public.__productMigrationHistory (migration_id TEXT PRIMARY KEY)" || exit 1

# Generate SQL for migrations
generate_migration_sql() {
    echo "BEGIN;"
    echo "DO \$\$"
    echo "DECLARE"
    echo "    current_migration_id TEXT;"
    echo "    sql_content TEXT;"
    echo "    error_occurred BOOLEAN := FALSE;"
    echo "    error_message TEXT;"
    echo "    error_file TEXT;"
    echo "BEGIN"
    
    for file in $(find_migration_files); do
        migration_id=$(basename "$file")
        sql_content=$(<"$file")
        
        echo "    current_migration_id := '${migration_id}';"
        echo "    IF NOT error_occurred AND NOT EXISTS (SELECT 1 FROM public.__productMigrationHistory WHERE migration_id = current_migration_id) THEN"
        echo "        BEGIN"
        echo "            RAISE NOTICE 'Executing migration: %', current_migration_id;"
        echo "            EXECUTE \$SQL\$${sql_content}\$SQL\$;"
        echo "            INSERT INTO public.__productMigrationHistory (migration_id) VALUES (current_migration_id);"
        echo "            RAISE NOTICE 'Migration completed: %', current_migration_id;"
        echo "        EXCEPTION WHEN OTHERS THEN"
        echo "            error_occurred := TRUE;"
        echo "            error_message := 'Error occurred in migration ' || current_migration_id || ': ' || SQLERRM;"
        echo "            error_file := current_migration_id;"
        echo "            RAISE NOTICE '%', error_message;"
        echo "        END;"
        echo "    ELSIF NOT error_occurred THEN"
        echo "        RAISE NOTICE 'Skipping already applied migration: %', current_migration_id;"
        echo "    END IF;"
    done
    
    echo "    IF error_occurred THEN"
    echo "        RAISE EXCEPTION 'Migration failed: % in file %', error_message, error_file;"
    echo "    END IF;"
    echo "END"
    echo "\$\$;"
    echo "COMMIT;"
}

# Execute migrations
log_message "Starting migrations"
migration_output=$(generate_migration_sql | psql -h ${DBSERVER} -p ${DBSERVERPORT} -U ${DBUSER} -d ${DBNAMEPRODUCT} 2>&1)
migration_status=$?

echo "$migration_output"

if [ $migration_status -ne 0 ] || echo "$migration_output" | grep -q "ERROR:"; then
    log_message "Migration failed"
    echo "Error details:"
    error_line=$(echo "$migration_output" | grep "ERROR:" | tail -n1)
    if [[ $error_line =~ "in file "([^:]+) ]]; then
        error_file="${BASH_REMATCH[1]}"
        echo "Error occurred in file: $error_file"
    else
        echo "Could not determine the file where the error occurred"
    fi
    echo "$error_line"
    exit 1
else
    log_message "All migrations applied successfully"
fi