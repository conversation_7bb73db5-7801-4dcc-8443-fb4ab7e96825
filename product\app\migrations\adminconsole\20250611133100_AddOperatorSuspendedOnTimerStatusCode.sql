INSERT INTO "AWP_INFRA"."Configurations" ("Id", "Name", "Value", "Description", "LastChangeTime")
SELECT uuid_generate_v4()::uuid,
'OperatorSuspendedOnTimerStatusCode',
'Suspended',
'Статус, в который оператор переводится при отстранении от работы по таймеру',
NOW()
WHERE
NOT EXISTS (
  SELECT "Id" FROM "AWP_INFRA"."Configurations"
  WHERE "Name"='OperatorSuspendedOnTimerStatusCode'
);


UPDATE "AWP_INFRA"."Configurations"
SET "Description" = 'Статус, в который оператор переводится при отстранении от работы'
WHERE "Name" = 'OperatorSuspendedStatusCode';
