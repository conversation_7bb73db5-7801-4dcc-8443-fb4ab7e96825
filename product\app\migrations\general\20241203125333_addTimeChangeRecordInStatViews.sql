DO $EF$
BEGIN
-- View: DATA_MARTS.V_DICT_OPERATORS

-- DROP VIEW "DATA_MARTS"."V_DICT_OPERATORS";

CREATE OR REPLACE VIEW "DATA_MARTS"."V_DICT_OPERATORS"
 AS
 SELECT "Id",
    "LastName",
    "FirstName",
    "MiddleName",
    "UserName",
	"LastChangeTime" AS "TimeRecordChange"
   FROM "AWP_INFRA"."Operators";

ALTER TABLE "DATA_MARTS"."V_DICT_OPERATORS"
    OWNER TO postgres;
COMMENT ON VIEW "DATA_MARTS"."V_DICT_OPERATORS"
    IS 'Справочник операторов';

COMMENT ON COLUMN "DATA_MARTS"."V_DICT_OPERATORS"."Id"
    IS 'Уникальный идентификатор оператора';

COMMENT ON COLUMN "DATA_MARTS"."V_DICT_OPERATORS"."LastName"
    IS 'Фамилия оператора';

COMMENT ON COLUMN "DATA_MARTS"."V_DICT_OPERATORS"."FirstName"
    IS 'Имя оператора';

COMMENT ON COLUMN "DATA_MARTS"."V_DICT_OPERATORS"."MiddleName"
    IS 'Отчество оператора';

COMMENT ON COLUMN "DATA_MARTS"."V_DICT_OPERATORS"."UserName"
    IS 'Учётная запись оператора';
	
COMMENT ON COLUMN "DATA_MARTS"."V_DICT_OPERATORS"."TimeRecordChange"
    IS 'Время последнего изменения поля';
END $EF$;

DO $EF$
BEGIN
-- View: DATA_MARTS.V_DICT_OPERATOR_STATUSES

-- DROP VIEW "DATA_MARTS"."V_DICT_OPERATOR_STATUSES";

CREATE OR REPLACE VIEW "DATA_MARTS"."V_DICT_OPERATOR_STATUSES"
 AS
 SELECT "Name",
    "Code",
    "IsStarting",
    "IsTerminating",
	"LastChangeTime" AS "TimeRecordChange"
   FROM "AWP_INFRA"."OperatorStatuses";

ALTER TABLE "DATA_MARTS"."V_DICT_OPERATOR_STATUSES"
    OWNER TO postgres;
COMMENT ON VIEW "DATA_MARTS"."V_DICT_OPERATOR_STATUSES"
    IS 'Справочник статусов';

COMMENT ON COLUMN "DATA_MARTS"."V_DICT_OPERATOR_STATUSES"."Name"
    IS 'Название статуса';

COMMENT ON COLUMN "DATA_MARTS"."V_DICT_OPERATOR_STATUSES"."Code"
    IS 'Код статуса';

COMMENT ON COLUMN "DATA_MARTS"."V_DICT_OPERATOR_STATUSES"."IsStarting"
    IS 'Признак начального статуса';

COMMENT ON COLUMN "DATA_MARTS"."V_DICT_OPERATOR_STATUSES"."IsTerminating"
    IS 'Признак финального статуса';
	
COMMENT ON COLUMN "DATA_MARTS"."V_DICT_OPERATOR_STATUSES"."TimeRecordChange"
    IS 'Время последнего изменения поля';
END $EF$;

DO $EF$
BEGIN
-- View: DATA_MARTS.V_OPERATOR_KPI

-- DROP VIEW "DATA_MARTS"."V_OPERATOR_KPI";

CREATE OR REPLACE VIEW "DATA_MARTS"."V_OPERATOR_KPI"
 AS
 SELECT "RequestId",
    "ContactStartTime",
    "OperatorId",
    "ChannelType",
    "ContactEndTime",
    "HT",
	"TimeRecordChange"
   FROM "KPI"."OPERATOR_KPI";

ALTER TABLE "DATA_MARTS"."V_OPERATOR_KPI"
    OWNER TO postgres;
COMMENT ON VIEW "DATA_MARTS"."V_OPERATOR_KPI"
    IS 'Статистика по контактам оператора';

COMMENT ON COLUMN "DATA_MARTS"."V_OPERATOR_KPI"."RequestId"
    IS 'Номер обращения';

COMMENT ON COLUMN "DATA_MARTS"."V_OPERATOR_KPI"."ContactStartTime"
    IS 'Время начала контакта';

COMMENT ON COLUMN "DATA_MARTS"."V_OPERATOR_KPI"."OperatorId"
    IS 'Идентификатор оператора';

COMMENT ON COLUMN "DATA_MARTS"."V_OPERATOR_KPI"."ChannelType"
    IS 'Идентификатор канала';

COMMENT ON COLUMN "DATA_MARTS"."V_OPERATOR_KPI"."ContactEndTime"
    IS 'Время завершения контакта';

COMMENT ON COLUMN "DATA_MARTS"."V_OPERATOR_KPI"."HT"
    IS 'Время обработки контакта ';
	
COMMENT ON COLUMN "DATA_MARTS"."V_OPERATOR_KPI"."TimeRecordChange"
    IS 'Время последнего изменения поля';
END $EF$;

DO $EF$
BEGIN
-- View: DATA_MARTS.V_OPERATOR_SLOT_TIME

-- DROP VIEW "DATA_MARTS"."V_OPERATOR_SLOT_TIME";

CREATE OR REPLACE VIEW "DATA_MARTS"."V_OPERATOR_SLOT_TIME"
 AS
 SELECT "RequestId",
    "ChannelType",
    "QueueId",
    "OperatorId",
    "SlotUseStartDate",
    "SlotUseEndDate",
	"TimeRecordChange"
   FROM "KPI"."OPERATOR_SLOT_TIME";

ALTER TABLE "DATA_MARTS"."V_OPERATOR_SLOT_TIME"
    OWNER TO postgres;
COMMENT ON VIEW "DATA_MARTS"."V_OPERATOR_SLOT_TIME"
    IS 'Статистика по слотам оператора';

COMMENT ON COLUMN "DATA_MARTS"."V_OPERATOR_SLOT_TIME"."RequestId"
    IS 'Номер обращения';

COMMENT ON COLUMN "DATA_MARTS"."V_OPERATOR_SLOT_TIME"."ChannelType"
    IS 'Идентификатор канала';

COMMENT ON COLUMN "DATA_MARTS"."V_OPERATOR_SLOT_TIME"."QueueId"
    IS 'Id очереди';

COMMENT ON COLUMN "DATA_MARTS"."V_OPERATOR_SLOT_TIME"."OperatorId"
    IS 'Идентификатор оператора';

COMMENT ON COLUMN "DATA_MARTS"."V_OPERATOR_SLOT_TIME"."SlotUseStartDate"
    IS 'Дата начала использования слота';

COMMENT ON COLUMN "DATA_MARTS"."V_OPERATOR_SLOT_TIME"."SlotUseEndDate"
    IS 'Дата окончания использования слота';
	
COMMENT ON COLUMN "DATA_MARTS"."V_OPERATOR_SLOT_TIME"."TimeRecordChange"
    IS 'Время последнего изменения поля';
END $EF$;

DO $EF$
BEGIN
-- View: DATA_MARTS.V_OPERATOR_STATUSES

-- DROP VIEW "DATA_MARTS"."V_OPERATOR_STATUSES";

CREATE OR REPLACE VIEW "DATA_MARTS"."V_OPERATOR_STATUSES"
 AS
 SELECT "OperatorId",
    "Status",
    "StartDate",
    "EndDate",
    "StatusDuration",
    "IsInitial",
	"TimeRecordChange"
   FROM "KPI"."OPERATOR_STATUSES";

ALTER TABLE "DATA_MARTS"."V_OPERATOR_STATUSES"
    OWNER TO postgres;
COMMENT ON VIEW "DATA_MARTS"."V_OPERATOR_STATUSES"
    IS 'Статистика по статусам оператора';

COMMENT ON COLUMN "DATA_MARTS"."V_OPERATOR_STATUSES"."OperatorId"
    IS 'Идентификатор оператора';

COMMENT ON COLUMN "DATA_MARTS"."V_OPERATOR_STATUSES"."Status"
    IS 'Код статуса';

COMMENT ON COLUMN "DATA_MARTS"."V_OPERATOR_STATUSES"."StartDate"
    IS 'Дата начала пребывания в статусе';

COMMENT ON COLUMN "DATA_MARTS"."V_OPERATOR_STATUSES"."EndDate"
    IS 'Дата окончания пребывания в статусе';

COMMENT ON COLUMN "DATA_MARTS"."V_OPERATOR_STATUSES"."StatusDuration"
    IS 'Время пребывания в статусе';

COMMENT ON COLUMN "DATA_MARTS"."V_OPERATOR_STATUSES"."IsInitial"
    IS 'Признак начального статуса';
	
COMMENT ON COLUMN "DATA_MARTS"."V_OPERATOR_STATUSES"."TimeRecordChange"
    IS 'Время последнего изменения поля';
END $EF$;