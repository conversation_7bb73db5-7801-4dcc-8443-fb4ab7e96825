DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "SCRIPTS"."__EFMigrationsHistory" WHERE "MigrationId" = '20241107164355_AddRatingStep') THEN
    INSERT INTO "SCRIPTS"."OwnerSystems" ("Name", "Description", "Code", "IsDeleted")
    SELECT 'RatingBot', 'Модуль оценочного бота', 'rating', false
    WHERE NOT EXISTS(SELECT "Id" FROM "SCRIPTS"."OwnerSystems" WHERE "Code"='rating');
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "SCRIPTS"."__EFMigrationsHistory" WHERE "MigrationId" = '20241107164355_AddRatingStep') THEN
    INSERT INTO "SCRIPTS"."__EFMigrationsHistory" ("MigrationId", "ProductVersion")
    VALUES ('20241107164355_AddRatingStep', '8.0.6');
    END IF;
END $EF$;