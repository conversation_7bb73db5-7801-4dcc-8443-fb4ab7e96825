-- Обновление статуса для закрытия чат-ботом
UPDATE "CRPM_CFG"."RequestStateStatuses"
SET "Code" = 'CHATBOT.CLOSE'
WHERE "Title" = 'Закрыто / Обработано Чат Ботом';

-- Добавление нового кода успешного завершения работы для оценки ботом, если он еще не существует для данной работы
INSERT INTO "CRPM_CFG"."WorkSuccessCodes" ("Code", "WorkId", "TargetStatusId")
SELECT 'CHATBOT.CLOSE',
       (SELECT "Id" FROM "CRPM_CFG"."RequestStateWorks" WHERE "RequestStateId" = (SELECT "Id" FROM "CRPM_CFG"."RequestStates" WHERE "Code" = 'RATINGBOT')),
       (SELECT "Id" FROM "CRPM_CFG"."RequestStateStatuses" WHERE "Code" = 'CHATBOT.CLOSE')
WHERE NOT EXISTS (
    SELECT 1 FROM "CRPM_CFG"."WorkSuccessCodes"
    WHERE "Code" = 'CHATBOT.CLOSE'
    AND "WorkId" = (SELECT "Id" FROM "CRPM_CFG"."RequestStateWorks" WHERE "RequestStateId" = (SELECT "Id" FROM "CRPM_CFG"."RequestStates" WHERE "Code" = 'RATINGBOT'))
); 