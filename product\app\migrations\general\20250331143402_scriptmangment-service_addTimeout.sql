DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "SCRIPTS"."__EFMigrationsHistory" WHERE "MigrationId" = '20250328110852_AddTimeOut') THEN
    ALTER TABLE "SCRIPTS"."ScriptSteps" ADD "Timeout" integer;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "SCRIPTS"."__EFMigrationsHistory" WHERE "MigrationId" = '20250328110852_AddTimeOut') THEN
    ALTER TABLE "SCRIPTS"."ScriptSteps" ADD "TimeoutStepId" bigint;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "SCRIPTS"."__EFMigrationsHistory" WHERE "MigrationId" = '20250328110852_AddTimeOut') THEN
    CREATE INDEX "IX_ScriptSteps_TimeoutStepId" ON "SCRIPTS"."ScriptSteps" ("TimeoutStepId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "SCRIPTS"."__EFMigrationsHistory" WHERE "MigrationId" = '20250328110852_AddTimeOut') THEN
    ALTER TABLE "SCRIPTS"."ScriptSteps" ADD CONSTRAINT "FK_ScriptSteps_ScriptSteps_TimeoutStepId" FOREIGN KEY ("TimeoutStepId") REFERENCES "SCRIPTS"."ScriptSteps" ("Id");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "SCRIPTS"."__EFMigrationsHistory" WHERE "MigrationId" = '20250328110852_AddTimeOut') THEN
    INSERT INTO "SCRIPTS"."__EFMigrationsHistory" ("MigrationId", "ProductVersion")
    VALUES ('20250328110852_AddTimeOut', '9.0.1');
    END IF;
END $EF$;