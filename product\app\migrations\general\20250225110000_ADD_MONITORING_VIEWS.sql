DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "CALC"."__CalcContextMigrations" WHERE "MigrationId" = '20250225110000_ADD_MONITORING_VIEWS') THEN

CREATE OR REPLACE VIEW "CALC"."V_MESSAGES_7DAYS"
	 AS
	 SELECT *
		FROM ( WITH all_aud AS (
                 SELECT "MESSAGE"."ID",
                    "MESSAGE"."DIRECTION",
                    "MESSAGE"."CHANNEL_ID",
                    "MESSAGE"."TIME_REGISTERED",
                    "MESSAGE"."TIME_SENT",
                    "MESSAGE"."TIME_RECEIVED"
                   FROM "UCMM_DATA"."MESSAGE"
                  WHERE "MESSAGE"."TIME_REGISTERED" >= (now()::date - '7 days'::interval)
                )
         SELECT 0 AS "Index",
            'Сегодня'::text AS "Временной интервал",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK AVG",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MAX"
           FROM all_aud
          WHERE now()::date < all_aud."TIME_REGISTERED" AND all_aud."TIME_REGISTERED" < now()
        UNION
         SELECT 1 AS "Index",
            'Вчера'::text AS "Временной интервал",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK AVG",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MAX"
           FROM all_aud
          WHERE (now()::date - '1 day'::interval) < all_aud."TIME_REGISTERED" AND all_aud."TIME_REGISTERED" < now()::date
        UNION
         SELECT 2 AS "Index",
            to_char(now()::date - '2 days'::interval, 'dd.mm.yyyy'::text) AS "Временной интервал",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK AVG",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MAX"
           FROM all_aud
          WHERE (now()::date - '2 days'::interval) < all_aud."TIME_REGISTERED" AND all_aud."TIME_REGISTERED" < (now()::date - '1 day'::interval)
        UNION
         SELECT 3 AS "Index",
            to_char(now()::date - '3 days'::interval, 'dd.mm.yyyy'::text) AS "Временной интервал",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK AVG",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MAX"
           FROM all_aud
          WHERE (now()::date - '3 days'::interval) < all_aud."TIME_REGISTERED" AND all_aud."TIME_REGISTERED" < (now()::date - '2 days'::interval)
        UNION
         SELECT 4 AS "Index",
            to_char(now()::date - '4 days'::interval, 'dd.mm.yyyy'::text) AS "Временной интервал",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK AVG",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MAX"
           FROM all_aud
          WHERE (now()::date - '4 days'::interval) < all_aud."TIME_REGISTERED" AND all_aud."TIME_REGISTERED" < (now()::date - '3 days'::interval)
        UNION
         SELECT 5 AS "Index",
            to_char(now()::date - '5 days'::interval, 'dd.mm.yyyy'::text) AS "Временной интервал",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK AVG",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MAX"
           FROM all_aud
          WHERE (now()::date - '5 days'::interval) < all_aud."TIME_REGISTERED" AND all_aud."TIME_REGISTERED" < (now()::date - '4 days'::interval)
        UNION
         SELECT 6 AS "Index",
            to_char(now()::date - '6 days'::interval, 'dd.mm.yyyy'::text) AS "Временной интервал",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK AVG",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MAX"
           FROM all_aud
          WHERE (now()::date - '6 days'::interval) < all_aud."TIME_REGISTERED" AND all_aud."TIME_REGISTERED" < (now()::date - '5 days'::interval)) unnamed_subquery
ORDER BY "Index";

CREATE OR REPLACE VIEW "CALC"."V_MESSAGES_DAY"
 AS
 SELECT "Index",
    "Временной интервал",
    "Входящие. Все каналы AVG",
    "Входящие. Чат AVG",
    "Входящие. Почта AVG",
    "Входящие. ТГ AVG",
    "Входящие. VK AVG",
    "Входящие. Все каналы MAX",
    "Входящие. Чат MAX",
    "Входящие. Почта MAX",
    "Входящие. ТГ MAX",
    "Входящие. VK MAX",
    "Входящие. Все каналы MIN",
    "Входящие. Чат MIN",
    "Входящие. Почта MIN",
    "Входящие. ТГ MIN",
    "Входящие. VK MIN",
    "Исходящие. Все каналы AVG",
    "Исходящие. Чат AVG",
    "Исходящие. Почта AVG",
    "Исходящие. ТГ AVG",
    "Исходящие. VK AVG",
    "Исходящие. Все каналы MAX",
    "Исходящие. Чат MAX",
    "Исходящие. Почта MAX",
    "Исходящие. ТГ MAX",
    "Исходящие. VK MAX",
    "Исходящие. Все каналы MIN",
    "Исходящие. Чат MIN",
    "Исходящие. Почта MIN",
    "Исходящие. ТГ MIN",
    "Исходящие. VK MIN"
   FROM ( WITH all_messages AS (
                 SELECT "MESSAGE"."ID",
                    "MESSAGE"."DIRECTION",
                    "MESSAGE"."CHANNEL_ID",
                    "MESSAGE"."TIME_REGISTERED" + '03:00:00'::interval AS "TIME_REGISTERED",
                    "MESSAGE"."TIME_SENT" + '03:00:00'::interval AS "TIME_SENT",
                    "MESSAGE"."TIME_RECEIVED" + '03:00:00'::interval AS "TIME_RECEIVED"
                   FROM "UCMM_DATA"."MESSAGE"
                  WHERE "MESSAGE"."TIME_REGISTERED" >= (now()::date + '03:00:00'::interval)
                )
         SELECT 0 AS "Index",
            '00:00:00 - 01:00:00'::text AS "Временной интервал",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK AVG",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MAX"
           FROM all_messages
          WHERE all_messages."TIME_REGISTERED" >= now()::date AND all_messages."TIME_REGISTERED" < (now()::date + '01:00:00'::interval)
        UNION
         SELECT 1 AS "Index",
            '01:00:00 - 02:00:00'::text AS "Временной интервал",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK AVG",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MAX"
           FROM all_messages
          WHERE all_messages."TIME_REGISTERED" >= (now()::date + '01:00:00'::interval) AND all_messages."TIME_REGISTERED" < (now()::date + '02:00:00'::interval)
        UNION
         SELECT 2 AS "Index",
            '02:00:00 - 03:00:00'::text AS "Временной интервал",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK AVG",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MAX"
           FROM all_messages
          WHERE all_messages."TIME_REGISTERED" >= (now()::date + '02:00:00'::interval) AND all_messages."TIME_REGISTERED" < (now()::date + '03:00:00'::interval)
        UNION
         SELECT 3 AS "Index",
            '03:00:00 - 04:00:00'::text AS "Временной интервал",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK AVG",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MAX"
           FROM all_messages
          WHERE all_messages."TIME_REGISTERED" >= (now()::date + '03:00:00'::interval) AND all_messages."TIME_REGISTERED" < (now()::date + '04:00:00'::interval)
        UNION
         SELECT 4 AS "Index",
            '04:00:00 - 05:00:00'::text AS "Временной интервал",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK AVG",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MAX"
           FROM all_messages
          WHERE all_messages."TIME_REGISTERED" >= (now()::date + '04:00:00'::interval) AND all_messages."TIME_REGISTERED" < (now()::date + '05:00:00'::interval)
        UNION
         SELECT 5 AS "Index",
            '05:00:00 - 06:00:00'::text AS "Временной интервал",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK AVG",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MAX"
           FROM all_messages
          WHERE all_messages."TIME_REGISTERED" >= (now()::date + '05:00:00'::interval) AND all_messages."TIME_REGISTERED" < (now()::date + '06:00:00'::interval)
        UNION
         SELECT 6 AS "Index",
            '06:00:00 - 07:00:00'::text AS "Временной интервал",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK AVG",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MAX"
           FROM all_messages
          WHERE all_messages."TIME_REGISTERED" >= (now()::date + '06:00:00'::interval) AND all_messages."TIME_REGISTERED" < (now()::date + '07:00:00'::interval)
        UNION
         SELECT 7 AS "Index",
            '07:00:00 - 08:00:00'::text AS "Временной интервал",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK AVG",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MAX"
           FROM all_messages
          WHERE all_messages."TIME_REGISTERED" >= (now()::date + '07:00:00'::interval) AND all_messages."TIME_REGISTERED" < (now()::date + '08:00:00'::interval)
        UNION
         SELECT 8 AS "Index",
            '08:00:00 - 09:00:00'::text AS "Временной интервал",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK AVG",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MAX"
           FROM all_messages
          WHERE all_messages."TIME_REGISTERED" >= (now()::date + '08:00:00'::interval) AND all_messages."TIME_REGISTERED" < (now()::date + '09:00:00'::interval)
        UNION
         SELECT 9 AS "Index",
            '09:00:00 - 10:00:00'::text AS "Временной интервал",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK AVG",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MAX"
           FROM all_messages
          WHERE all_messages."TIME_REGISTERED" >= (now()::date + '09:00:00'::interval) AND all_messages."TIME_REGISTERED" < (now()::date + '10:00:00'::interval)
        UNION
         SELECT 10 AS "Index",
            '10:00:00 - 11:00:00'::text AS "Временной интервал",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK AVG",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MAX"
           FROM all_messages
          WHERE all_messages."TIME_REGISTERED" >= (now()::date + '10:00:00'::interval) AND all_messages."TIME_REGISTERED" < (now()::date + '11:00:00'::interval)
        UNION
         SELECT 11 AS "Index",
            '11:00:00 - 12:00:00'::text AS "Временной интервал",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK AVG",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MAX"
           FROM all_messages
          WHERE all_messages."TIME_REGISTERED" >= (now()::date + '11:00:00'::interval) AND all_messages."TIME_REGISTERED" < (now()::date + '12:00:00'::interval)
        UNION
         SELECT 12 AS "Index",
            '12:00:00 - 13:00:00'::text AS "Временной интервал",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK AVG",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MAX"
           FROM all_messages
          WHERE all_messages."TIME_REGISTERED" >= (now()::date + '12:00:00'::interval) AND all_messages."TIME_REGISTERED" < (now()::date + '13:00:00'::interval)
        UNION
         SELECT 13 AS "Index",
            '13:00:00 - 14:00:00'::text AS "Временной интервал",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK AVG",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MAX"
           FROM all_messages
          WHERE all_messages."TIME_REGISTERED" >= (now()::date + '13:00:00'::interval) AND all_messages."TIME_REGISTERED" < (now()::date + '14:00:00'::interval)
        UNION
         SELECT 14 AS "Index",
            '14:00:00 - 15:00:00'::text AS "Временной интервал",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK AVG",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MAX"
           FROM all_messages
          WHERE all_messages."TIME_REGISTERED" >= (now()::date + '14:00:00'::interval) AND all_messages."TIME_REGISTERED" < (now()::date + '15:00:00'::interval)
        UNION
         SELECT 15 AS "Index",
            '15:00:00 - 16:00:00'::text AS "Временной интервал",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK AVG",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MAX"
           FROM all_messages
          WHERE all_messages."TIME_REGISTERED" >= (now()::date + '15:00:00'::interval) AND all_messages."TIME_REGISTERED" < (now()::date + '16:00:00'::interval)
        UNION
         SELECT 16 AS "Index",
            '16:00:00 - 17:00:00'::text AS "Временной интервал",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK AVG",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MAX"
           FROM all_messages
          WHERE all_messages."TIME_REGISTERED" >= (now()::date + '16:00:00'::interval) AND all_messages."TIME_REGISTERED" < (now()::date + '17:00:00'::interval)
        UNION
         SELECT 17 AS "Index",
            '17:00:00 - 18:00:00'::text AS "Временной интервал",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK AVG",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MAX"
           FROM all_messages
          WHERE all_messages."TIME_REGISTERED" >= (now()::date + '17:00:00'::interval) AND all_messages."TIME_REGISTERED" < (now()::date + '18:00:00'::interval)
        UNION
         SELECT 18 AS "Index",
            '18:00:00 - 19:00:00'::text AS "Временной интервал",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK AVG",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MAX"
           FROM all_messages
          WHERE all_messages."TIME_REGISTERED" >= (now()::date + '18:00:00'::interval) AND all_messages."TIME_REGISTERED" < (now()::date + '19:00:00'::interval)
        UNION
         SELECT 19 AS "Index",
            '19:00:00 - 20:00:00'::text AS "Временной интервал",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK AVG",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MAX"
           FROM all_messages
          WHERE all_messages."TIME_REGISTERED" >= (now()::date + '19:00:00'::interval) AND all_messages."TIME_REGISTERED" < (now()::date + '20:00:00'::interval)
        UNION
         SELECT 20 AS "Index",
            '20:00:00 - 21:00:00'::text AS "Временной интервал",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK AVG",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MAX"
           FROM all_messages
          WHERE all_messages."TIME_REGISTERED" >= (now()::date + '20:00:00'::interval) AND all_messages."TIME_REGISTERED" < (now()::date + '21:00:00'::interval)
        UNION
         SELECT 21 AS "Index",
            '21:00:00 - 22:00:00'::text AS "Временной интервал",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK AVG",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MAX"
           FROM all_messages
          WHERE all_messages."TIME_REGISTERED" >= (now()::date + '21:00:00'::interval) AND all_messages."TIME_REGISTERED" < (now()::date + '22:00:00'::interval)
        UNION
         SELECT 22 AS "Index",
            '22:00:00 - 23:00:00'::text AS "Временной интервал",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK AVG",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MAX"
           FROM all_messages
          WHERE all_messages."TIME_REGISTERED" >= (now()::date + '22:00:00'::interval) AND all_messages."TIME_REGISTERED" < (now()::date + '23:00:00'::interval)
        UNION
         SELECT 23 AS "Index",
            '23:00:00 - 24:00:00'::text AS "Временной интервал",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 1 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 21 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 91 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK AVG",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 1 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_REGISTERED" - all_messages."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MAX",
            avg(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK AVG",
            min(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MIN",
            max(
                CASE
                    WHEN all_messages."DIRECTION" = 2 AND all_messages."CHANNEL_ID" = 81 THEN all_messages."TIME_SENT" - all_messages."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MAX"
           FROM all_messages
          WHERE all_messages."TIME_REGISTERED" >= (now()::date + '23:00:00'::interval) AND all_messages."TIME_REGISTERED" < (now()::date + '24:00:00'::interval)) unnamed_subquery
  ORDER BY "Index";

CREATE OR REPLACE VIEW "CALC"."V_MESSAGES_HOUR"
 AS
 SELECT "Index",
    "Временной интервал",
    "Входящие. Все каналы AVG",
    "Входящие. Чат AVG",
    "Входящие. Почта AVG",
    "Входящие. ТГ AVG",
    "Входящие. VK AVG",
    "Входящие. Все каналы MAX",
    "Входящие. Чат MAX",
    "Входящие. Почта MAX",
    "Входящие. ТГ MAX",
    "Входящие. VK MAX",
    "Входящие. Все каналы MIN",
    "Входящие. Чат MIN",
    "Входящие. Почта MIN",
    "Входящие. ТГ MIN",
    "Входящие. VK MIN",
    "Исходящие. Все каналы AVG",
    "Исходящие. Чат AVG",
    "Исходящие. Почта AVG",
    "Исходящие. ТГ AVG",
    "Исходящие. VK AVG",
    "Исходящие. Все каналы MAX",
    "Исходящие. Чат MAX",
    "Исходящие. Почта MAX",
    "Исходящие. ТГ MAX",
    "Исходящие. VK MAX",
    "Исходящие. Все каналы MIN",
    "Исходящие. Чат MIN",
    "Исходящие. Почта MIN",
    "Исходящие. ТГ MIN",
    "Исходящие. VK MIN"
   FROM ( WITH all_aud AS (
                 SELECT "MESSAGE"."ID",
                    "MESSAGE"."DIRECTION",
                    "MESSAGE"."CHANNEL_ID",
                    "MESSAGE"."TIME_REGISTERED",
                    "MESSAGE"."TIME_SENT",
                    "MESSAGE"."TIME_RECEIVED"
                   FROM "UCMM_DATA"."MESSAGE"
                  WHERE "MESSAGE"."TIME_REGISTERED" >= (now() - '01:00:00'::interval)
                )
         SELECT 0 AS "Index",
            '0-10 минут'::text AS "Временной интервал",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK AVG",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MAX"
           FROM all_aud
          WHERE all_aud."TIME_REGISTERED" >= (now() - '00:10:00'::interval) AND all_aud."TIME_REGISTERED" < now()
        UNION
         SELECT 1 AS "Index",
            '10-20 минут'::text AS "Временной интервал",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK AVG",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MAX"
           FROM all_aud
          WHERE all_aud."TIME_REGISTERED" >= (now() - '00:20:00'::interval) AND all_aud."TIME_REGISTERED" < (now() - '00:10:00'::interval)
        UNION
         SELECT 2 AS "Index",
            '20-30 минут'::text AS "Временной интервал",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK AVG",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MAX"
           FROM all_aud
          WHERE all_aud."TIME_REGISTERED" >= (now() - '00:30:00'::interval) AND all_aud."TIME_REGISTERED" < (now() - '00:20:00'::interval)
        UNION
         SELECT 3 AS "Index",
            '30-40 минут'::text AS "Временной интервал",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK AVG",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MAX"
           FROM all_aud
          WHERE all_aud."TIME_REGISTERED" >= (now() - '00:40:00'::interval) AND all_aud."TIME_REGISTERED" < (now() - '00:30:00'::interval)
        UNION
         SELECT 4 AS "Index",
            '40-50 минут'::text AS "Временной интервал",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK AVG",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MAX"
           FROM all_aud
          WHERE all_aud."TIME_REGISTERED" >= (now() - '00:50:00'::interval) AND all_aud."TIME_REGISTERED" < (now() - '00:40:00'::interval)
        UNION
         SELECT 5 AS "Index",
            '50-60 минут'::text AS "Временной интервал",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Все каналы MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Почта MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 1 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Почта MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. Чат MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 21 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. Чат MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 91 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. ТГ MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK AVG",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 1 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_REGISTERED" - all_aud."TIME_RECEIVED"
                    ELSE NULL::interval
                END) AS "Входящие. VK MAX",
            avg(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK AVG",
            min(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MIN",
            max(
                CASE
                    WHEN all_aud."DIRECTION" = 2 AND all_aud."CHANNEL_ID" = 81 THEN all_aud."TIME_SENT" - all_aud."TIME_REGISTERED"
                    ELSE NULL::interval
                END) AS "Исходящие. VK MAX"
           FROM all_aud
          WHERE all_aud."TIME_REGISTERED" >= (now() - '01:00:00'::interval) AND all_aud."TIME_REGISTERED" < (now() - '00:50:00'::interval)) unnamed_subquery
  ORDER BY "Index";

CREATE OR REPLACE VIEW "CALC"."V_REQUESTS_STATUS_7DAYS"
 AS
 SELECT "Index",
    "Временной интервал",
    "Регистрация AVG",
    "Регистрация MIN",
    "Регистрация MAX",
    "Проверка на черный список AVG",
    "Проверка на черный список MIN",
    "Проверка на черный список MAX",
    "Проверка на спам AVG",
    "Проверка на спам MIN",
    "Проверка на спам MAX",
    "Отправка приветственного AVG",
    "Отправка приветственного MIN",
    "Отправка приветственного MAX",
    "Отправка информационного AVG",
    "Отправка информационного MIN",
    "Отправка информационного MAX"
   FROM ( WITH all_aud AS (
                 SELECT unnamed_subquery_1."REQUEST_ID",
                    unnamed_subquery_1."TIME_REGISTERED",
                    unnamed_subquery_1."STATUS",
                    unnamed_subquery_1."CHANGE_DATE",
                    unnamed_subquery_1."TIME_IN_STATUS"
                   FROM ( SELECT unnamed_subquery_2."REQUEST_ID",
                            unnamed_subquery_2."TIME_REGISTERED",
                            unnamed_subquery_2."STATUS",
                            unnamed_subquery_2."CHANGE_DATE",
                            lead(unnamed_subquery_2."CHANGE_DATE") OVER (PARTITION BY unnamed_subquery_2."REQUEST_ID" ORDER BY unnamed_subquery_2."CHANGE_DATE") - unnamed_subquery_2."CHANGE_DATE" AS "TIME_IN_STATUS"
                           FROM ( SELECT unnamed_subquery_3."REQUEST_ID",
                                    unnamed_subquery_3."TIME_REGISTERED",
                                    unnamed_subquery_3."STATUS",
                                    unnamed_subquery_3."CHANGE_DATE",
                                    row_number() OVER (PARTITION BY unnamed_subquery_3."REQUEST_ID", unnamed_subquery_3."STATUS" ORDER BY unnamed_subquery_3."CHANGE_DATE") AS "RowNumber"
                                   FROM ( SELECT rc."REQUEST_ID",
    rc."TIME_REGISTERED",
    status."Code" as "STATUS",
    COALESCE(ins_aud."CHANGE_DATE", upd_aud."CHANGE_DATE") - '03:00:00'::interval AS "CHANGE_DATE"
   FROM "CRPM_DATA_AUD"."REQUEST_CHANGES" rc
     LEFT JOIN "CRPM_DATA_AUD"."AUDITS" ins_aud ON rc."INSERT_AUDIT_ID" = ins_aud."ID"
     LEFT JOIN "CRPM_DATA_AUD"."AUDITS" upd_aud ON rc."UPDATE_AUDIT_ID" = upd_aud."ID"
     LEFT JOIN "CRPM_CFG"."RequestStateStatuses" status ON rc."STATUS" = status."Id"
  WHERE COALESCE(ins_aud."CHANGE_DATE", upd_aud."CHANGE_DATE") >= (now()::date - '7 days'::interval + '03:00:00'::interval)
  ORDER BY rc."TIME_REGISTERED" DESC) unnamed_subquery_3) unnamed_subquery_2
                          WHERE unnamed_subquery_2."RowNumber" = 1) unnamed_subquery_1
                  WHERE (unnamed_subquery_1."STATUS" = ANY (ARRAY['AUTOPROCESSING.BLACKLIST','AUTOPROCESSING.SPAM','AUTOPROCESSING.SEND_WELCOME_MESSAGE','AUTOPROCESSING.SEND_INFO_MESSAGE'])) OR unnamed_subquery_1."STATUS" IS NULL
                )
         SELECT 0 AS "Index",
            'Сегодня'::text AS "Временной интервал",
            avg(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MAX"
           FROM all_aud
          WHERE now()::date < all_aud."CHANGE_DATE" AND all_aud."CHANGE_DATE" < now()
        UNION
         SELECT 1 AS "Index",
            'Вчера'::text AS "Временной интервал",
            avg(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MAX"
           FROM all_aud
          WHERE (now()::date - '1 day'::interval) < all_aud."CHANGE_DATE" AND all_aud."CHANGE_DATE" < now()::date
        UNION
         SELECT 2 AS "Index",
            to_char(now()::date - '2 days'::interval, 'dd.mm.yyyy'::text) AS "Временной интервал",
            avg(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MAX"
           FROM all_aud
          WHERE (now()::date - '2 days'::interval) < all_aud."CHANGE_DATE" AND all_aud."CHANGE_DATE" < (now()::date - '1 day'::interval)
        UNION
         SELECT 3 AS "Index",
            to_char(now()::date - '3 days'::interval, 'dd.mm.yyyy'::text) AS "Временной интервал",
            avg(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MAX"
           FROM all_aud
          WHERE (now()::date - '3 days'::interval) < all_aud."CHANGE_DATE" AND all_aud."CHANGE_DATE" < (now()::date - '2 days'::interval)
        UNION
         SELECT 4 AS "Index",
            to_char(now()::date - '4 days'::interval, 'dd.mm.yyyy'::text) AS "Временной интервал",
            avg(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MAX"
           FROM all_aud
          WHERE (now()::date - '4 days'::interval) < all_aud."CHANGE_DATE" AND all_aud."CHANGE_DATE" < (now()::date - '3 days'::interval)
        UNION
         SELECT 5 AS "Index",
            to_char(now()::date - '5 days'::interval, 'dd.mm.yyyy'::text) AS "Временной интервал",
            avg(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MAX"
           FROM all_aud
          WHERE (now()::date - '5 days'::interval) < all_aud."CHANGE_DATE" AND all_aud."CHANGE_DATE" < (now()::date - '4 days'::interval)
        UNION
         SELECT 6 AS "Index",
            to_char(now()::date - '6 days'::interval, 'dd.mm.yyyy'::text) AS "Временной интервал",
            avg(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MAX"
           FROM all_aud
          WHERE (now()::date - '6 days'::interval) < all_aud."CHANGE_DATE" AND all_aud."CHANGE_DATE" < (now()::date - '5 days'::interval)) unnamed_subquery
 ORDER BY "Index";

CREATE OR REPLACE VIEW "CALC"."V_REQUESTS_STATUS_DAY"
 AS
 SELECT "Index",
    "Временной интервал",
    "Регистрация AVG",
    "Регистрация MIN",
    "Регистрация MAX",
    "Проверка на черный список AVG",
    "Проверка на черный список MIN",
    "Проверка на черный список MAX",
    "Проверка на спам AVG",
    "Проверка на спам MIN",
    "Проверка на спам MAX",
    "Отправка приветственного AVG",
    "Отправка приветственного MIN",
    "Отправка приветственного MAX",
    "Отправка информационного AVG",
    "Отправка информационного MIN",
    "Отправка информационного MAX"
   FROM ( WITH all_aud AS (
                 SELECT unnamed_subquery_1."REQUEST_ID",
                    unnamed_subquery_1."TIME_REGISTERED",
                    unnamed_subquery_1."STATUS",
                    unnamed_subquery_1."CHANGE_DATE",
                    unnamed_subquery_1."TIME_IN_STATUS"
                   FROM ( SELECT unnamed_subquery_2."REQUEST_ID",
                            unnamed_subquery_2."TIME_REGISTERED",
                            unnamed_subquery_2."STATUS",
                            unnamed_subquery_2."CHANGE_DATE",
                            lead(unnamed_subquery_2."CHANGE_DATE") OVER (PARTITION BY unnamed_subquery_2."REQUEST_ID" ORDER BY unnamed_subquery_2."CHANGE_DATE") - unnamed_subquery_2."CHANGE_DATE" AS "TIME_IN_STATUS"
                           FROM ( SELECT unnamed_subquery_3."REQUEST_ID",
                                    unnamed_subquery_3."TIME_REGISTERED",
                                    unnamed_subquery_3."STATUS",
                                    unnamed_subquery_3."CHANGE_DATE",
                                    row_number() OVER (PARTITION BY unnamed_subquery_3."REQUEST_ID", unnamed_subquery_3."STATUS" ORDER BY unnamed_subquery_3."CHANGE_DATE") AS "RowNumber"
                                   FROM ( SELECT rc."REQUEST_ID",
    rc."TIME_REGISTERED",
    status."Code" as "STATUS",
    COALESCE(ins_aud."CHANGE_DATE", upd_aud."CHANGE_DATE") + '03:00:00'::interval AS "CHANGE_DATE"
   FROM "CRPM_DATA_AUD"."REQUEST_CHANGES" rc
     LEFT JOIN "CRPM_DATA_AUD"."AUDITS" ins_aud ON rc."INSERT_AUDIT_ID" = ins_aud."ID"
     LEFT JOIN "CRPM_DATA_AUD"."AUDITS" upd_aud ON rc."UPDATE_AUDIT_ID" = upd_aud."ID"
     LEFT JOIN "CRPM_CFG"."RequestStateStatuses" status ON rc."STATUS" = status."Id"
  WHERE COALESCE(ins_aud."CHANGE_DATE", upd_aud."CHANGE_DATE") >= (now()::date + '03:00:00'::interval)
  ORDER BY rc."TIME_REGISTERED" DESC) unnamed_subquery_3) unnamed_subquery_2
                          WHERE unnamed_subquery_2."RowNumber" = 1) unnamed_subquery_1
                  WHERE (unnamed_subquery_1."STATUS" = ANY (ARRAY['AUTOPROCESSING.BLACKLIST','AUTOPROCESSING.SPAM','AUTOPROCESSING.SEND_WELCOME_MESSAGE','AUTOPROCESSING.SEND_INFO_MESSAGE'])) OR unnamed_subquery_1."STATUS" IS NULL
                )
         SELECT 0 AS "Index",
            '00:00:00 - 01:00:00'::text AS "Временной интервал",
            avg(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MAX"
           FROM all_aud
          WHERE all_aud."CHANGE_DATE" >= now()::date AND all_aud."CHANGE_DATE" < (now()::date + '01:00:00'::interval)
        UNION
         SELECT 1 AS "Index",
            '01:00:00 - 02:00:00'::text AS "Временной интервал",
            avg(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MAX"
           FROM all_aud
          WHERE all_aud."CHANGE_DATE" >= (now()::date + '01:00:00'::interval) AND all_aud."CHANGE_DATE" < (now()::date + '02:00:00'::interval)
        UNION
         SELECT 2 AS "Index",
            '02:00:00 - 03:00:00'::text AS "Временной интервал",
            avg(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MAX"
           FROM all_aud
          WHERE all_aud."CHANGE_DATE" >= (now()::date + '02:00:00'::interval) AND all_aud."CHANGE_DATE" < (now()::date + '03:00:00'::interval)
        UNION
         SELECT 3 AS "Index",
            '03:00:00 - 04:00:00'::text AS "Временной интервал",
            avg(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MAX"
           FROM all_aud
          WHERE all_aud."CHANGE_DATE" >= (now()::date + '03:00:00'::interval) AND all_aud."CHANGE_DATE" < (now()::date + '04:00:00'::interval)
        UNION
         SELECT 4 AS "Index",
            '04:00:00 - 05:00:00'::text AS "Временной интервал",
            avg(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MAX"
           FROM all_aud
          WHERE all_aud."CHANGE_DATE" >= (now()::date + '04:00:00'::interval) AND all_aud."CHANGE_DATE" < (now()::date + '05:00:00'::interval)
        UNION
         SELECT 5 AS "Index",
            '05:00:00 - 06:00:00'::text AS "Временной интервал",
            avg(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MAX"
           FROM all_aud
          WHERE all_aud."CHANGE_DATE" >= (now()::date + '05:00:00'::interval) AND all_aud."CHANGE_DATE" < (now()::date + '06:00:00'::interval)
        UNION
         SELECT 6 AS "Index",
            '06:00:00 - 07:00:00'::text AS "Временной интервал",
            avg(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MAX"
           FROM all_aud
          WHERE all_aud."CHANGE_DATE" >= (now()::date + '06:00:00'::interval) AND all_aud."CHANGE_DATE" < (now()::date + '07:00:00'::interval)
        UNION
         SELECT 7 AS "Index",
            '07:00:00 - 08:00:00'::text AS "Временной интервал",
            avg(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MAX"
           FROM all_aud
          WHERE all_aud."CHANGE_DATE" >= (now()::date + '07:00:00'::interval) AND all_aud."CHANGE_DATE" < (now()::date + '08:00:00'::interval)
        UNION
         SELECT 8 AS "Index",
            '08:00:00 - 09:00:00'::text AS "Временной интервал",
            avg(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MAX"
           FROM all_aud
          WHERE all_aud."CHANGE_DATE" >= (now()::date + '08:00:00'::interval) AND all_aud."CHANGE_DATE" < (now()::date + '09:00:00'::interval)
        UNION
         SELECT 9 AS "Index",
            '09:00:00 - 10:00:00'::text AS "Временной интервал",
            avg(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MAX"
           FROM all_aud
          WHERE all_aud."CHANGE_DATE" >= (now()::date + '09:00:00'::interval) AND all_aud."CHANGE_DATE" < (now()::date + '10:00:00'::interval)
        UNION
         SELECT 10 AS "Index",
            '10:00:00 - 11:00:00'::text AS "Временной интервал",
            avg(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MAX"
           FROM all_aud
          WHERE all_aud."CHANGE_DATE" >= (now()::date + '10:00:00'::interval) AND all_aud."CHANGE_DATE" < (now()::date + '11:00:00'::interval)
        UNION
         SELECT 11 AS "Index",
            '11:00:00 - 12:00:00'::text AS "Временной интервал",
            avg(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MAX"
           FROM all_aud
          WHERE all_aud."CHANGE_DATE" >= (now()::date + '11:00:00'::interval) AND all_aud."CHANGE_DATE" < (now()::date + '12:00:00'::interval)
        UNION
         SELECT 12 AS "Index",
            '12:00:00 - 13:00:00'::text AS "Временной интервал",
            avg(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MAX"
           FROM all_aud
          WHERE all_aud."CHANGE_DATE" >= (now()::date + '12:00:00'::interval) AND all_aud."CHANGE_DATE" < (now()::date + '13:00:00'::interval)
        UNION
         SELECT 13 AS "Index",
            '13:00:00 - 14:00:00'::text AS "Временной интервал",
            avg(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MAX"
           FROM all_aud
          WHERE all_aud."CHANGE_DATE" >= (now()::date + '13:00:00'::interval) AND all_aud."CHANGE_DATE" < (now()::date + '14:00:00'::interval)
        UNION
         SELECT 14 AS "Index",
            '14:00:00 - 15:00:00'::text AS "Временной интервал",
            avg(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MAX"
           FROM all_aud
          WHERE all_aud."CHANGE_DATE" >= (now()::date + '14:00:00'::interval) AND all_aud."CHANGE_DATE" < (now()::date + '15:00:00'::interval)
        UNION
         SELECT 15 AS "Index",
            '15:00:00 - 16:00:00'::text AS "Временной интервал",
            avg(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MAX"
           FROM all_aud
          WHERE all_aud."CHANGE_DATE" >= (now()::date + '15:00:00'::interval) AND all_aud."CHANGE_DATE" < (now()::date + '16:00:00'::interval)
        UNION
         SELECT 16 AS "Index",
            '16:00:00 - 17:00:00'::text AS "Временной интервал",
            avg(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MAX"
           FROM all_aud
          WHERE all_aud."CHANGE_DATE" >= (now()::date + '16:00:00'::interval) AND all_aud."CHANGE_DATE" < (now()::date + '17:00:00'::interval)
        UNION
         SELECT 17 AS "Index",
            '17:00:00 - 18:00:00'::text AS "Временной интервал",
            avg(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MAX"
           FROM all_aud
          WHERE all_aud."CHANGE_DATE" >= (now()::date + '17:00:00'::interval) AND all_aud."CHANGE_DATE" < (now()::date + '18:00:00'::interval)
        UNION
         SELECT 18 AS "Index",
            '18:00:00 - 19:00:00'::text AS "Временной интервал",
            avg(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MAX"
           FROM all_aud
          WHERE all_aud."CHANGE_DATE" >= (now()::date + '18:00:00'::interval) AND all_aud."CHANGE_DATE" < (now()::date + '19:00:00'::interval)
        UNION
         SELECT 19 AS "Index",
            '19:00:00 - 20:00:00'::text AS "Временной интервал",
            avg(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MAX"
           FROM all_aud
          WHERE all_aud."CHANGE_DATE" >= (now()::date + '19:00:00'::interval) AND all_aud."CHANGE_DATE" < (now()::date + '20:00:00'::interval)
        UNION
         SELECT 20 AS "Index",
            '20:00:00 - 21:00:00'::text AS "Временной интервал",
            avg(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MAX"
           FROM all_aud
          WHERE all_aud."CHANGE_DATE" >= (now()::date + '20:00:00'::interval) AND all_aud."CHANGE_DATE" < (now()::date + '21:00:00'::interval)
        UNION
         SELECT 21 AS "Index",
            '21:00:00 - 22:00:00'::text AS "Временной интервал",
            avg(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MAX"
           FROM all_aud
          WHERE all_aud."CHANGE_DATE" >= (now()::date + '21:00:00'::interval) AND all_aud."CHANGE_DATE" < (now()::date + '22:00:00'::interval)
        UNION
         SELECT 22 AS "Index",
            '22:00:00 - 23:00:00'::text AS "Временной интервал",
            avg(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MAX"
           FROM all_aud
          WHERE all_aud."CHANGE_DATE" >= (now()::date + '22:00:00'::interval) AND all_aud."CHANGE_DATE" < (now()::date + '23:00:00'::interval)
        UNION
         SELECT 23 AS "Index",
            '23:00:00 - 24:00:00'::text AS "Временной интервал",
            avg(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MAX"
           FROM all_aud
          WHERE all_aud."CHANGE_DATE" >= (now()::date + '23:00:00'::interval) AND all_aud."CHANGE_DATE" < (now()::date + '24:00:00'::interval)) unnamed_subquery
ORDER BY "Index";

CREATE OR REPLACE VIEW "CALC"."V_REQUESTS_STATUS_HOUR"
 AS
 SELECT "Index",
    "Временной интервал",
    "Регистрация AVG",
    "Регистрация MIN",
    "Регистрация MAX",
    "Проверка на черный список AVG",
    "Проверка на черный список MIN",
    "Проверка на черный список MAX",
    "Проверка на спам AVG",
    "Проверка на спам MIN",
    "Проверка на спам MAX",
    "Отправка приветственного AVG",
    "Отправка приветственного MIN",
    "Отправка приветственного MAX",
    "Отправка информационного AVG",
    "Отправка информационного MIN",
    "Отправка информационного MAX"
   FROM ( WITH all_aud AS (
                 SELECT unnamed_subquery_1."REQUEST_ID",
                    unnamed_subquery_1."TIME_REGISTERED",
                    unnamed_subquery_1."STATUS",
                    unnamed_subquery_1."CHANGE_DATE",
                    unnamed_subquery_1."TIME_IN_STATUS"
                   FROM ( SELECT unnamed_subquery_2."REQUEST_ID",
                            unnamed_subquery_2."TIME_REGISTERED",
                            unnamed_subquery_2."STATUS",
                            unnamed_subquery_2."CHANGE_DATE",
                            lead(unnamed_subquery_2."CHANGE_DATE") OVER (PARTITION BY unnamed_subquery_2."REQUEST_ID" ORDER BY unnamed_subquery_2."CHANGE_DATE") - unnamed_subquery_2."CHANGE_DATE" AS "TIME_IN_STATUS"
                           FROM ( SELECT unnamed_subquery_3."REQUEST_ID",
                                    unnamed_subquery_3."TIME_REGISTERED",
                                    unnamed_subquery_3."STATUS",
                                    unnamed_subquery_3."CHANGE_DATE",
                                    row_number() OVER (PARTITION BY unnamed_subquery_3."REQUEST_ID", unnamed_subquery_3."STATUS" ORDER BY unnamed_subquery_3."CHANGE_DATE") AS "RowNumber"
                                   FROM ( SELECT rc."REQUEST_ID",
    rc."TIME_REGISTERED",
    status."Code" as "STATUS",
    COALESCE(ins_aud."CHANGE_DATE", upd_aud."CHANGE_DATE") AS "CHANGE_DATE"
   FROM "CRPM_DATA_AUD"."REQUEST_CHANGES" rc
     LEFT JOIN "CRPM_DATA_AUD"."AUDITS" ins_aud ON rc."INSERT_AUDIT_ID" = ins_aud."ID"
     LEFT JOIN "CRPM_DATA_AUD"."AUDITS" upd_aud ON rc."UPDATE_AUDIT_ID" = upd_aud."ID"
     LEFT JOIN "CRPM_CFG"."RequestStateStatuses" status ON rc."STATUS" = status."Id"
  WHERE COALESCE(ins_aud."CHANGE_DATE", upd_aud."CHANGE_DATE") >= (now() - '01:00:00'::interval)
  ORDER BY rc."TIME_REGISTERED" DESC) unnamed_subquery_3) unnamed_subquery_2
                          WHERE unnamed_subquery_2."RowNumber" = 1) unnamed_subquery_1
                  WHERE (unnamed_subquery_1."STATUS" = ANY (ARRAY['AUTOPROCESSING.BLACKLIST','AUTOPROCESSING.SPAM','AUTOPROCESSING.SEND_WELCOME_MESSAGE','AUTOPROCESSING.SEND_INFO_MESSAGE'])) OR unnamed_subquery_1."STATUS" IS NULL
                )
         SELECT 0 AS "Index",
            '0-10 минут'::text AS "Временной интервал",
            avg(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MAX"
           FROM all_aud
          WHERE all_aud."CHANGE_DATE" >= (now() - '00:10:00'::interval) AND all_aud."CHANGE_DATE" < now()
        UNION
         SELECT 1 AS "Index",
            '10-20 минут'::text AS "Временной интервал",
            avg(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MAX"
           FROM all_aud
          WHERE all_aud."CHANGE_DATE" >= (now() - '00:20:00'::interval) AND all_aud."CHANGE_DATE" < (now() - '00:10:00'::interval)
        UNION
         SELECT 2 AS "Index",
            '20-30 минут'::text AS "Временной интервал",
            avg(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MAX"
           FROM all_aud
          WHERE all_aud."CHANGE_DATE" >= (now() - '00:30:00'::interval) AND all_aud."CHANGE_DATE" < (now() - '00:20:00'::interval)
        UNION
         SELECT 3 AS "Index",
            '30-40 минут'::text AS "Временной интервал",
            avg(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MAX"
           FROM all_aud
          WHERE all_aud."CHANGE_DATE" >= (now() - '00:40:00'::interval) AND all_aud."CHANGE_DATE" < (now() - '00:30:00'::interval)
        UNION
         SELECT 4 AS "Index",
            '40-50 минут'::text AS "Временной интервал",
            avg(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MAX"
           FROM all_aud
          WHERE all_aud."CHANGE_DATE" >= (now() - '00:50:00'::interval) AND all_aud."CHANGE_DATE" < (now() - '00:40:00'::interval)
        UNION
         SELECT 5 AS "Index",
            '50-60 минут'::text AS "Временной интервал",
            avg(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" IS NULL THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Регистрация MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.BLACKLIST' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на черный список MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SPAM' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Проверка на спам MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_WELCOME_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка приветственного MAX",
            avg(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного AVG",
            min(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MIN",
            max(
                CASE
                    WHEN all_aud."STATUS" = 'AUTOPROCESSING.SEND_INFO_MESSAGE' THEN all_aud."TIME_IN_STATUS"
                    ELSE NULL::interval
                END) AS "Отправка информационного MAX"
           FROM all_aud
          WHERE all_aud."CHANGE_DATE" >= (now() - '01:00:00'::interval) AND all_aud."CHANGE_DATE" < (now() - '00:50:00'::interval)) unnamed_subquery
 ORDER BY "Index";
 END IF;

END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "CALC"."__CalcContextMigrations" WHERE "MigrationId" = '20250225110000_ADD_MONITORING_VIEWS') THEN
    INSERT INTO "CALC"."__CalcContextMigrations" ("MigrationId", "ProductVersion")
    VALUES ('20250225110000_ADD_MONITORING_VIEWS', '9.0.1');
    END IF;
END $EF$;
