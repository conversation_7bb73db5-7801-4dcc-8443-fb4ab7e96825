DO $MIGRATION$

DECLARE noResponseWorkerId smallint; -- Id обработчика "Оценочный бот. Обработка отсутствия ответа"
DECLARE noResponseStateId smallint; -- Id этапа "Оценочный бот. Обработка отсутствия ответа"
DECLARE noResponseStatusId smallint; -- Id статуса "Обработка отсутствия ответа"
DECLARE noResponseStateWorkId smallint; -- Id работы на этапе "Оценочный бот. Обработка отсутствия ответа"
DECLARE ratingBotWaitStateMaxDurationsId smallint; -- Id таймаута для этапа "Оценочный бот ожидает ответа от клиента"

DECLARE closedStatusId smallint; -- Id статуса "Закрыто / Решено"
BEGIN

RAISE NOTICE '[AddClientNoResponseWorkerRatingBot] Start';

SELECT "Id" INTO closedStatusId FROM "CRPM_CFG"."RequestStateStatuses" WHERE "Title"='Закрыто / Решено';

-- Добавление обработчика
RAISE NOTICE '[AddClientNoResponseWorkerRatingBot.Worker] Start add worker';

INSERT INTO "CRPM_CFG"."WorkExecutors"
("RequestStreamId", "ChannelId", "TypeCode", "IsEnabled", "Title", "Configuration", "RowVersion")
SELECT 1, NULL, 'Product.ClientNoResponseWorker', (SELECT "IsEnabled" FROM "CRPM_CFG"."WorkExecutors" WHERE "TypeCode" = 'MM.ChatBotWorker' AND "Title" = 'Обработка оценочным ботом'), 'Оценочный бот. Обработка отсутствия ответа', '<?xml version="1.0" encoding="utf-16"?>
<ClientNoResponseWorkerConfiguration>
  <DefaultCompleteCode>DEFAULT</DefaultCompleteCode>
</ClientNoResponseWorkerConfiguration>', 638900271777973367
WHERE NOT EXISTS(SELECT "Id" FROM "CRPM_CFG"."WorkExecutors" WHERE "TypeCode" = 'Product.ClientNoResponseWorker' AND "Title" = 'Оценочный бот. Обработка отсутствия ответа')
RETURNING "Id" INTO noResponseWorkerId;

IF noResponseWorkerId IS NULL OR noResponseWorkerId = 0 THEN
    SELECT "Id" INTO noResponseWorkerId FROM "CRPM_CFG"."WorkExecutors" WHERE "TypeCode" = 'Product.ClientNoResponseWorker' AND "Title" = 'Оценочный бот. Обработка отсутствия ответа';
	RAISE NOTICE '[AddClientNoResponseWorkerRatingBot] noResponseWorkerId already exist. Id: %', noResponseWorkerId;
END IF;

RAISE NOTICE '[AddClientNoResponseWorkerRatingBot.Worker] End created worker with Id: %', noResponseWorkerId;
-------------------------------------------------------------------------------------------

-- Добавление обработчика в менеджеры автоматических работ
RAISE NOTICE '[AddClientNoResponseWorkerRatingBot.WorkExCfg] Start add worker to automatic workers';
INSERT INTO "CRPM_CFG"."WorkExecutionConfigurations" ("WorkExecutionManagerId", "WorkExecutorId", "Count", "RowVersion")
SELECT 1, noResponseWorkerId, 100, 638900273369300426
WHERE NOT EXISTS(SELECT "Id" FROM "CRPM_CFG"."WorkExecutionConfigurations" WHERE "WorkExecutorId" = noResponseWorkerId AND "WorkExecutionManagerId" = 1);
RAISE NOTICE '[AddClientNoResponseWorkerRatingBot.WorkExCfg] End added worker to automatic workers';
-------------------------------------------------------------------------------------------

-- Создание этапов
-- Добавление этапа 'Оценочный бот. Обработка отсутствия ответа'
RAISE NOTICE '[AddClientNoResponseWorkerRatingBot.State] Start add state';

INSERT INTO "CRPM_CFG"."RequestStates"
("Title", "SchemeId", "IsDefault", "IsTerminating", "MaxDurationId", "RowVersion", "Code", "DefaultTransitionOnSuccessId", "DefaultTransitionOnErrorId")
SELECT 'Оценочный бот. Обработка отсутствия ответа', 1, false, false, NULL, 638900276388220870, 'RATINGBOT.NOANSWER', closedStatusId, (SELECT "Id" FROM "CRPM_CFG"."RequestStateStatuses" WHERE "Code"='CLOSED.CRASH')
WHERE NOT EXISTS(SELECT "Id" FROM "CRPM_CFG"."RequestStates" WHERE "Code" = 'RATINGBOT.NOANSWER')
RETURNING "Id" INTO noResponseStateId;

IF noResponseStateId IS NULL OR noResponseStateId = 0 THEN
    SELECT "Id" INTO noResponseStateId FROM "CRPM_CFG"."RequestStates" WHERE "Code" = 'RATINGBOT.NOANSWER';
	RAISE NOTICE '[AddClientNoResponseWorkerRatingBot] noResponseStateId already exist. Id: %', noResponseStateId;
END IF;

RAISE NOTICE '[AddClientNoResponseWorkerRatingBot.State] End created state with Id: %', noResponseStateId;
-------------------------------------------------------------------------------------------

-- Добавление статуса 'Обработка отсутствия ответа' с кодом RATINGBOT.NOANSWER
RAISE NOTICE '[AddClientNoResponseWorkerRatingBot.Status] Start add no rate status';
INSERT INTO "CRPM_CFG"."RequestStateStatuses"
("Title", "StateId", "RowVersion", "Code")
SELECT 'Обработка отсутствия ответа', noResponseStateId, 638900268203983692, 'RATINGBOT.NOANSWER'
WHERE NOT EXISTS(SELECT "Id" FROM "CRPM_CFG"."RequestStateStatuses" WHERE "Code" = 'RATINGBOT.NOANSWER')
RETURNING "Id" INTO noResponseStatusId;

IF noResponseStatusId IS NULL OR noResponseStatusId = 0 THEN
    SELECT "Id" INTO noResponseStatusId FROM "CRPM_CFG"."RequestStateStatuses" WHERE "Code" = 'RATINGBOT.NOANSWER';
	RAISE NOTICE '[AddClientNoResponseWorkerRatingBot] noResponseStatusId already exist. Id: %', noResponseStatusId;
END IF;

RAISE NOTICE '[AddClientNoResponseWorkerRatingBot.Status] End added no rate status with Id: %', noResponseStatusId;
-------------------------------------------------------------------------------------------

-- Настройка переходов для этапа 'Оценочный бот. Обработка отсутствия ответа'
RAISE NOTICE '[AddClientNoResponseWorkerRatingBot.Status] Start configure transition states';
INSERT INTO "CRPM_CFG"."AvailableTargetStates"
("StateId", "TargetStatusId", "RowVersion", "StatusId")
SELECT noResponseStateId, closedStatusId, 638900274126061424, noResponseStatusId
WHERE NOT EXISTS (SELECT "Id" FROM "CRPM_CFG"."AvailableTargetStates" WHERE "StateId" = noResponseStateId AND "TargetStatusId" = closedStatusId AND "StatusId" = noResponseStatusId);
RAISE NOTICE '[AddClientNoResponseWorkerRatingBot.Status] End configure transition states';
-------------------------------------------------------------------------------------------

-- Добавление работы для этапа 'Оценочный бот. Обработка отсутствия ответа'
RAISE NOTICE '[AddClientNoResponseWorkerRatingBot.StateWork] Start add work for no response state';
INSERT INTO "CRPM_CFG"."RequestStateWorks"
("Title", "RequestStateId", "Order", "ProvideExecutorToRequest", "WorkExecutorId", "StatusOnProposedId", "StatusOnActiveId", "StatusOnPausedId", "StatusOnPostponedId", "StatusOnErrorRetryId", "ForceStatusChangeOnFailed", "ForceStatusChangeOnSucceeded", "StatusOnSucceededId", "StatusOnFailedId", "StatusOnRevokedId", "RowVersion", "GenerateEvents", "Enabled")
SELECT 'Обработка отсутствия ответа', noResponseStateId, 0, false, noResponseWorkerId, noResponseStatusId, noResponseStatusId, NULL, NULL, NULL, NULL, NULL, (SELECT "Id" FROM "CRPM_CFG"."RequestStateStatuses" WHERE "Title"='Закрыто / Решено'), (SELECT "Id" FROM "CRPM_CFG"."RequestStateStatuses" WHERE "Code"='CLOSED.CRASH'), NULL, 638900276488488322, false, true
WHERE NOT EXISTS (SELECT "Id" FROM "CRPM_CFG"."RequestStateWorks" WHERE "RequestStateId" = noResponseStateId AND "WorkExecutorId" = noResponseWorkerId)
RETURNING "Id" INTO noResponseStateWorkId;

IF noResponseStateWorkId IS NULL OR noResponseStateWorkId = 0 THEN
    SELECT "Id" INTO noResponseStateWorkId FROM "CRPM_CFG"."RequestStateWorks" WHERE "RequestStateId" = noResponseStateId AND "WorkExecutorId" = noResponseWorkerId;
	RAISE NOTICE '[AddClientNoResponseWorkerRatingBot] noResponseStateWorkId already exist. Id: %', noResponseStateWorkId;
END IF;

RAISE NOTICE '[AddClientNoResponseWorkerRatingBot.StateWork] End added work for no response state';
-------------------------------------------------------------------------------------------

-- Добавление кодов результата для работы
RAISE NOTICE '[AddClientNoResponseWorkerRatingBot.StateWork] Start add result codes';

INSERT INTO "CRPM_CFG"."WorkSuccessCodes"
("WorkId", "ForceStateChange", "Code", "TargetStatusId", "RowVersion")
SELECT noResponseStateWorkId, NULL, 'DEFAULT', closedStatusId, 638900275721988167
WHERE NOT EXISTS (SELECT "Id" FROM "CRPM_CFG"."WorkSuccessCodes" WHERE "WorkId" = noResponseStateWorkId AND "Code" = 'DEFAULT');

INSERT INTO "CRPM_CFG"."WorkSuccessCodes"
("WorkId", "ForceStateChange", "Code", "TargetStatusId", "RowVersion")
SELECT noResponseStateWorkId, NULL, 'AWPWORK.CLOSED_WITH_ANSWER', closedStatusId, 638900275359935455
WHERE NOT EXISTS (SELECT "Id" FROM "CRPM_CFG"."WorkSuccessCodes" WHERE "WorkId" = noResponseStateWorkId AND "Code" = 'AWPWORK.CLOSED_WITH_ANSWER');

RAISE NOTICE '[AddClientNoResponseWorkerRatingBot.StateWork] End added result codes';
-------------------------------------------------------------------------------------------

-- Конфигурация этапа "Оценочный бот ожидает ответа от клиента". Смена статуса при переходе по таймауту.
RAISE NOTICE '[AddClientNoResponseWorkerRatingBot.Config] Start configure RATINGBOT.WAIT';
UPDATE "CRPM_CFG"."MaxDurations"
SET "TargetStatusId" = noResponseStatusId
WHERE "Id" = (SELECT "MaxDurationId" FROM "CRPM_CFG"."RequestStates" WHERE "Code" = 'RATINGBOT.WAIT')
RETURNING "Id" INTO ratingBotWaitStateMaxDurationsId;
RAISE NOTICE '[AddClientNoResponseWorkerRatingBot.Config] End configure RATINGBOT.WAIT. MaxDurationId: %', ratingBotWaitStateMaxDurationsId;

RAISE NOTICE '[AddClientNoResponseWorkerRatingBot] End';

END $MIGRATION$;
