-- Удалить из настроек возможность настройки автообработчиков для:
-- Возврат из отложенных
UPDATE "AWP_INFRA"."Configurations"
SET "Value" = (
    SELECT jsonb_pretty(jsonb_set(
            "Value"::jsonb,
            '{AutoHandlers}',
            (
                SELECT jsonb_agg(elem)
                FROM jsonb_array_elements(("Value"::jsonb)->'AutoHandlers') elem
                WHERE elem->>'IsEnabledValueAttributeCode' <> 'Queue.PostponeReturned'
        )::jsonb
    ))::text
)
WHERE "Name" = 'SettingsServiceConfiguration';

-- Перевод на оператора
UPDATE "AWP_INFRA"."Configurations"
SET "Value" = (
    SELECT jsonb_pretty(jsonb_set(
            "Value"::jsonb,
            '{AutoHandlers}',
            (
                SELECT jsonb_agg(elem)
                FROM jsonb_array_elements(("Value"::jsonb)->'AutoHandlers') elem
                WHERE elem->>'IsEnabledValueAttributeCode' <> 'Queue.RedirectToOperator.ShouldSendAutoReply'
        )::jsonb
    ))::text
)
WHERE "Name" = 'SettingsServiceConfiguration';

-- Перевод в другую очередь
UPDATE "AWP_INFRA"."Configurations"
SET "Value" = (
    SELECT jsonb_pretty(jsonb_set(
            "Value"::jsonb,
            '{AutoHandlers}',
            (
                SELECT jsonb_agg(elem)
                FROM jsonb_array_elements(("Value"::jsonb)->'AutoHandlers') elem
                WHERE elem->>'IsEnabledValueAttributeCode' <> 'Queue.RedirectToQueue.ShouldSendAutoReply'
        )::jsonb
    ))::text
)
WHERE "Name" = 'SettingsServiceConfiguration';
