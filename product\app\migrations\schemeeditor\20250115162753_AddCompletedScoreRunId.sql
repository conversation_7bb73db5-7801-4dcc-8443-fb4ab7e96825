DO $MIG$

BEGIN

-- Добавление кастома AddCompletedScoreRunId в CustomAttributesMapForRequests
RAISE NOTICE '[AddCompletedScoreRunId.CA] Start add AddCompletedScoreRunId to CustomAttributesMapForRequests settings';
UPDATE "CRPM_CFG"."Settings"
SET "Configuration" = regexp_replace(
    "Configuration",
    '(<!--Binary-->)',
    '<Mapping Code="CompletedScoreRunId" Type="Long" Index="8" />
    \1',
    'i'
)
WHERE "Title" = 'CustomAttributesMapForRequests' AND "Configuration" NOT LIKE '%<Mapping Code="CompletedScoreRunId"%';
RAISE NOTICE '[AddCompletedScoreRunId.CA] End updating AddCompletedScoreRunId settings';

END $MIG$;
