#!/bin/bash

#includes
. /.config.sh || exit 1
. /.config-ticketing.sh || exit 1

shopt -s nocasematch
echo -e "[$(date '+%d.%m.%Y %T.%3N')] Restoring databases" && tput sgr0

#Export database password
export PGPASSWORD=${DBPASSWORD}

echo -e "[$(date '+%d.%m.%Y %T.%3N')] Applying databases changes" && tput sgr0

#Apply scripts
cd /app/product_crpm_init_scripts

echo -e "[$(date '+%d.%m.%Y %T.%3N')] Apply sql-script InitializeProductCrpm..." && tput sgr0
psql -h ${DBSERVER} -p ${DBSERVERPORT} -U ${DBUSER} -d "${DBNAMEPRODUCT}" -f InitializeProductCrpm.sql || exit 1
echo -e "[$(date '+%d.%m.%Y %T.%3N')] Apply sql-script InitializeProductCrpm complete" && tput sgr0

echo -e "[$(date '+%d.%m.%Y %T.%3N')] Product CRPM configuring completed" && tput sgr0
echo -e " Настройка модуля CRPM основного продукта успешно выполнена" && tput sgr0
exit 1