DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "SCRIPTS"."__EFMigrationsHistory" WHERE "MigrationId" = '20250410175858_AddChoiceStep') THEN
    CREATE TABLE IF NOT EXISTS "SCRIPTS"."ChoiceStepAnswers" (
        "Id" bigint GENERATED BY DEFAULT AS IDENTITY,
        "ScriptStepId" bigint NOT NULL,
        "NextStepId" bigint,
        "VariableId" bigint,
        "DisplayTextVariant" text NOT NULL,
        "KeyValueButton" text NOT NULL,
        CONSTRAINT "PK_ChoiceStepAnswers" PRIMARY KEY ("Id"),
        CONSTRAINT "FK_ChoiceStepAnswers_ScriptSteps_NextStepId" FOREIGN KEY ("NextStepId") REFERENCES "SCRIPTS"."ScriptSteps" ("Id"),
        CONSTRAINT "FK_ChoiceStepAnswers_ScriptSteps_ScriptStepId" FOREIGN KEY ("ScriptStepId") REFERENCES "SCRIPTS"."ScriptSteps" ("Id") ON DELETE CASCADE,
        CONSTRAINT "FK_ChoiceStepAnswers_ScriptVariables_VariableId" FOREIGN KEY ("VariableId") REFERENCES "SCRIPTS"."ScriptVariables" ("Id")
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "SCRIPTS"."__EFMigrationsHistory" WHERE "MigrationId" = '20250410175858_AddChoiceStep') THEN
    CREATE INDEX "IX_ChoiceStepAnswers_NextStepId" ON "SCRIPTS"."ChoiceStepAnswers" ("NextStepId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "SCRIPTS"."__EFMigrationsHistory" WHERE "MigrationId" = '20250410175858_AddChoiceStep') THEN
    CREATE UNIQUE INDEX "IX_ChoiceStepAnswers_ScriptStepId" ON "SCRIPTS"."ChoiceStepAnswers" ("ScriptStepId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "SCRIPTS"."__EFMigrationsHistory" WHERE "MigrationId" = '20250410175858_AddChoiceStep') THEN
    CREATE INDEX "IX_ChoiceStepAnswers_VariableId" ON "SCRIPTS"."ChoiceStepAnswers" ("VariableId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "SCRIPTS"."__EFMigrationsHistory" WHERE "MigrationId" = '20250410175858_AddChoiceStep') THEN
    INSERT INTO "SCRIPTS"."__EFMigrationsHistory" ("MigrationId", "ProductVersion")
    VALUES ('20250410175858_AddChoiceStep', '9.0.1');
    END IF;
END $EF$;

