#!/bin/bash

echo "updating certificates..."
update-ca-certificates
echo "certificates udpated"

chmod +x /app/*.sh

echo "transforming files..."
find /app/. \( -type f -name 'transforms*.sh' \) -exec bash -c {} \;
echo "files transformed"

# Check if USERSELECTOR is set and handle accordingly
if [ -n "${USERSELECTOR}" ]; then
  case "${USERSELECTOR}" in
    0)
      exit 0
      ;;
    1)
      /app/db_init.sh || exit 1
      ;;
    2)
      /app/cm_init.sh || exit 1
      ;;
    3)
      /app/migrations.sh || exit 1
      ;;
    4)
      /app/product_crpm_init.sh || exit 1
      ;;
    *)
      echo "Invalid USERSELECTOR value: ${USERSELECTOR}"
      exit 1
      ;;
  esac
else
  echo -e "Выберите необходимое действие:"
  echo -e "0 - Выход"
  echo -e "1 - Выполнить инициализацию базы данных"
  echo -e "2 - Выполнить инициализацию модуля Credential Manager"
  echo -e "3 - Выполнить миграции"
  echo -e "4 - Выполнить настройку модуля CRPM основного продукта"
  while true; do
    read -e -p "Ваш выбор: " USERSELECTOR
    case "${USERSELECTOR}" in
      0)
        exit 0
        ;;
      1)
        /app/db_init.sh || exit 1
        ;;
      2)
        /app/cm_init.sh || exit 1
        ;;
      3)
        /app/migrations.sh || exit 1
        ;;
      4)
        /app/product_crpm_init.sh || exit 1
        ;;
      *)
        echo "Invalid selection: ${USERSELECTOR}"
        ;;
    esac
  done
fi
