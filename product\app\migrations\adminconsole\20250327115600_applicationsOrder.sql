DO $MIGR$

    DECLARE supervisorsRoleId uuid;
        DECLARE operatorsRoleId uuid;

    BEGIN

        supervisorsRoleId := (select "Id" from "AWP_INFRA"."UserRoles" where "Name" = N'Супервизоры');
        operatorsRoleId := (select "Id" from "AWP_INFRA"."UserRoles" where "Name" = N'Операторы');

--	очищаем то, что будем перезабивать как должно быть
        DELETE FROM "AWP_INFRA"."ApplicationUserRole"
        WHERE "ApplicationId" IN (SELECT "Id" FROM "AWP_INFRA"."Applications" where "Name" IN (
                                                                                               N'АРМ Супервизора',
                                                                                               N'Настройки',
                                                                                               N'Скрипты диалогов',
                                                                                               N'Кнопочный бот',
                                                                                               N'Оценочные боты',
                                                                                               N'Оценочные формы',
                                                                                               N'АРМ Маркетинг',
                                                                                               N'Обработка обращения',
                                                                                               N'Ожидают обработки',
                                                                                               N'Видео-чат',
                                                                                               N'Отчеты',
                                                                                               N'Сервисы автоматизации',
                                                                                               N'Контактные лица',
                                                                                               N'Объекты обслуживания',
                                                                                               N'Инфо',
                                                                                               N'Внутренний чат',
                                                                                               N'Документация'
            ));

        UPDATE "AWP_INFRA"."Applications"
        SET "SortOrder" = 10
        WHERE "Name"=N'АРМ Супервизора';

        INSERT INTO "AWP_INFRA"."ApplicationUserRole" ("ApplicationId", "UserRoleId")
        SELECT "Id", supervisorsRoleId from "AWP_INFRA"."Applications" where "Name" = N'АРМ Супервизора';



        UPDATE "AWP_INFRA"."Applications"
        SET "SortOrder" = 20
        WHERE "Name"=N'Настройки';

        INSERT INTO "AWP_INFRA"."ApplicationUserRole" ("ApplicationId", "UserRoleId")
        SELECT "Id", supervisorsRoleId from "AWP_INFRA"."Applications" where "Name" = N'Настройки';



        UPDATE "AWP_INFRA"."Applications"
        SET "SortOrder" = 30
        WHERE "Name"=N'Скрипты диалогов';

        INSERT INTO "AWP_INFRA"."ApplicationUserRole" ("ApplicationId", "UserRoleId")
        SELECT "Id", supervisorsRoleId from "AWP_INFRA"."Applications" where "Name" = N'Скрипты диалогов';



        UPDATE "AWP_INFRA"."Applications"
        SET "SortOrder" = 40
        WHERE "Name"=N'Кнопочный бот';

        INSERT INTO "AWP_INFRA"."ApplicationUserRole" ("ApplicationId", "UserRoleId")
        SELECT "Id", supervisorsRoleId from "AWP_INFRA"."Applications" where "Name" = N'Кнопочный бот';



        UPDATE "AWP_INFRA"."Applications"
        SET "Name" = N'Оценочный бот'
        WHERE "Name"=N'Оценочные боты';

        UPDATE "AWP_INFRA"."Applications"
        SET "SortOrder" = 50
        WHERE "Name"=N'Оценочный бот';

        INSERT INTO "AWP_INFRA"."ApplicationUserRole" ("ApplicationId", "UserRoleId")
        SELECT "Id", supervisorsRoleId from "AWP_INFRA"."Applications" where "Name" = N'Оценочный бот';



        UPDATE "AWP_INFRA"."Applications"
        SET "SortOrder" = 60
        WHERE "Name"=N'Оценочные формы';

        INSERT INTO "AWP_INFRA"."ApplicationUserRole" ("ApplicationId", "UserRoleId")
        SELECT "Id", supervisorsRoleId from "AWP_INFRA"."Applications" where "Name" = N'Оценочные формы';



        UPDATE "AWP_INFRA"."Applications"
        SET "SortOrder" = 70
        WHERE "Name"=N'АРМ Маркетинг';

        INSERT INTO "AWP_INFRA"."ApplicationUserRole" ("ApplicationId", "UserRoleId")
        SELECT "Id", supervisorsRoleId from "AWP_INFRA"."Applications" where "Name" = N'АРМ Маркетинг';



        UPDATE "AWP_INFRA"."Applications"
        SET "SortOrder" = 80
        WHERE "Name"=N'Обработка обращения';

        INSERT INTO "AWP_INFRA"."ApplicationUserRole" ("ApplicationId", "UserRoleId")
        SELECT "Id", supervisorsRoleId from "AWP_INFRA"."Applications" where "Name" = N'Обработка обращения';

        INSERT INTO "AWP_INFRA"."ApplicationUserRole" ("ApplicationId", "UserRoleId")
        SELECT "Id", operatorsRoleId from "AWP_INFRA"."Applications" where "Name" = N'Обработка обращения';



        UPDATE "AWP_INFRA"."Applications"
        SET "SortOrder" = 90
        WHERE "Name"=N'Ожидают обработки';

        INSERT INTO "AWP_INFRA"."ApplicationUserRole" ("ApplicationId", "UserRoleId")
        SELECT "Id", supervisorsRoleId from "AWP_INFRA"."Applications" where "Name" = N'Ожидают обработки';

        INSERT INTO "AWP_INFRA"."ApplicationUserRole" ("ApplicationId", "UserRoleId")
        SELECT "Id", operatorsRoleId from "AWP_INFRA"."Applications" where "Name" = N'Ожидают обработки';



        UPDATE "AWP_INFRA"."Applications"
        SET "SortOrder" = 100
        WHERE "Name"=N'Видео-чат';

        INSERT INTO "AWP_INFRA"."ApplicationUserRole" ("ApplicationId", "UserRoleId")
        SELECT "Id", supervisorsRoleId from "AWP_INFRA"."Applications" where "Name" = N'Видео-чат';

        INSERT INTO "AWP_INFRA"."ApplicationUserRole" ("ApplicationId", "UserRoleId")
        SELECT "Id", operatorsRoleId from "AWP_INFRA"."Applications" where "Name" = N'Видео-чат';



        UPDATE "AWP_INFRA"."Applications"
        SET "SortOrder" = 110
        WHERE "Name"=N'Отчеты';

        INSERT INTO "AWP_INFRA"."ApplicationUserRole" ("ApplicationId", "UserRoleId")
        SELECT "Id", supervisorsRoleId from "AWP_INFRA"."Applications" where "Name" = N'Отчеты';



        UPDATE "AWP_INFRA"."Applications"
        SET "SortOrder" = 120
        WHERE "Name"=N'Сервисы автоматизации';

        INSERT INTO "AWP_INFRA"."ApplicationUserRole" ("ApplicationId", "UserRoleId")
        SELECT "Id", supervisorsRoleId from "AWP_INFRA"."Applications" where "Name" = N'Сервисы автоматизации';



        UPDATE "AWP_INFRA"."Applications"
        SET "SortOrder" = 130
        WHERE "Name"=N'Контактные лица';

        INSERT INTO "AWP_INFRA"."ApplicationUserRole" ("ApplicationId", "UserRoleId")
        SELECT "Id", supervisorsRoleId from "AWP_INFRA"."Applications" where "Name" = N'Контактные лица';

        INSERT INTO "AWP_INFRA"."ApplicationUserRole" ("ApplicationId", "UserRoleId")
        SELECT "Id", operatorsRoleId from "AWP_INFRA"."Applications" where "Name" = N'Контактные лица';



        UPDATE "AWP_INFRA"."Applications"
        SET "SortOrder" = 140
        WHERE "Name"=N'Объекты обслуживания';

        INSERT INTO "AWP_INFRA"."ApplicationUserRole" ("ApplicationId", "UserRoleId")
        SELECT "Id", supervisorsRoleId from "AWP_INFRA"."Applications" where "Name" = N'Объекты обслуживания';

        INSERT INTO "AWP_INFRA"."ApplicationUserRole" ("ApplicationId", "UserRoleId")
        SELECT "Id", operatorsRoleId from "AWP_INFRA"."Applications" where "Name" = N'Объекты обслуживания';



        UPDATE "AWP_INFRA"."Applications"
        SET "SortOrder" = 150
        WHERE "Name"=N'Инфо';

        INSERT INTO "AWP_INFRA"."ApplicationUserRole" ("ApplicationId", "UserRoleId")
        SELECT "Id", supervisorsRoleId from "AWP_INFRA"."Applications" where "Name" = N'Инфо';

        INSERT INTO "AWP_INFRA"."ApplicationUserRole" ("ApplicationId", "UserRoleId")
        SELECT "Id", operatorsRoleId from "AWP_INFRA"."Applications" where "Name" = N'Инфо';



        UPDATE "AWP_INFRA"."Applications"
        SET "SortOrder" = 160
        WHERE "Name"=N'Внутренний чат';

        INSERT INTO "AWP_INFRA"."ApplicationUserRole" ("ApplicationId", "UserRoleId")
        SELECT "Id", supervisorsRoleId from "AWP_INFRA"."Applications" where "Name" = N'Внутренний чат';

        INSERT INTO "AWP_INFRA"."ApplicationUserRole" ("ApplicationId", "UserRoleId")
        SELECT "Id", operatorsRoleId from "AWP_INFRA"."Applications" where "Name" = N'Внутренний чат';



        UPDATE "AWP_INFRA"."Applications"
        SET "SortOrder" = 170
        WHERE "Name"=N'Документация';

        INSERT INTO "AWP_INFRA"."ApplicationUserRole" ("ApplicationId", "UserRoleId")
        SELECT "Id", supervisorsRoleId from "AWP_INFRA"."Applications" where "Name" = N'Документация';

        INSERT INTO "AWP_INFRA"."ApplicationUserRole" ("ApplicationId", "UserRoleId")
        SELECT "Id", operatorsRoleId from "AWP_INFRA"."Applications" where "Name" = N'Документация';


    END $MIGR$;