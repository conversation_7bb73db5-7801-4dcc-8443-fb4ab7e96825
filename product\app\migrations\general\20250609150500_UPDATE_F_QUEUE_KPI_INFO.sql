-- DROP FUNCTION "CALC"."F_QUEUES_KPI_INFO"(timestamp, timestamp);

CREATE OR REPLACE FUNCTION "CALC"."F_QUEUES_KPI_INFO"(_startdate timestamp without time zone, _enddate timestamp without time zone)
 RETURNS TABLE("ID" smallint, "TITLE" character varying, "QUEUE_TIME_DELETED" timestamp without time zone, "TOTAL_REQUESTS" bigint, "CLOSED_REQUESTS" bigint, "CLOSED_REQUESTS_BY_OPERATOR" bigint, "CLOSED_REQUESTS_BY_SUPERVISOR" bigint, "CLOSED_REQUESTS_BY_SYSTEM" bigint, "CLOSED_REQUESTS_BY_BOT" bigint, "CLOSED_REQUESTS_BY_BOT_PERCENT" numeric, "OPENED_REQUESTS" bigint, "REQUESTS_IN_PROCESSING" bigint, "ASSIGNED_TO_CHAT_BOT" bigint, "ASSIGNED_TO_OPERATOR" bigint, "ASSIGNED_TO_SUPERVISOR" bigint, "REDIRECTED" bigint, "REDIRECTED_PERCENT" numeric, "POSTPONED" bigint, "REPEATED" bigint, "FCR" bigint, "FCR_PERCENT" bigint, "TOTAL_OPERATORS" bigint, "ONLINE_OPERATORS" bigint, "FREE_OPERATORS" bigint, "PENDING_OPERATOR_RESPONSE" bigint, "SL" numeric, "SL_PERCENT" numeric, "AHT" numeric, "AWT" numeric, "ASA" numeric, "ACW" numeric, "ACSI" numeric, "ASSI" numeric, "CSAT" numeric, "CDSAT" numeric, "ASA_ALARM" numeric, "ASA_WARNING" numeric, "AHT_ALARM" numeric, "AHT_WARNING" numeric, "ACSI_ALARM" numeric, "ACSI_WARNING" numeric, "ART_ALARM" numeric, "ART_WARNING" numeric, "AVERAGE_IN_QUEUE_TIME" numeric, "LOST" bigint, "LOST_PERCENT" numeric, "VOICE_COUNT" bigint, "VOICE_LOST" bigint, "VOICE_LOST_PERCENT" numeric, "IN_QUEUE_TIME" numeric, "IN_QUEUE_TIME_COUNT" bigint, "HT_COUNT" bigint, "HT_SUM" bigint, "HT_MIN" numeric, "HT_MAX" numeric, "WT_COUNT" bigint, "WT_SUM" bigint, "SA_COUNT" bigint, "SA_SUM" bigint, "SA_MIN" numeric, "SA_MAX" numeric, "CW_COUNT" bigint, "CW_SUM" bigint, "CSI_COUNT" bigint, "CSI_SUM" bigint, "CSI_GROUP_COUNT" bigint, "SSI_GROUP_COUNT" bigint, "SSI_COUNT" bigint, "SSI_SUM" bigint, "CSI_TOTAL_COUNT" bigint, "CSAT_COUNT" bigint, "CDSAT_COUNT" bigint, "ART" numeric, "ART_SUM" numeric, "ART_COUNT" numeric, "VOICE_ART" numeric, "VOICE_ART_SUM" numeric, "VOICE_ART_COUNT" numeric, "A1RT" numeric, "A1RT_SUM" numeric, "A1RT_COUNT" bigint, "A1RT_MAX" numeric, "TOTAL_OPERATORS_ALL" bigint, "ONLINE_OPERATORS_ALL" bigint, "FREE_OPERATORS_ALL" bigint, "NOW" timestamp with time zone, "DIVISION_NAME" text, "DIVISION_ID" uuid)
 LANGUAGE sql
 STABLE
AS $function$

WITH usertypeenum AS (
    SELECT
        0 AS "Unknown",
        1 AS "Operator",
        2 AS "Supervisor",
        3 AS "Bot",
        4 AS "System"
),

     actual_status AS (
         SELECT
             "V_OPERATOR_STATUSES"."STATUS_CODE" AS "Status",
             "V_OPERATOR_STATUSES"."OPERATOR_ID" AS "OperatorId"
         FROM
             "CALC"."V_OPERATOR_STATUSES"
     ),

     redirected_info AS (
         SELECT
             "REQUEST_EVENT"."SourceQueueId",
             COUNT(
                     DISTINCT CASE
                                  WHEN "REQUEST_EVENT"."IsRedirected" = true THEN "REQUEST_EVENT"."RequestId"
                                  ELSE NULL::bigint
                 END
             ) AS "REDIRECTED_COUNT"
         FROM
             "KPI"."REQUEST_EVENT"
         WHERE
             "REQUEST_EVENT"."EventTime" BETWEEN _startDate AND _endDate
         GROUP BY
             "REQUEST_EVENT"."SourceQueueId"
     ),

     pre_total_operators AS (
         SELECT
             DISTINCT erq."ExecutorId"
         FROM
             "CALC"."CFG_ExecutorRequestQueues" erq
     ),

     total_operators AS (
         SELECT
             COUNT(erq."ExecutorId") AS "TOTAL_OPERATORS",

             COUNT(
                     CASE
                         WHEN upper(actual_status."Status"::text) = upper('InWork'::text)
                             OR upper(actual_status."Status"::text) = upper('Ready'::text) THEN erq."ExecutorId"
                         ELSE NULL::uuid
                         END
             ) AS "ONLINE_OPERATORS",

             COUNT(
                     CASE
                         WHEN upper(actual_status."Status"::text) = upper('Ready'::text) THEN erq."ExecutorId"
                         ELSE NULL::uuid
                         END
             ) AS "FREE_OPERATORS"

         FROM
             pre_total_operators erq
                 JOIN actual_status ON actual_status."OperatorId" = erq."ExecutorId"
     ),

     total_operators_per_queue AS (
         SELECT
             erq."QueueId",
             COUNT(erq."ExecutorId") AS "TOTAL_OPERATORS",

             COUNT(
                     CASE
                         WHEN upper(actual_status."Status"::text) = upper('InWork'::text)
                             OR upper(actual_status."Status"::text) = upper('Ready'::text) THEN erq."ExecutorId"
                         ELSE NULL::uuid
                         END
             ) AS "ONLINE_OPERATORS",

             COUNT(
                     CASE
                         WHEN upper(actual_status."Status"::text) = upper('Ready'::text) THEN erq."ExecutorId"
                         ELSE NULL::uuid
                         END
             ) AS "FREE_OPERATORS"
         FROM
             "CALC"."CFG_ExecutorRequestQueues" erq
                 JOIN actual_status ON actual_status."OperatorId" = erq."ExecutorId"
         GROUP BY
             erq."QueueId"
     ),

     request_with_kpi_groupby_queueid_current AS (
         SELECT
             r."QueueId",
             COUNT(1) AS "TOTAL_REQUESTS",

             COUNT(1) FILTER (
                 WHERE r."TimeClosed" IS NOT NULL
                 ) AS "CLOSED_REQUESTS",

             COUNT(1) FILTER (
                 WHERE
                 r."TimeClosed" IS NOT NULL
                     AND r_kpi."ClosedByUserType" = (
                     (
                         SELECT usertypeenum."Operator"
                         FROM usertypeenum
                     )
                 )
                 ) AS "CLOSED_REQUESTS_BY_OPERATOR",

             COUNT(1) FILTER (
                 WHERE
                 r."TimeClosed" IS NOT NULL
                     AND r_kpi."ClosedByUserType" = (
                     (
                         SELECT usertypeenum."Supervisor"
                         FROM usertypeenum
                     )
                 )
                 ) AS "CLOSED_REQUESTS_BY_SUPERVISOR",

             COUNT(1) FILTER (
                 WHERE
                 r."TimeClosed" IS NOT NULL
                     AND
                 (
                     (
                         r_kpi."ClosedByUserType" = 4 -- System
                             OR r_kpi."ClosedByUserType" = 0 -- Unknown
                         )
                         OR
                     (
                         r_kpi."ClosedByUserType" IS NULL
                             AND r_kpi."TimeBotWorkEnded" IS NULL
                         )
                     )
                 ) AS "CLOSED_BY_SYSTEM",

             COUNT(1) FILTER (
                 WHERE
                 r."TimeClosed" IS NOT NULL
                     AND
                 (
                     r_kpi."ClosedByUserType" = 3 -- Bot
                         OR
                     (
                         r_kpi."ClosedByUserType" IS NULL
                             AND r_kpi."TimeBotWorkEnded" IS NOT NULL
                         )
                     )
                 ) AS "CLOSED_BY_BOT",

             COUNT(r."RepeatedRequestId") AS "REPEATED",
             COUNT(1) FILTER (
                 WHERE
                 r."RepeatedRequestId" IS NULL
                     AND r."TimeClosed" IS NOT NULL
                 ) AS "FCR",

             100 * (
                         COUNT(1) FILTER (
                     WHERE
                     r."RepeatedRequestId" IS NULL
                         AND r."TimeClosed" IS NOT NULL
                     )
                 ) / COUNT(1) AS "FCR_PERCENT",

             COUNT(1) FILTER (
                 WHERE
                 r_kpi."SA" IS NULL
                     AND r."Status" IN (
                     (
                         SELECT rss."Id"
                         FROM "CRPM_CFG"."RequestStateStatuses" rss
                         WHERE rss."Code"::text IN (
                                                    'OPERATOR.WAIT'::text,
                                                    'CLAIM.OPERATOR.WAIT'::text)
                     )
                 )
                 ) AS "PENDING_OPERATOR_RESPONSE",

             COUNT(1) FILTER (
                 WHERE
                 (r_kpi."SA" IS NOT NULL AND r_kpi."SA" <> 0)
                     AND r_kpi."SA"::double precision <= (
                     (
                         SELECT queueasa."AlarmASA"
                         FROM "CALC"."CFG_Queue_ASA" queueasa
                         WHERE queueasa."Id" = r."QueueId"
                     )
                 )
                 ) AS "SL",

             AVG(r_kpi."HT") FILTER ( WHERE r_kpi."HT" <> 0 ) AS "AHT",
             COUNT(r_kpi."HT") FILTER ( WHERE r_kpi."HT" <> 0 ) AS "Count_HT",
             SUM(r_kpi."HT") AS "Sum_HT",

             ROUND(
                     COALESCE(
                             MIN(
                                     CASE
                                         WHEN r_kpi."HT" <> 0 THEN r_kpi."HT"
                                         ELSE NULL::integer
                                         END
                             )::numeric(10, 0),
                             0::numeric(10, 0)
                     )
             ) AS "MIN_HT",

             ROUND(
                     COALESCE(
                             max(r_kpi."HT")::numeric(10, 0),
                             0::numeric(10, 0)
                     )
             ) AS "MAX_HT",

             AVG(r_kpi."WT") AS "AWT",
             COUNT(r_kpi."WT") AS "Count_WT",
             SUM(r_kpi."WT") AS "Sum_WT",
             AVG(r_kpi."SA") AS "ASA",
             COUNT(r_kpi."SA") AS "Count_SA",
             SUM(r_kpi."SA") AS "Sum_SA",

             ROUND(
                     COALESCE(
                             MIN(
                                     CASE
                                         WHEN r_kpi."SA" <> 0 THEN r_kpi."SA"
                                         ELSE NULL::integer
                                         END
                             ),
                             0
                     )::double precision
             )::numeric(10, 0) AS "MIN_SA",

             ROUND(COALESCE(max(r_kpi."SA"), 0)::double precision)::numeric(10, 0) AS "MAX_SA",

             MIN(
                     COALESCE(
                             (
                                 SELECT queueasa."AlarmASA"
                                 FROM "CALC"."CFG_Queue_ASA" queueasa
                                 WHERE queueasa."Id" = r."QueueId"
                             ),
                             0::double precision
                     )
             ) AS "AlarmASA",

             MIN(
                     COALESCE(
                             (
                                 SELECT queueasa."WarningASA"
                                 FROM "CALC"."CFG_Queue_ASA" queueasa
                                 WHERE queueasa."Id" = r."QueueId"
                             ),
                             0::double precision
                     )
             ) AS "WarningASA",

             MIN(
                     COALESCE(
                             (
                                 SELECT queueasa."AlarmAHT"
                                 FROM "CALC"."CFG_Queue_ASA" queueasa
                                 WHERE queueasa."Id" = r."QueueId"
                             ),
                             0::double precision
                     )
             ) AS "AlarmAHT",

             MIN(
                     COALESCE(
                             (
                                 SELECT queueasa."WarningAHT"
                                 FROM "CALC"."CFG_Queue_ASA" queueasa
                                 WHERE queueasa."Id" = r."QueueId"
                             ),
                             0::double precision
                     )
             ) AS "WarningAHT",

             MIN(
                     COALESCE(
                             (
                                 SELECT queueasa."AlarmACSI"
                                 FROM "CALC"."CFG_Queue_ASA" queueasa
                                 WHERE queueasa."Id" = r."QueueId"
                             ),
                             0::double precision
                     )
             ) AS "AlarmACSI",

             MIN(
                     COALESCE(
                             (
                                 SELECT queueasa."WarningACSI"
                                 FROM "CALC"."CFG_Queue_ASA" queueasa
                                 WHERE queueasa."Id" = r."QueueId"
                             ),
                             0::double precision
                     )
             ) AS "WarningACSI",

             MIN(
                     COALESCE(
                             (
                                 SELECT queueasa."AlarmART"
                                 FROM "CALC"."CFG_Queue_ASA" queueasa
                                 WHERE queueasa."Id" = r."QueueId"
                             ),
                             0::double precision
                     )
             ) AS "AlarmART",

             MIN(
                     COALESCE(
                             (
                                 SELECT queueasa."WarningART"
                                 FROM "CALC"."CFG_Queue_ASA" queueasa
                                 WHERE queueasa."Id" = r."QueueId"
                             ),
                             0::double precision
                     )
             ) AS "WarningART",

             COALESCE(
                     AVG(
                             date_part(
                                     'epoch'::text,
                                     COALESCE(
                                             r_kpi."TimeOperatorFirstDistribution"::timestamp with time zone,
                                             CASE
                                                 WHEN r."TimeClosed" = '0001-01-01 00:00:00'::timestamp without time zone
                                                     OR r."TimeClosed" IS NULL THEN timezone('utc'::text, now())::timestamp with time zone
                                                 ELSE r."TimeClosed"::timestamp with time zone
                                                 END
                                     ) - COALESCE(
                                             r_kpi."TimeBotWorkEnded"::timestamp with time zone,
                                             r."TimeCreated"::timestamp with time zone
                                         )
                             )
                     ),
                     0::double precision
             )::numeric(20, 0) AS "AverageInQueueTime",

             COUNT(r_kpi."TimeLost") AS "LOST",

             COALESCE(
                     COUNT(r_kpi."TimeLost")::double precision / NULLIF(
                             COUNT(r_kpi."Id")::double precision,
                             0::double precision
                                                                 ) * 100::double precision,
                     0::double precision
             )::numeric(10, 0) AS "LOST_PERCENT",

             COUNT(1) FILTER (
                 WHERE r_kpi."ChannelType" = 10
                 ) AS "VOICE_COUNT",

             COALESCE(
                     COUNT(
                             CASE
                                 WHEN r_kpi."ChannelType" = 10 THEN r_kpi."TimeLost"
                                 ELSE NULL::timestamp without time zone
                                 END
                     ),
                     0::bigint
             ) AS "VOICE_LOST",

             COALESCE(
                     COUNT(
                             CASE
                                 WHEN r_kpi."ChannelType" = 10 THEN r_kpi."TimeLost"
                                 ELSE NULL::timestamp without time zone
                                 END
                     )::double precision / NULLIF(
                             COUNT(r_kpi."Id")::double precision,
                             0::double precision
                                           ) * 100::double precision,
                     0::double precision
             )::numeric(10, 0) AS "VOICE_LOST_PERCENT",

             COALESCE(
                     AVG(
                             date_part(
                                     'epoch'::text,
                                     COALESCE(
                                             r_kpi."TimeOperatorFirstDistribution"::timestamp with time zone,
                                             CASE
                                                 WHEN r."TimeClosed" = '0001-01-01 00:00:00'::timestamp without time zone
                                                     OR r."TimeClosed" IS NULL THEN timezone('utc'::text, now())::timestamp with time zone
                                                 ELSE r."TimeClosed"::timestamp with time zone
                                                 END
                                     ) - COALESCE(r_kpi."TimeBotWorkEnded", r."TimeCreated")::timestamp with time zone
                             )
                     ),
                     0::double precision
             )::numeric(10, 0) AS "IN_QUEUE_TIME",

             CASE
                 WHEN AVG(
                         date_part(
                                 'epoch'::text,
                                 COALESCE(
                                         r_kpi."TimeOperatorFirstDistribution"::timestamp with time zone,
                                         CASE
                                             WHEN r."TimeClosed" = '0001-01-01 00:00:00'::timestamp without time zone
                                                 OR r."TimeClosed" IS NULL THEN timezone('utc'::text, now())::timestamp with time zone
                                             ELSE r."TimeClosed"::timestamp with time zone
                                             END
                                 ) - COALESCE(r_kpi."TimeBotWorkEnded", r."TimeCreated")::timestamp with time zone
                         )
                      ) IS NULL THEN 0
                 ELSE 1
                 END AS "IN_QUEUE_TIME_COUNT"
         FROM
             "CRPM_DATA"."Requests" r
                 LEFT JOIN "KPI"."REQUEST_KPI" r_kpi ON r."Id" = r_kpi."Id"
         WHERE
             r."TimeRegistered" BETWEEN _startDate AND _endDate
           AND r."SchemaId" <> 3 -- division
         GROUP BY
             r."QueueId"
     ),

     request_with_kpi_groupby_queueid_opened AS (
         SELECT
             r."QueueId",

             COUNT(1) FILTER (
                 WHERE(
                          ((
                              SELECT rss."Code"
                              FROM "CRPM_CFG"."RequestStateStatuses" rss
                              WHERE rss."Id" = r."Status"))::text
                          ) = 'OPERATOR.WAIT'::text
                 ) AS "OPENED_REQUESTS",

             COUNT(1) FILTER (
                 WHERE (
                           ((
                               SELECT rss."Code"
                               FROM "CRPM_CFG"."RequestStateStatuses" rss
                               WHERE rss."Id" = r."Status"))::text
                           ) = ANY (ARRAY [
                     'CHATBOT'::character varying::text,
                     'CHATBOT.WAIT'::character varying::text])
                 ) AS "ASSIGNED_TO_CHAT_BOT",

             COUNT(1) FILTER (
                 WHERE (
                           ((
                               SELECT rss."Code"
                               FROM "CRPM_CFG"."RequestStateStatuses" rss
                               WHERE rss."Id" = r."Status"))::text
                           ) = ANY (ARRAY [
                     'OPERATOR.WORK'::character varying::text,
                     'OPERATOR.PAUSE'::character varying::text,
                     'VOICE'::character varying::text,
                     'REQUEST.PROCESSING'::character varying::text])
                 ) AS "ASSIGNED_TO_OPERATOR",

             COUNT(1) FILTER (
                 WHERE (
                           ((
                               SELECT rss."Code"
                               FROM "CRPM_CFG"."RequestStateStatuses" rss
                               WHERE rss."Id" = r."Status"))::text
                           ) = 'SUPERVISOR'::text
                 ) AS "ASSIGNED_TO_SUPERVISOR",

             COUNT(1) FILTER (
                 WHERE (
                           ((
                               SELECT rss."Code"
                               FROM "CRPM_CFG"."RequestStateStatuses" rss
                               WHERE rss."Id" = r."Status"))::text
                           ) = 'OPERATOR.POSTPONE'::text
                 ) AS "POSTPONED",

             COUNT(1) FILTER (
                 WHERE(
                          ((
                              SELECT rss."Code"
                              FROM "CRPM_CFG"."RequestStateStatuses" rss
                              WHERE rss."Id" = r."Status"))::text
                          ) != 'OPERATOR.POSTPONE'::text
                     AND (
                             ((
                                 SELECT rss."Code"
                                 FROM "CRPM_CFG"."RequestStateStatuses" rss
                                 WHERE rss."Id" = r."Status"))::text
                             ) != 'OPERATOR.WAIT'::text
                 ) AS "REQUESTS_IN_PROCESSING"
         FROM
             "CRPM_DATA"."Requests" r
                 LEFT JOIN "KPI"."REQUEST_KPI" r_kpi ON r."Id" = r_kpi."Id"
         WHERE
             r."TimeClosed" IS NULL
           AND r."TimeRegistered" BETWEEN _startDate AND _endDate
           AND r."SchemaId" <> 3 -- division
         GROUP BY
             r."QueueId"
     ),

     art_req AS (
         SELECT
             q_art."QueueId",
             SUM(q_art."sumRT") AS "sumRT",
             SUM(q_art."countRT") AS "countRT",
             SUM(q_art."sumRT") / SUM(q_art."countRT") AS "ART"
         FROM (
                  SELECT (
                             SELECT r."QueueId"
                             FROM "CRPM_DATA"."Requests" r
                             WHERE r."Id" = ore."RequestId"
                         ) AS "QueueId",
                         SUM(ore."RT") AS "sumRT",
                         COUNT(ore."RT") AS "countRT"
                  FROM
                      "KPI"."OPERATOR_RESPONSE" ore
                  WHERE
                      ore."ReactionStartDate" BETWEEN _startDate AND _endDate
                  GROUP BY
                      ore."RequestId"
              ) AS q_art
         GROUP BY
             q_art."QueueId"
     ),

     art_req_voice AS (
         SELECT
             q_art_voice."QueueId",
             SUM(q_art_voice."sumRT") AS "sumRT",
             SUM(q_art_voice."countRT") AS "countRT",
             SUM(q_art_voice."sumRT") / SUM(q_art_voice."countRT") AS "ART"
         FROM (
                  SELECT (
                             SELECT r."QueueId"
                             FROM "CRPM_DATA"."Requests" r
                             WHERE r."Id" = ore."RequestId"
                         ) AS "QueueId",
                         SUM(ore."RT") AS "sumRT",
                         COUNT(ore."RT") AS "countRT"
                  FROM
                      "KPI"."OPERATOR_RESPONSE" ore
                  WHERE
                      ore."ReactionStartDate" BETWEEN _startDate AND _endDate
                    AND ore."ChannelType" = 10
                  GROUP BY
                      ore."RequestId"
              ) AS q_art_voice
         GROUP BY
             q_art_voice."QueueId"
     ),

     a1rt_req AS (
         SELECT
             q_art."QueueId",
             AVG(q_art."RT") AS "A1RT",
             SUM(q_art."RT") AS "sumRT",
             COALESCE(max(q_art."RT"), 0::bigint)::numeric(10, 0) AS "MAX_1RT",
             COUNT(q_art."RequestId") AS "countRT"
         FROM(
                 SELECT DISTINCT ON (ore."RequestId")
                     (   SELECT r."QueueId"
                         FROM "CRPM_DATA"."Requests" r
                         WHERE r."Id" = ore."RequestId"
                     ) AS "QueueId",
                     ore."RT",
                     ore."RequestId"
                 FROM
                     "KPI"."OPERATOR_RESPONSE" ore
                 WHERE
                     ore."ReactionStartDate" BETWEEN _startDate AND _endDate
                 ORDER BY
                     ore."RequestId",
                     ore."ReactionStartDate"
             ) AS q_art
         GROUP BY
             q_art."QueueId"
     ),

     acw_info AS (
         SELECT
             r."QueueId",
             AVG(ocs."ACW") AS "ACW",
             COUNT(ocs."ACW") AS "Count_CW",
             SUM(ocs."ACW") AS "Sum_CW"
         FROM
             "KPI"."OPERATOR_CALL_STATE" ocs
                 LEFT JOIN "CRPM_DATA"."Requests" AS r ON ocs."RequestId" = r."Id"
         WHERE
             ocs."TimeCallAccepted" BETWEEN _startDate AND _endDate
         GROUP BY
             r."QueueId"
     ),

     csi_info_evaluation_pre AS ( -- Присваиваем ранг каждой записи на базе Date, чтобы выбрать последнюю оценку (rank = 1)
         SELECT
             "RequestId",
             "Score",
             RANK() OVER (PARTITION BY "RequestId" ORDER BY "Date" DESC) AS "Rank"
         FROM "EXTERNAL_EVALUATIONS"."ExternalEvaluations"
     ),

     csi_info_evaluation_result AS (
         SELECT
             "RequestId",
             "Score"
         FROM csi_info_evaluation_pre
         WHERE "Rank" = 1
     ),

     csi_info AS ( -- Подсчёт CSI
         SELECT
             r."QueueId",
             AVG(e."Score") AS "ACSI",
             SUM(e."Score") AS "Sum_CSI",
             COUNT(e."Score") AS "Count_CSI",

             COUNT(
                     DISTINCT CASE
                                  WHEN e."Score" IS NULL THEN NULL::bigint
                                  ELSE e."Score"
                 END
             ) AS "CSI_GROUP_COUNT",

             COALESCE(COUNT(e."Score"), 0::bigint) AS "TOTAL_CSI_COUNT",

             COALESCE(
                     COUNT(
                             CASE
                                 WHEN e."Score" > 4 THEN e."Score"
                                 ELSE NULL::smallint
                                 END
                     )::numeric(10, 2) / NULLIF(COUNT(e."Score"), 0)::numeric,
                     0::numeric
             ) * 100::numeric(10, 2) AS "CSAT",

             COUNT(
                     CASE
                         WHEN e."Score" > 4 THEN e."Score"
                         ELSE NULL::smallint
                         END
             ) AS "Count_CSAT",

             COALESCE(
                     COUNT(
                             CASE
                                 WHEN e."Score" < 4 THEN e."Score"
                                 ELSE NULL::smallint
                                 END
                     )::numeric(10, 2) / NULLIF(COUNT(e."Score"), 0)::numeric,
                     0::numeric
             ) * 100::numeric(10, 2) AS "CDSAT",

             COUNT(
                     CASE
                         WHEN e."Score" < 4 THEN e."Score"
                         ELSE NULL::smallint
                         END
             ) AS "Count_CDSAT"
         FROM
             csi_info_evaluation_result AS e
                 LEFT JOIN "CRPM_DATA"."Requests" AS r ON r."Id" = e."RequestId"
         WHERE
             r."SchemaId" <> 3 -- division
           AND r."TimeRegistered" BETWEEN _startDate AND _endDate
         GROUP BY r."QueueId"
     ),

     ssi_info_from_rkpi AS (
         SELECT
             "Id" AS "RequestId",
             "SSI"
         FROM
             "KPI"."REQUEST_KPI"
         WHERE
             "TimeRegistered" BETWEEN _startDate AND _endDate
     ),

     ssi_info_from_rkpi_with_queueId AS
         (
             SELECT
                 r_kpi."RequestId",
                 r."QueueId",
                 r_kpi."SSI"
             FROM
                 ssi_info_from_rkpi r_kpi
                     LEFT JOIN "CRPM_DATA"."Requests" AS r ON r."Id" = r_kpi."RequestId"
             WHERE r."SchemaId" <> 3 -- division
         ),

     ssi_info_from_eval AS (
         SELECT
             r_kpi."RequestId",
             r_kpi."QueueId",
             e."ScoreResult" AS "EvalSSI",
             r_kpi."SSI" AS "ReqSSI",
             e."PublishedAt"
         FROM
             ssi_info_from_rkpi_with_queueId AS r_kpi
                 LEFT JOIN "CALC"."V_EVALUATIONS" AS e ON r_kpi."RequestId" = (e."Context"->>'requestId')::bigint
         WHERE
             e."ScoreResult" IS NOT NULL OR r_kpi."SSI" IS NOT NULL
     ),

     ssi_info_filtered AS (
         SELECT
             "QueueId",
             "RequestId",
             (CASE
                  WHEN ("ReqSSI" IS NOT NULL AND "PublishedAt" IS NULL) THEN "ReqSSI"
                  ELSE COALESCE("EvalSSI", "ReqSSI")
                 END) AS "SSI"
         FROM
             ssi_info_from_eval
     ),

     pre_ssi_info_result AS (
         SELECT
             "QueueId",
             "RequestId",
             AVG("SSI") AS "SSI"
         FROM
             ssi_info_filtered
         GROUP BY "RequestId", "QueueId"
     ),

     ssi_info_result AS (
         SELECT
             "QueueId",
             COUNT(
                     DISTINCT CASE
                                  WHEN "SSI" IS NULL THEN NULL::bigint
                                  ELSE "RequestId"
                 END
             ) AS "SSI_GROUP_COUNT",
             AVG("SSI") AS "ASSI",
             COUNT("SSI") AS "Count_SSI",
             SUM("SSI") AS "Sum_SSI"
         FROM pre_ssi_info_result
         GROUP BY "QueueId"
     ),

     operator_root_group AS (
     	SELECT operatorgroup."Name", operatorgroup."Id", rq."Id" as "QueueId"
		FROM
		    "CRPM_CFG"."CustomAttributes" ca
		    JOIN "CRPM_CFG"."RequestQueues" rq ON rq."Id" = ca."QueueId"
		    JOIN "AWP_INFRA"."OperatorGroups" operatorgroup ON operatorgroup."Id"::character varying::text = ca."Value"::text
		WHERE
		    ca."Key" = 'Division'::text),

     res AS( -- Результирующая CTE
         SELECT
             queues."Id" AS "ID",
             queues."Title" AS "TITLE",
             queues."TimeDeleted" AS "QUEUE_TIME_DELETED",
             COALESCE(rbq."TOTAL_REQUESTS", 0::bigint) AS "TOTAL_REQUESTS",
             COALESCE(rbq."CLOSED_REQUESTS", 0::bigint) AS "CLOSED_REQUESTS",
             COALESCE(rbq."CLOSED_REQUESTS_BY_OPERATOR", 0::bigint) AS "CLOSED_REQUESTS_BY_OPERATOR",
             COALESCE(rbq."CLOSED_REQUESTS_BY_SUPERVISOR", 0::bigint) AS "CLOSED_REQUESTS_BY_SUPERVISOR",
             COALESCE(rbq."CLOSED_BY_SYSTEM", 0::bigint) AS "CLOSED_REQUESTS_BY_SYSTEM",
             COALESCE(rbq."CLOSED_BY_BOT", 0::bigint) AS "CLOSED_REQUESTS_BY_BOT",

             COALESCE(
                     rbq."CLOSED_BY_BOT"::numeric / NULLIF(rbq."CLOSED_REQUESTS"::double precision, 0::double precision)::numeric(10, 2) * 100::numeric,
                     0::numeric(10, 2)
             )::numeric(10, 2) AS "CLOSED_REQUESTS_BY_BOT_PERCENT",

             COALESCE(rbqo."OPENED_REQUESTS", 0::bigint) AS "OPENED_REQUESTS",
             COALESCE(rbqo."REQUESTS_IN_PROCESSING", 0::bigint) AS "REQUESTS_IN_PROCESSING",
             COALESCE(rbqo."ASSIGNED_TO_CHAT_BOT", 0::bigint) AS "ASSIGNED_TO_CHAT_BOT",
             COALESCE(rbqo."ASSIGNED_TO_OPERATOR", 0::bigint) AS "ASSIGNED_TO_OPERATOR",
             COALESCE(rbqo."ASSIGNED_TO_SUPERVISOR", 0::bigint) AS "ASSIGNED_TO_SUPERVISOR",

             COALESCE(
                     (
                         SELECT ri."REDIRECTED_COUNT"
                         FROM redirected_info ri
                         WHERE ri."SourceQueueId" = queues."Id"
                     ),
                     0::bigint
             ) AS "REDIRECTED",

             (
                 CASE
                     WHEN COALESCE(
                                  (
                                      SELECT ri."REDIRECTED_COUNT"
                                      FROM redirected_info ri
                                      WHERE ri."SourceQueueId" = queues."Id"
                                  ),
                                  0::bigint
                          ) = 0 THEN 0::numeric(10, 0)
                     ELSE(
                         CASE
                             WHEN rbq."TOTAL_REQUESTS" IS NULL
                                 OR rbq."TOTAL_REQUESTS" = 0 THEN 100::numeric(10, 0)
                             ELSE (
                                 CASE
                                     WHEN (
                                              (
                                                  SELECT ri."REDIRECTED_COUNT"
                                                  FROM redirected_info ri
                                                  WHERE ri."SourceQueueId" = queues."Id"
                                              )::numeric(10, 2) / rbq."TOTAL_REQUESTS"::numeric(10, 2)
                                              ) > 1 THEN 100::numeric(10, 0)
                                     ELSE (
                                         100::numeric(10, 2) * (
                                             SELECT ri."REDIRECTED_COUNT"
                                             FROM redirected_info ri
                                             WHERE ri."SourceQueueId" = queues."Id"
                                         )::numeric(10, 2) / rbq."TOTAL_REQUESTS"::numeric(10, 2)
                                         )::numeric(10, 0)
                                     END
                                 )::numeric(10, 0)
                             END
                         )::numeric(10, 0)
                     END
                 )::numeric(10, 0) AS "REDIRECTED_PERCENT",

             COALESCE(rbqo."POSTPONED", 0::bigint) AS "POSTPONED",
             COALESCE(rbq."REPEATED", 0::bigint) AS "REPEATED",
             COALESCE(rbq."FCR", 0::bigint) AS "FCR",
             COALESCE(rbq."FCR_PERCENT", 0::bigint) AS "FCR_PERCENT",
             COALESCE(total_operators_per_queue."TOTAL_OPERATORS", 0::bigint) AS "TOTAL_OPERATORS",
             COALESCE(total_operators_per_queue."ONLINE_OPERATORS", 0::bigint) AS "ONLINE_OPERATORS",
             COALESCE(total_operators_per_queue."FREE_OPERATORS", 0::bigint) AS "FREE_OPERATORS",
             COALESCE(rbq."PENDING_OPERATOR_RESPONSE", 0::bigint) AS "PENDING_OPERATOR_RESPONSE",
             COALESCE(rbq."SL"::numeric(10, 2), 0::numeric) AS "SL",

             COALESCE(
                     rbq."SL"::numeric / NULLIF(rbq."TOTAL_REQUESTS"::double precision, 0::double precision)::numeric(10, 2) * 100::numeric,
                     0::numeric(10, 2)
             )::numeric(10, 0) AS "SL_PERCENT",

             COALESCE(rbq."AHT"::numeric(10, 2), 0::numeric) AS "AHT",
             COALESCE(rbq."AWT"::numeric(10, 2), 0::numeric) AS "AWT",
             COALESCE(rbq."ASA"::numeric(15, 2), 0::numeric) AS "ASA",
             COALESCE(acwi."ACW"::numeric(10, 2), 0::numeric) AS "ACW",
             COALESCE(csii."ACSI"::numeric(10, 2), 0::numeric) AS "ACSI",
             COALESCE(ssii."ASSI"::numeric(10, 2), 0::numeric) AS "ASSI",
             COALESCE(csii."CSAT"::numeric(10, 2), 0::numeric) AS "CSAT",
             COALESCE(csii."CDSAT"::numeric(10, 2), 0::numeric) AS "CDSAT",
             COALESCE(rbq."AlarmASA", 0::double precision)::numeric(10, 2) AS "ASA_ALARM",
             COALESCE(rbq."WarningASA", 0::double precision)::numeric(10, 2) AS "ASA_WARNING",
             COALESCE(rbq."AlarmAHT", 0::double precision)::numeric(10, 2) AS "AHT_ALARM",
             COALESCE(rbq."WarningAHT", 0::double precision)::numeric(10, 2) AS "AHT_WARNING",
             COALESCE(rbq."AlarmACSI", 0::double precision)::numeric(10, 2) AS "ACSI_ALARM",
             COALESCE(rbq."WarningACSI", 0::double precision)::numeric(10, 2) AS "ACSI_WARNING",
             COALESCE(rbq."AlarmART", 0::double precision)::numeric(10, 2) AS "ART_ALARM",
             COALESCE(rbq."WarningART", 0::double precision)::numeric(10, 2) AS "ART_WARNING",
             COALESCE(rbq."AverageInQueueTime", 0::numeric) AS "AVERAGE_IN_QUEUE_TIME",
             COALESCE(rbq."LOST", 0::bigint) AS "LOST",
             COALESCE(rbq."LOST_PERCENT", 0::bigint::numeric) AS "LOST_PERCENT",
             COALESCE(rbq."VOICE_COUNT", 0::bigint) AS "VOICE_COUNT",
             COALESCE(rbq."VOICE_LOST", 0::bigint) AS "VOICE_LOST",
             COALESCE(rbq."VOICE_LOST_PERCENT", 0::bigint::numeric) AS "VOICE_LOST_PERCENT",
             COALESCE(rbq."IN_QUEUE_TIME", 0::bigint::numeric) AS "IN_QUEUE_TIME",
             COALESCE(rbq."IN_QUEUE_TIME_COUNT"::bigint, 0::bigint) AS "IN_QUEUE_TIME_COUNT",
             COALESCE(rbq."Count_HT", 0::bigint) AS "HT_COUNT",
             COALESCE(rbq."Sum_HT", 0::bigint) AS "HT_SUM",
             COALESCE(rbq."MIN_HT", 0::bigint::numeric) AS "HT_MIN",
             COALESCE(rbq."MAX_HT", 0::bigint::numeric) AS "HT_MAX",
             COALESCE(rbq."Count_WT", 0::bigint) AS "WT_COUNT",
             COALESCE(rbq."Sum_WT", 0::bigint) AS "WT_SUM",
             COALESCE(rbq."Count_SA", 0::bigint) AS "SA_COUNT",
             COALESCE(rbq."Sum_SA", 0::bigint) AS "SA_SUM",
             COALESCE(rbq."MIN_SA", 0::bigint::numeric) AS "SA_MIN",
             COALESCE(rbq."MAX_SA", 0::bigint::numeric) AS "SA_MAX",
             COALESCE(acwi."Count_CW", 0::bigint) AS "CW_COUNT",
             COALESCE(acwi."Sum_CW", 0::bigint) AS "CW_SUM",
             COALESCE(csii."Count_CSI", 0::bigint) AS "CSI_COUNT",
             COALESCE(csii."Sum_CSI"::bigint, 0::bigint) AS "CSI_SUM",
             COALESCE(csii."CSI_GROUP_COUNT", 0::bigint) AS "CSI_GROUP_COUNT",
             COALESCE(ssii."SSI_GROUP_COUNT", 0)::bigint AS "SSI_GROUP_COUNT",
             COALESCE(ssii."Count_SSI", 0)::bigint AS "SSI_COUNT",
             COALESCE(ssii."Sum_SSI", 0)::bigint AS "SSI_SUM",
             COALESCE(csii."TOTAL_CSI_COUNT", 0::bigint) AS "CSI_TOTAL_COUNT",
             COALESCE(csii."Count_CSAT", 0::bigint) AS "CSAT_COUNT",
             COALESCE(csii."Count_CDSAT", 0::bigint) AS "CDSAT_COUNT",
             COALESCE(art_req."ART"::numeric(10, 2), 0::numeric) AS "ART",
             COALESCE(art_req."sumRT", 0::bigint::numeric) AS "ART_SUM",
             COALESCE(art_req."countRT", 0::bigint::numeric) AS "ART_COUNT",
             COALESCE(art_req_voice."ART"::numeric(10, 2), 0::numeric) AS "VOICE_ART",
             COALESCE(art_req_voice."sumRT", 0::bigint::numeric) AS "VOICE_ART_SUM",
             COALESCE(art_req_voice."countRT", 0::bigint::numeric) AS "VOICE_ART_COUNT",
             COALESCE(a1rt_req."A1RT"::numeric(15, 2), 0::numeric) AS "A1RT",
             COALESCE(a1rt_req."sumRT", 0::bigint::numeric) AS "A1RT_SUM",
             COALESCE(a1rt_req."countRT", 0::bigint) AS "A1RT_COUNT",

             COALESCE(a1rt_req."MAX_1RT", 0::bigint::numeric) AS "A1RT_MAX",
             (
                 SELECT total_operators."TOTAL_OPERATORS"
                 FROM total_operators
             ) AS "TOTAL_OPERATORS_ALL",

             (
                 SELECT total_operators."ONLINE_OPERATORS"
                 FROM total_operators
             ) AS "ONLINE_OPERATORS_ALL",

             (
                 SELECT total_operators."FREE_OPERATORS"
                 FROM total_operators
             ) AS "FREE_OPERATORS_ALL",

             timezone('UTC'::text, now()::timestamp with time zone)::timestamp with time zone AS "NOW",
    		 operator_root_group."Name" AS "DIVISION_NAME",
    		 operator_root_group."Id" AS "DIVISION_ID"
         FROM
             "CALC"."CFG_RequestQueues" queues
         LEFT JOIN request_with_kpi_groupby_queueid_current rbq ON rbq."QueueId" = queues."Id"
         LEFT JOIN request_with_kpi_groupby_queueid_opened rbqo ON rbqo."QueueId" = queues."Id"
         LEFT JOIN art_req ON art_req."QueueId" = queues."Id"
         LEFT JOIN art_req_voice ON art_req_voice."QueueId" = queues."Id"
         LEFT JOIN a1rt_req ON a1rt_req."QueueId" = queues."Id"
         LEFT JOIN total_operators_per_queue ON total_operators_per_queue."QueueId" = queues."Id"
         LEFT JOIN acw_info AS acwi ON acwi."QueueId" = queues."Id"
         LEFT JOIN csi_info AS csii ON csii."QueueId" = queues."Id"
         LEFT JOIN ssi_info_result AS ssii ON ssii."QueueId" = queues."Id"
         LEFT JOIN operator_root_group ON operator_root_group."QueueId" = queues."Id"
         WHERE
            queues."IsService" = FALSE
     )

SELECT -- Всё расчитывается в рамках одной очереди
       "ID", -- Id очереди
       "TITLE", -- Название очереди
       "QUEUE_TIME_DELETED", -- Время удаление очереди (Для фильтрации)
       "TOTAL_REQUESTS", -- Всего обращений в очереди
       "CLOSED_REQUESTS", -- Закрытые Всего
       "CLOSED_REQUESTS_BY_OPERATOR", -- Закрытые Оператором
       "CLOSED_REQUESTS_BY_SUPERVISOR", -- Закрытые Супервизором
       "CLOSED_REQUESTS_BY_SYSTEM", -- Закрытые Системой
       "CLOSED_REQUESTS_BY_BOT", -- Закрытые БОТом
       "CLOSED_REQUESTS_BY_BOT_PERCENT", -- % автоматиации (Доля обращений, которые были закрыты БОТом)
       "OPENED_REQUESTS", -- Открытых обращений
       "REQUESTS_IN_PROCESSING", -- В обоаботке
       "ASSIGNED_TO_CHAT_BOT", -- Назначено на чат-бота
       "ASSIGNED_TO_OPERATOR", -- Назначено на оператора
       "ASSIGNED_TO_SUPERVISOR", -- Назначено на супервизора
       "REDIRECTED", -- Переведено по очереди
       "REDIRECTED_PERCENT", -- Переведено %
       "POSTPONED", -- Отложено
       "REPEATED", -- Повторных
       "FCR", --
       "FCR_PERCENT", --
       "TOTAL_OPERATORS", -- Всего операторов
       "ONLINE_OPERATORS", -- Активных операторов
       "FREE_OPERATORS", -- Свободных операторов
       "PENDING_OPERATOR_RESPONSE", -- Ожидают обработки оператора
       "SL",
       "SL_PERCENT", -- % обращений, по которым клиент получил ответ за заданное для очереди целевое время ASA, от общего количества обращений в очереди
       "AHT",
       "AWT",
       "ASA", -- Средняя скорость ответа оператора от момента регистрации обращения / завершения обработки чат-ботом до момента первого ответа оператором
       "ACW", -- Среднее значение времени поствызовной обработки голосового вызова / видеовызова
       "ACSI", -- Средняя оценка качества обработки обращения, выставленная клиентом
       "ASSI", -- Среднее значение индекса удовлетворенности супервизора (SSI)
       "CSAT", -- Индекс удовлетворенности клиента в процентах (доля положительных оценок: CSI = 5)
       "CDSAT", -- Индекс неудовлетворенности клиента в процентах (доля отрицательных оценок: CSI = 1; 2; 3)
       "ASA_ALARM", --
       "ASA_WARNING", --
       "AHT_ALARM", --
       "AHT_WARNING", --
       "ACSI_ALARM", --
       "ACSI_WARNING", --
       "ART_ALARM", --
       "ART_WARNING", --
       "AVERAGE_IN_QUEUE_TIME", --
       "LOST", -- Потеряно
       "LOST_PERCENT", -- % Потеряно
       "VOICE_COUNT", -- Количество голосовых обращений
       "VOICE_LOST", -- Потеряно голосовых
       "VOICE_LOST_PERCENT", --
       "IN_QUEUE_TIME", --
       "IN_QUEUE_TIME_COUNT", --
       "HT_COUNT", --
       "HT_SUM", --
       "HT_MIN", --
       "HT_MAX", --
       "WT_COUNT", --
       "WT_SUM", --
       "SA_COUNT", --
       "SA_SUM", --
       "SA_MIN", --
       "SA_MAX", --
       "CW_COUNT", --
       "CW_SUM", --
       "CSI_COUNT", --
       "CSI_SUM", --
       "CSI_GROUP_COUNT", --
       "SSI_GROUP_COUNT", --
       "SSI_COUNT", --
       "SSI_SUM", --
       "CSI_TOTAL_COUNT", --
       "CSAT_COUNT", --
       "CDSAT_COUNT", --
       "ART", --
       "ART_SUM", --
       "ART_COUNT", --
       "VOICE_ART", --
       "VOICE_ART_SUM", --
       "VOICE_ART_COUNT", --
       "A1RT", --
       "A1RT_SUM", --
       "A1RT_COUNT", --
       "A1RT_MAX", --
       "TOTAL_OPERATORS_ALL", --
       "ONLINE_OPERATORS_ALL", --
       "FREE_OPERATORS_ALL", --
       "NOW", --
       "DIVISION_NAME",
       "DIVISION_ID"
FROM
    res AS r;

$function$
;