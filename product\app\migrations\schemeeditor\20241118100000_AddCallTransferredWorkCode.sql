WITH work_id AS (
    SELECT "Id" FROM "CRPM_CFG"."RequestStateWorks"
                WHERE "Title" = 'Обработка оператором'
                AND "RequestStateId" = (SELECT "Id" FROM "CRPM_CFG"."RequestStates" WHERE "Code"='CALL_CONTROL_OPERATOR_WORK')
)

INSERT INTO "CRPM_CFG"."WorkSuccessCodes"
(
    "WorkId", "ForceStateChange", "Code", "TargetStatusId", "RowVersion"
)
SELECT (SELECT "Id" FROM work_id), true, 'CALL.TRANSFER', (SELECT "Id" FROM "CRPM_CFG"."RequestStateStatuses" WHERE "Code"='CALL_CONTROL_OPERATOR_TRANSFER_WAIT'), 638512078309379863
WHERE NOT EXISTS(SELECT "Id" FROM "CRPM_CFG"."WorkSuccessCodes" WHERE "Code" = 'CALL.TRANSFER' and "WorkId" = (SELECT "Id" FROM work_id));
