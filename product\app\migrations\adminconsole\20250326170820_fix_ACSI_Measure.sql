-- Удалить единицу измерения для ACSI
UPDATE "AWP_INFRA"."Configurations"
SET "Value" = jsonb_pretty(jsonb_set(
        "Value"::jsonb,
        '{ThresholdValues}',
        (
            SELECT jsonb_agg(
                           CASE
                               WHEN elem->>'MeasurementCustomAttributeName' = 'Queue.ThresholdValues.MeasureACSI' THEN
                                   elem - 'MeasurementCustomAttributeName'
                               ELSE
                                   elem
                               END
                   )
            FROM jsonb_array_elements("Value"::jsonb->'ThresholdValues') AS elem
        )
                           ))
WHERE "Name" = 'SettingsServiceConfiguration';

-- Удалить некорректную настройку в очередях, если уже настроено
DELETE FROM "CRPM_CFG"."CustomAttributes"
WHERE "Key" = 'Queue.ThresholdValues.MeasureACSI';
