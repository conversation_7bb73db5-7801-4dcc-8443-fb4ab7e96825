WITH 
    ScriptStateId AS (
        SELECT "Id" FROM "CRPM_CFG"."RequestStates"
                    WHERE "Code" = 'SCRIPTBOT'
                    LIMIT 1
    ),
    ScriptBotWorkId AS (
        SELECT "Id" FROM "CRPM_CFG"."RequestStateWorks"
                    WHERE "RequestStateId" = (SELECT * FROM ScriptStateId)
                    LIMIT 1
    ),
    SendPvooId AS (
        SELECT "Id" FROM "CRPM_CFG"."RequestStateStatuses"
        WHERE "Code" = 'SEND.PVOO'
        LIMIT 1
    )
INSERT INTO "CRPM_CFG"."WorkErrorCodes"(
    "Id", "RetryCount", "RetryDelay", "WorkId", "ForceStateChange", "Code", "TargetStatusId", "RowVersion")
SELECT
    nextval('"CRPM_CFG"."WorkErrorCodes_Id_seq"'::regclass),
    v."RetryCount",
    v."RetryDelay",
    (SELECT * FROM ScriptBotWorkId),
    v."ForceStateChange"::boolean,
    v."Code",
    (SELECT * FROM SendPvooId),
    v."RowVersion"
FROM (
    VALUES 
        (0, NULL::integer, false, 'CHATBOT.ERROR.GENERIC', 638600193231761611),
        (3, 2, false, 'CHATBOT.ERROR.INFRASTRUCTURE', 638600193743489508)
) AS v("RetryCount", "RetryDelay", "ForceStateChange", "Code", "RowVersion")
WHERE NOT EXISTS (
    SELECT 1 
    FROM "CRPM_CFG"."WorkErrorCodes"
    WHERE "WorkId" = (SELECT * FROM ScriptBotWorkId)
    AND "Code" = v."Code"
);