CREATE OR REPLACE FUNCTION create_service_role_and_grant_access(
    role_name text, 
    password text DEFAULT '{transform:DbPassword.Ticketing}',
    grant_connect_to_db text DEFAULT 'product-ticketing'
) RETURNS void AS $EF$
DECLARE
    schema_record RECORD;
    role_exists boolean;
BEGIN
	-- Удаляем роль если она существует
    IF EXISTS (SELECT 1 FROM pg_roles WHERE rolname = role_name) THEN
        -- Завершаем все активные сессии этой роли
        PERFORM pg_terminate_backend(pid) 
        FROM pg_stat_activity 
        WHERE usename = role_name;
        
        -- Переназначаем и удаляем зависимые объекты
        EXECUTE format('REASSIGN OWNED BY %I TO postgres', role_name);
        EXECUTE format('DROP OWNED BY %I', role_name);
        EXECUTE format('DROP ROLE %I', role_name);
        
        RAISE NOTICE 'Role "%" already exists. Recreating...', role_name;
    END IF;

    -- Создаём новую роль
    EXECUTE format('
        CREATE ROLE %I WITH
        LOGIN
        NOSUPERUSER
        NOCREATEDB
        NOCREATEROLE
        NOINHERIT
        NOREPLICATION
        PASSWORD %L
        CONNECTION LIMIT -1',
        role_name, password);
    
    -- Права на подключение к БД
    IF grant_connect_to_db IS NOT NULL THEN
        BEGIN
            EXECUTE format('GRANT CONNECT ON DATABASE %I TO %I', grant_connect_to_db, role_name);
        EXCEPTION WHEN OTHERS THEN
            RAISE WARNING 'Error grant acces for DB "%": %', grant_connect_to_db, SQLERRM;
        END;
    END IF;
    
    -- Права на все существующие схемы (кроме системных)
    FOR schema_record IN 
        SELECT nspname 
        FROM pg_namespace 
        WHERE nspname NOT LIKE 'pg_%' 
        AND nspname != 'information_schema'
        AND nspname != 'pg_catalog'
    LOOP
        BEGIN
            -- Права на схему
            EXECUTE format('GRANT USAGE ON SCHEMA %I TO %I', schema_record.nspname, role_name);
            
            -- Права на существующие таблицы
            EXECUTE format('
                GRANT SELECT, INSERT, UPDATE, DELETE 
                ON ALL TABLES IN SCHEMA %I TO %I', 
                schema_record.nspname, role_name);

            -- Права на последовательности
            EXECUTE format('
                GRANT USAGE, SELECT, UPDATE 
                ON ALL SEQUENCES IN SCHEMA %I TO %I',
               schema_record.nspname, role_name);

            -- Права на функции
            EXECUTE format('
                GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA %I TO %I', 
                schema_record.nspname, role_name);
            
            -- Права на будущие таблицы
            EXECUTE format('
                ALTER DEFAULT PRIVILEGES IN SCHEMA %I 
                GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO %I', 
                schema_record.nspname, role_name);

            -- Права на будущие последовательности
            EXECUTE format('
                ALTER DEFAULT PRIVILEGES IN SCHEMA %I 
                GRANT USAGE, SELECT, UPDATE ON SEQUENCES TO %I',
                schema_record.nspname, role_name);
            
            -- Права на будущие функции
            EXECUTE format('
                ALTER DEFAULT PRIVILEGES IN SCHEMA %I 
                GRANT EXECUTE ON FUNCTIONS TO %I', 
                schema_record.nspname, role_name);
                
        EXCEPTION WHEN OTHERS THEN
            RAISE WARNING 'Error "%": %', 
                         schema_record.nspname, SQLERRM;
        END;
    END LOOP;
    
    RAISE NOTICE 'Role "%" created', role_name;
END;
$EF$ LANGUAGE plpgsql;

-- Создание ролей и выдача прав
SELECT create_service_role_and_grant_access(role_name) 
FROM unnest(ARRAY[
    'channels_ticketing',
    'crpm_ticketing',
    'crpm_ucmm_ticketing',
    'ucmm_ticketing'
]) AS role_name;

DROP FUNCTION create_service_role_and_grant_access(role_name text, password text, grant_connect_to_db text);