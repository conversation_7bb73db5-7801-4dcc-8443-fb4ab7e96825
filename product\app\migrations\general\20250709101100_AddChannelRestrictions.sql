DO $EF$
BEGIN
	IF NOT EXISTS (SELECT 1 FROM "CRPM_CFG"."ExecChannelCounts" WHERE "Channel"='CHANNEL.VIDEOCHAT' AND "ExecutionManagerCode"='WebAwpWorkExecutionManager') THEN
		INSERT INTO "CRPM_CFG"."ExecChannelCounts"("Channel", "MaxCount", "RowVersion", "Id", "ExecutionManagerCode")
		VALUES ('CHANNEL.VIDEOCHAT', 1, 638876352788619535, '022c2a32-267a-42cf-8796-e370690e06c6', 'WebAwpWorkExecutionManager');
	END IF;

	IF NOT EXISTS (SELECT 1 FROM "CRPM_CFG"."ExecChannelCounts" WHERE "Channel"='CHANNEL.CALLBACK' AND "ExecutionManagerCode"='WebAwpWorkExecutionManager') THEN
		INSERT INTO "CRPM_CFG"."ExecChannelCounts"("Channel", "MaxCount", "RowVersion", "Id", "ExecutionManagerCode")
		VALUES ('CHANNEL.CALLBACK', 1, 638876352788619535, '1f181ac9-243f-405a-8e78-bdc1e79ea4d0', 'WebAwpWorkExecutionManager');
	END IF;
END $EF$;