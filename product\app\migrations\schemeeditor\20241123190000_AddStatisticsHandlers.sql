INSERT INTO "CRPM_CFG"."RequestEventHandlers" ("RowVersion", "Title", "Configuration", "TypeCode", "IsEnabled", "EventType", "StreamId", "SchemeId", "StateId", "StatusId")
SELECT 638657945451038553, 'Статистика. Обращение переоткрыто',
'<?xml version="1.0" encoding="utf-16"?>
<RequestReopenedHandlerConfig />',
'RequestReopenedHandler', true, 'RequestStateStatusChanged', 1, NULL, NULL, NULL
WHERE NOT EXISTS(
        SELECT "Id" FROM "CRPM_CFG"."RequestEventHandlers" WHERE "TypeCode"='RequestReopenedHandler' AND "EventType"='RequestStateStatusChanged'
    );

INSERT INTO "CRPM_CFG"."RequestEventHandlers" ("RowVersion", "Title", "Configuration", "TypeCode", "IsEnabled", "EventType", "StreamId", "SchemeId", "StateId", "StatusId")
SELECT 638657945451038553, 'Статистика. Изменение тематик',
'<?xml version="1.0" encoding="utf-16"?>
<RequestSubjectsChangedHandlerConfig />',
'RequestSubjectsChangedHandler', true, 'RequestSubjectsChanged', 1, NULL, NULL, NULL
WHERE NOT EXISTS(
        SELECT "Id" FROM "CRPM_CFG"."RequestEventHandlers" WHERE "TypeCode"='RequestSubjectsChangedHandler' AND "EventType"='RequestSubjectsChanged'
    );

