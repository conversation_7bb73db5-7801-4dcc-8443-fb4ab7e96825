DO $SCRIPTBOTSTEPTIMOUTINIT$

DECLARE maxAttributeId smallint; -- Index последнего кастомного атрибута в настройке CustomAttributesMapForRequests с типом Long

BEGIN

SELECT
    (xpath(
        '//*[local-name()="Mapping" and @Type="Long" and @Index = (//*[local-name()="Mapping" and @Type="Long"]/@Index)[position()=last()]]/@Index',
        xmlparse(DOCUMENT "Configuration")
    ))[1]::text::int + 1 into maxAttributeId
FROM "CRPM_CFG"."Settings"
WHERE xmlparse(DOCUMENT "Configuration") IS NOT NULL AND "Title" = 'CustomAttributesMapForRequests';
IF maxAttributeId IS NULL OR maxAttributeId = 0 THEN
SET maxAttributeId = 1;
END IF;

-- Добавление кастома ChatBotDelay в CustomAttributesMapForRequests
RAISE NOTICE '[ScriptbotStepTimeout.CustomAttributesMapForRequests] Start add ChatBotDelay to CustomAttributesMapForRequests settings';
UPDATE "CRPM_CFG"."Settings"
SET "Configuration" = regexp_replace(
    "Configuration",
    '(<!--Binary-->)',
    format('<Mapping Code="ChatBotDelay" Type="Long" Index="%s" />
    \1', maxAttributeId),
    'i'
)
WHERE "Title" = 'CustomAttributesMapForRequests' AND "Configuration" NOT LIKE '%<Mapping Code="ChatBotDelay"%';
RAISE NOTICE '[ScriptbotStepTimeout.CustomAttributesMapForRequests] End updating ChatBotDelay settings';
-------------------------------------------------------------------------------------------

END $SCRIPTBOTSTEPTIMOUTINIT$;