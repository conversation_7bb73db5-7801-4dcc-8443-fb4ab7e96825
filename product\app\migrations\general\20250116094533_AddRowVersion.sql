DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "KPI"."__KpiServiceDbContextMigrations" WHERE "MigrationId" = '20250116094533_AddRowVersion') THEN
    ALTER TABLE "KPI"."OPERATOR_SLOT_TIME" ADD "RowVersion" bigint NOT NULL DEFAULT 0;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "KPI"."__KpiServiceDbContextMigrations" WHERE "MigrationId" = '20250116094533_AddRowVersion') THEN
    INSERT INTO "KPI"."__KpiServiceDbContextMigrations" ("MigrationId", "ProductVersion")
    VALUES ('20250116094533_AddRowVersion', '9.0.1');
    END IF;
END $EF$;

