DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM pg_namespace WHERE nspname = 'SERVICE_SESSIONS_CFG') THEN
        CREATE SCHEMA "SERVICE_SESSIONS_CFG";
    END IF;
END $EF$;
CREATE TABLE IF NOT EXISTS "SERVICE_SESSIONS_CFG"."__ServiceSystemsConfigurationDbContext" (
    "MigrationId" character varying(150) NOT NULL,
    "ProductVersion" character varying(32) NOT NULL,
    CONSTRAINT "PK___ServiceSystemsConfigurationDbContext" PRIMARY KEY ("MigrationId")
);

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "SERVICE_SESSIONS_CFG"."__ServiceSystemsConfigurationDbContext" WHERE "MigrationId" = '20250322094211_Init') THEN
        IF NOT EXISTS(SELECT 1 FROM pg_namespace WHERE nspname = 'SERVICE_SESSIONS_CFG') THEN
            CREATE SCHEMA "SERVICE_SESSIONS_CFG";
        END IF;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "SERVICE_SESSIONS_CFG"."__ServiceSystemsConfigurationDbContext" WHERE "MigrationId" = '20250322094211_Init') THEN
        IF NOT EXISTS(SELECT 1 FROM pg_namespace WHERE nspname = 'SERVICE_SESSIONS_CFG') THEN
            CREATE SCHEMA "SERVICE_SESSIONS_CFG";
        END IF;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "SERVICE_SESSIONS_CFG"."__ServiceSystemsConfigurationDbContext" WHERE "MigrationId" = '20250322094211_Init') THEN
    CREATE TYPE "SERVICE_SESSIONS_CFG".search_form_field_type AS ENUM ('date', 'date_time', 'number', 'string');
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "SERVICE_SESSIONS_CFG"."__ServiceSystemsConfigurationDbContext" WHERE "MigrationId" = '20250322094211_Init') THEN
    CREATE TABLE "SERVICE_SESSIONS_CFG"."ServiceSystems" (
        "Id" smallint GENERATED BY DEFAULT AS IDENTITY,
        "Code" text NOT NULL,
        "Name" text NOT NULL,
        "Description" text,
        "IsEnabled" boolean NOT NULL DEFAULT FALSE,
        "IsDeleted" boolean NOT NULL DEFAULT FALSE,
        "AdapterType" text NOT NULL,
        "AdapterConfiguration" text,
        CONSTRAINT "PK_ServiceSystems" PRIMARY KEY ("Id")
    );
    COMMENT ON TABLE "SERVICE_SESSIONS_CFG"."ServiceSystems" IS 'Система обслуживания';
    COMMENT ON COLUMN "SERVICE_SESSIONS_CFG"."ServiceSystems"."Id" IS 'Уникальный идентификатор';
    COMMENT ON COLUMN "SERVICE_SESSIONS_CFG"."ServiceSystems"."Code" IS 'Код системы обслуживания';
    COMMENT ON COLUMN "SERVICE_SESSIONS_CFG"."ServiceSystems"."Name" IS 'Название системы обслуживания';
    COMMENT ON COLUMN "SERVICE_SESSIONS_CFG"."ServiceSystems"."Description" IS 'Описание';
    COMMENT ON COLUMN "SERVICE_SESSIONS_CFG"."ServiceSystems"."IsEnabled" IS 'Признак активности';
    COMMENT ON COLUMN "SERVICE_SESSIONS_CFG"."ServiceSystems"."IsDeleted" IS 'Признак удаления';
    COMMENT ON COLUMN "SERVICE_SESSIONS_CFG"."ServiceSystems"."AdapterType" IS 'Тип адаптера';
    COMMENT ON COLUMN "SERVICE_SESSIONS_CFG"."ServiceSystems"."AdapterConfiguration" IS 'Конфигурация адаптера';
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "SERVICE_SESSIONS_CFG"."__ServiceSystemsConfigurationDbContext" WHERE "MigrationId" = '20250322094211_Init') THEN
    CREATE TABLE "SERVICE_SESSIONS_CFG"."ServiceObjectTypes" (
        "Id" smallint GENERATED BY DEFAULT AS IDENTITY,
        "Code" text NOT NULL,
        "Name" text NOT NULL,
        "Description" text,
        "IsEnabled" boolean NOT NULL DEFAULT FALSE,
        "IsDeleted" boolean NOT NULL DEFAULT FALSE,
        "AdapterConfiguration" text,
        "ServiceSystemId" smallint NOT NULL,
        CONSTRAINT "PK_ServiceObjectTypes" PRIMARY KEY ("Id"),
        CONSTRAINT "FK_ServiceObjectTypes_ServiceSystems_ServiceSystemId" FOREIGN KEY ("ServiceSystemId") REFERENCES "SERVICE_SESSIONS_CFG"."ServiceSystems" ("Id") ON DELETE CASCADE
    );
    COMMENT ON TABLE "SERVICE_SESSIONS_CFG"."ServiceObjectTypes" IS 'Тип объекта';
    COMMENT ON COLUMN "SERVICE_SESSIONS_CFG"."ServiceObjectTypes"."Id" IS 'Уникальный идентификатор';
    COMMENT ON COLUMN "SERVICE_SESSIONS_CFG"."ServiceObjectTypes"."Code" IS 'Код типа объекта';
    COMMENT ON COLUMN "SERVICE_SESSIONS_CFG"."ServiceObjectTypes"."Name" IS 'Название типа объекта (договор, клиент, адрес, т.д.)';
    COMMENT ON COLUMN "SERVICE_SESSIONS_CFG"."ServiceObjectTypes"."Description" IS 'Описание типа объекта';
    COMMENT ON COLUMN "SERVICE_SESSIONS_CFG"."ServiceObjectTypes"."IsEnabled" IS 'Признак активности';
    COMMENT ON COLUMN "SERVICE_SESSIONS_CFG"."ServiceObjectTypes"."IsDeleted" IS 'Признак удаления';
    COMMENT ON COLUMN "SERVICE_SESSIONS_CFG"."ServiceObjectTypes"."AdapterConfiguration" IS 'Конфигурация адаптера';
    COMMENT ON COLUMN "SERVICE_SESSIONS_CFG"."ServiceObjectTypes"."ServiceSystemId" IS 'Связь с системой обслуживания';
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "SERVICE_SESSIONS_CFG"."__ServiceSystemsConfigurationDbContext" WHERE "MigrationId" = '20250322094211_Init') THEN
    CREATE TABLE "SERVICE_SESSIONS_CFG"."SearchForms" (
        "Id" smallint GENERATED BY DEFAULT AS IDENTITY,
        "Code" text NOT NULL,
        "Name" text NOT NULL,
        "IsEnabled" boolean NOT NULL DEFAULT FALSE,
        "IsDeleted" boolean NOT NULL DEFAULT FALSE,
        "AdapterConfiguration" text,
        "ResultItemIdPath" text NOT NULL,
        "ServiceObjectTypeId" smallint NOT NULL,
        CONSTRAINT "PK_SearchForms" PRIMARY KEY ("Id"),
        CONSTRAINT "FK_SearchForms_ServiceObjectTypes_ServiceObjectTypeId" FOREIGN KEY ("ServiceObjectTypeId") REFERENCES "SERVICE_SESSIONS_CFG"."ServiceObjectTypes" ("Id") ON DELETE CASCADE
    );
    COMMENT ON TABLE "SERVICE_SESSIONS_CFG"."SearchForms" IS 'Форма поиска';
    COMMENT ON COLUMN "SERVICE_SESSIONS_CFG"."SearchForms"."Id" IS 'Уникальный идентификатор';
    COMMENT ON COLUMN "SERVICE_SESSIONS_CFG"."SearchForms"."Code" IS 'Код формы поиска';
    COMMENT ON COLUMN "SERVICE_SESSIONS_CFG"."SearchForms"."Name" IS 'Название формы';
    COMMENT ON COLUMN "SERVICE_SESSIONS_CFG"."SearchForms"."IsEnabled" IS 'Признак активности';
    COMMENT ON COLUMN "SERVICE_SESSIONS_CFG"."SearchForms"."IsDeleted" IS 'Признак удаления';
    COMMENT ON COLUMN "SERVICE_SESSIONS_CFG"."SearchForms"."AdapterConfiguration" IS 'Конфигурация адаптера';
    COMMENT ON COLUMN "SERVICE_SESSIONS_CFG"."SearchForms"."ResultItemIdPath" IS 'Представляет путь до идентификатора объекта обслуживания во внешней системе';
    COMMENT ON COLUMN "SERVICE_SESSIONS_CFG"."SearchForms"."ServiceObjectTypeId" IS 'Связь с ServiceObjectTypes';
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "SERVICE_SESSIONS_CFG"."__ServiceSystemsConfigurationDbContext" WHERE "MigrationId" = '20250322094211_Init') THEN
    CREATE TABLE "SERVICE_SESSIONS_CFG"."ServiceObjectTypeResultFields" (
        "Id" smallint GENERATED BY DEFAULT AS IDENTITY,
        "FieldName" text NOT NULL,
        "FieldType" "SERVICE_SESSIONS_CFG".search_form_field_type NOT NULL,
        "FieldPath" text NOT NULL,
        "Description" text,
        "ServiceObjectTypeId" smallint NOT NULL,
        CONSTRAINT "PK_ServiceObjectTypeResultFields" PRIMARY KEY ("Id"),
        CONSTRAINT "FK_ServiceObjectTypeResultFields_ServiceObjectTypes_ServiceObj~" FOREIGN KEY ("ServiceObjectTypeId") REFERENCES "SERVICE_SESSIONS_CFG"."ServiceObjectTypes" ("Id") ON DELETE CASCADE
    );
    COMMENT ON TABLE "SERVICE_SESSIONS_CFG"."ServiceObjectTypeResultFields" IS 'Результирующее поле типа объекта';
    COMMENT ON COLUMN "SERVICE_SESSIONS_CFG"."ServiceObjectTypeResultFields"."Id" IS 'Уникальный идентификатор';
    COMMENT ON COLUMN "SERVICE_SESSIONS_CFG"."ServiceObjectTypeResultFields"."FieldName" IS 'Название поля';
    COMMENT ON COLUMN "SERVICE_SESSIONS_CFG"."ServiceObjectTypeResultFields"."FieldType" IS 'Тип данных';
    COMMENT ON COLUMN "SERVICE_SESSIONS_CFG"."ServiceObjectTypeResultFields"."FieldPath" IS 'Путь до поля';
    COMMENT ON COLUMN "SERVICE_SESSIONS_CFG"."ServiceObjectTypeResultFields"."Description" IS 'Описание/Подсказка для оператора';
    COMMENT ON COLUMN "SERVICE_SESSIONS_CFG"."ServiceObjectTypeResultFields"."ServiceObjectTypeId" IS 'Связь с ServiceObjectTypes';
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "SERVICE_SESSIONS_CFG"."__ServiceSystemsConfigurationDbContext" WHERE "MigrationId" = '20250322094211_Init') THEN
    CREATE TABLE "SERVICE_SESSIONS_CFG"."SearchFormRequestFields" (
        "Id" smallint GENERATED BY DEFAULT AS IDENTITY,
        "FieldName" text NOT NULL,
        "FieldType" "SERVICE_SESSIONS_CFG".search_form_field_type NOT NULL,
        "IsRequired" boolean NOT NULL,
        "FieldPath" text NOT NULL,
        "InputMask" text,
        "Description" text,
        "SearchFormId" smallint NOT NULL,
        CONSTRAINT "PK_SearchFormRequestFields" PRIMARY KEY ("Id"),
        CONSTRAINT "FK_SearchFormRequestFields_SearchForms_SearchFormId" FOREIGN KEY ("SearchFormId") REFERENCES "SERVICE_SESSIONS_CFG"."SearchForms" ("Id") ON DELETE CASCADE
    );
    COMMENT ON TABLE "SERVICE_SESSIONS_CFG"."SearchFormRequestFields" IS 'Поле формы поиска для запроса';
    COMMENT ON COLUMN "SERVICE_SESSIONS_CFG"."SearchFormRequestFields"."Id" IS 'Уникальный идентификатор';
    COMMENT ON COLUMN "SERVICE_SESSIONS_CFG"."SearchFormRequestFields"."FieldName" IS 'Название поля';
    COMMENT ON COLUMN "SERVICE_SESSIONS_CFG"."SearchFormRequestFields"."FieldType" IS 'Тип данных';
    COMMENT ON COLUMN "SERVICE_SESSIONS_CFG"."SearchFormRequestFields"."IsRequired" IS 'Обязательное ли поле';
    COMMENT ON COLUMN "SERVICE_SESSIONS_CFG"."SearchFormRequestFields"."FieldPath" IS 'Путь до поля';
    COMMENT ON COLUMN "SERVICE_SESSIONS_CFG"."SearchFormRequestFields"."InputMask" IS 'Маска ввода';
    COMMENT ON COLUMN "SERVICE_SESSIONS_CFG"."SearchFormRequestFields"."Description" IS 'Описание/Подсказка для оператора';
    COMMENT ON COLUMN "SERVICE_SESSIONS_CFG"."SearchFormRequestFields"."SearchFormId" IS 'Связь с формой поиска';
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "SERVICE_SESSIONS_CFG"."__ServiceSystemsConfigurationDbContext" WHERE "MigrationId" = '20250322094211_Init') THEN
    CREATE TABLE "SERVICE_SESSIONS_CFG"."SearchFormResultFields" (
        "Id" smallint GENERATED BY DEFAULT AS IDENTITY,
        "FieldName" text NOT NULL,
        "FieldType" "SERVICE_SESSIONS_CFG".search_form_field_type NOT NULL,
        "FieldPath" text NOT NULL,
        "Description" text,
        "SearchFormId" smallint NOT NULL,
        CONSTRAINT "PK_SearchFormResultFields" PRIMARY KEY ("Id"),
        CONSTRAINT "FK_SearchFormResultFields_SearchForms_SearchFormId" FOREIGN KEY ("SearchFormId") REFERENCES "SERVICE_SESSIONS_CFG"."SearchForms" ("Id") ON DELETE CASCADE
    );
    COMMENT ON TABLE "SERVICE_SESSIONS_CFG"."SearchFormResultFields" IS 'Поле формы поиска для результата';
    COMMENT ON COLUMN "SERVICE_SESSIONS_CFG"."SearchFormResultFields"."Id" IS 'Уникальный идентификатор';
    COMMENT ON COLUMN "SERVICE_SESSIONS_CFG"."SearchFormResultFields"."FieldName" IS 'Название поля';
    COMMENT ON COLUMN "SERVICE_SESSIONS_CFG"."SearchFormResultFields"."FieldType" IS 'Тип данных';
    COMMENT ON COLUMN "SERVICE_SESSIONS_CFG"."SearchFormResultFields"."FieldPath" IS 'Путь до поля';
    COMMENT ON COLUMN "SERVICE_SESSIONS_CFG"."SearchFormResultFields"."Description" IS 'Описание/Подсказка для оператора';
    COMMENT ON COLUMN "SERVICE_SESSIONS_CFG"."SearchFormResultFields"."SearchFormId" IS 'Связь с формой поиска';
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "SERVICE_SESSIONS_CFG"."__ServiceSystemsConfigurationDbContext" WHERE "MigrationId" = '20250322094211_Init') THEN
    CREATE INDEX "IX_SearchFormRequestFields_SearchFormId" ON "SERVICE_SESSIONS_CFG"."SearchFormRequestFields" ("SearchFormId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "SERVICE_SESSIONS_CFG"."__ServiceSystemsConfigurationDbContext" WHERE "MigrationId" = '20250322094211_Init') THEN
    CREATE INDEX "IX_SearchFormResultFields_SearchFormId" ON "SERVICE_SESSIONS_CFG"."SearchFormResultFields" ("SearchFormId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "SERVICE_SESSIONS_CFG"."__ServiceSystemsConfigurationDbContext" WHERE "MigrationId" = '20250322094211_Init') THEN
    CREATE UNIQUE INDEX "IX_SearchForms_Code" ON "SERVICE_SESSIONS_CFG"."SearchForms" ("Code");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "SERVICE_SESSIONS_CFG"."__ServiceSystemsConfigurationDbContext" WHERE "MigrationId" = '20250322094211_Init') THEN
    CREATE INDEX "IX_SearchForms_ServiceObjectTypeId" ON "SERVICE_SESSIONS_CFG"."SearchForms" ("ServiceObjectTypeId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "SERVICE_SESSIONS_CFG"."__ServiceSystemsConfigurationDbContext" WHERE "MigrationId" = '20250322094211_Init') THEN
    CREATE INDEX "IX_ServiceObjectTypeResultFields_ServiceObjectTypeId" ON "SERVICE_SESSIONS_CFG"."ServiceObjectTypeResultFields" ("ServiceObjectTypeId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "SERVICE_SESSIONS_CFG"."__ServiceSystemsConfigurationDbContext" WHERE "MigrationId" = '20250322094211_Init') THEN
    CREATE UNIQUE INDEX "IX_ServiceObjectTypes_Code" ON "SERVICE_SESSIONS_CFG"."ServiceObjectTypes" ("Code");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "SERVICE_SESSIONS_CFG"."__ServiceSystemsConfigurationDbContext" WHERE "MigrationId" = '20250322094211_Init') THEN
    CREATE INDEX "IX_ServiceObjectTypes_ServiceSystemId" ON "SERVICE_SESSIONS_CFG"."ServiceObjectTypes" ("ServiceSystemId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "SERVICE_SESSIONS_CFG"."__ServiceSystemsConfigurationDbContext" WHERE "MigrationId" = '20250322094211_Init') THEN
    CREATE UNIQUE INDEX "IX_ServiceSystems_Code" ON "SERVICE_SESSIONS_CFG"."ServiceSystems" ("Code");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "SERVICE_SESSIONS_CFG"."__ServiceSystemsConfigurationDbContext" WHERE "MigrationId" = '20250322094211_Init') THEN
    INSERT INTO "SERVICE_SESSIONS_CFG"."__ServiceSystemsConfigurationDbContext" ("MigrationId", "ProductVersion")
    VALUES ('20250322094211_Init', '9.0.1');
    END IF;
END $EF$;



