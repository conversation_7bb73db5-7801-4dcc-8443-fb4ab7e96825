DO $$
    DECLARE
        jid integer;
        scid integer;
    BEGIN
        -- Creating a new job
        INSERT INTO pgagent.pga_job(
            jobjclid, jobname, jobdesc, jobhostagent, jobenabled
        ) VALUES (
                     1::integer, 'J_LOAD_REQVIEW_NEW'::text, ''::text, ''::text, true
                 ) RETURNING jobid INTO jid;

        -- Steps
-- Inserting a step (jobid: NULL)
        INSERT INTO pgagent.pga_jobstep (
            jstjobid, jstname, jstenabled, jstkind,
            jstconnstr, jstdbname, jstonerror,
            jstcode, jstdesc
        ) VALUES (
                     jid, 'step1'::text, true, 's'::character(1),
                     ''::text, '{transform:DbName}'::name, 'f'::character(1),
                     'do
                     ' ||
                     '$' || '$' || '
BEGIN
    perform pg_terminate_backend(pid) from pg_stat_activity 
      where query like ''%CALL "CALC"."P_LOAD_REQVIEW"(true)%''
            and pid <> pg_backend_pid();
	WHILE 1=1
	LOOP	
		CALL "CALC"."P_LOAD_REQVIEW"(true);
		perform pg_sleep(5);		
		commit;
	END LOOP;
END;' || '
$' || '$'::text, ''::text
                 ) ;

        -- Schedules
-- Inserting a schedule
        INSERT INTO pgagent.pga_schedule(
            jscjobid, jscname, jscdesc, jscenabled,
            jscstart, jscend,    jscminutes, jschours, jscweekdays, jscmonthdays, jscmonths
        ) VALUES (
                     jid, 'OnceMinute'::text, ''::text, true,
                     '2020-01-01 00:00:00+03'::timestamp with time zone, '3000-01-01 00:00:00+03'::timestamp with time zone,
                     -- Minutes
                     ARRAY[true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true]::boolean[],
                     -- Hours
                     ARRAY[false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false]::boolean[],
                     -- Week days
                     ARRAY[false,false,false,false,false,false,false]::boolean[],
                     -- Month days
                     ARRAY[false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false]::boolean[],
                     -- Months
                     ARRAY[false,false,false,false,false,false,false,false,false,false,false,false]::boolean[]
                 ) RETURNING jscid INTO scid;
    END
$$;

DO $$
    DECLARE
        jid integer;
        scid integer;
    BEGIN
        -- Creating a new job
        INSERT INTO pgagent.pga_job(
            jobjclid, jobname, jobdesc, jobhostagent, jobenabled
        ) VALUES (
                     1::integer, 'J_LOAD_REQVIEW_EXISTS'::text, ''::text, ''::text, true
                 ) RETURNING jobid INTO jid;

        -- Steps
-- Inserting a step (jobid: NULL)
        INSERT INTO pgagent.pga_jobstep (
            jstjobid, jstname, jstenabled, jstkind,
            jstconnstr, jstdbname, jstonerror,
            jstcode, jstdesc
        ) VALUES (
                     jid, 'step1'::text, true, 's'::character(1),
                     ''::text, '{transform:DbName}'::name, 'f'::character(1),
                     'do
                      ' ||
                     '$' || '$' || '
BEGIN
    perform pg_terminate_backend(pid) from pg_stat_activity 
      where query like ''%CALL "CALC"."P_LOAD_REQVIEW"(false)%''
            and pid <> pg_backend_pid();
	WHILE 1=1
	LOOP	
		CALL "CALC"."P_LOAD_REQVIEW"(false);
		perform pg_sleep(5);		
		commit;
	END LOOP;
END;' || '
$' || '$'::text, ''::text
                 ) ;

        -- Schedules
-- Inserting a schedule
        INSERT INTO pgagent.pga_schedule(
            jscjobid, jscname, jscdesc, jscenabled,
            jscstart, jscend,    jscminutes, jschours, jscweekdays, jscmonthdays, jscmonths
        ) VALUES (
                     jid, 'OnceMinute'::text, ''::text, true,
                     '2020-01-01 00:00:00+03'::timestamp with time zone, '3000-01-01 00:00:00+03'::timestamp with time zone,
                     -- Minutes
                     ARRAY[true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true]::boolean[],
                     -- Hours
                     ARRAY[false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false]::boolean[],
                     -- Week days
                     ARRAY[false,false,false,false,false,false,false]::boolean[],
                     -- Month days
                     ARRAY[false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false]::boolean[],
                     -- Months
                     ARRAY[false,false,false,false,false,false,false,false,false,false,false,false]::boolean[]
                 ) RETURNING jscid INTO scid;
    END
$$;

DO $$
    DECLARE
        jid integer;
        scid integer;
    BEGIN
        -- Creating a new job
        INSERT INTO pgagent.pga_job(
            jobjclid, jobname, jobdesc, jobhostagent, jobenabled
        ) VALUES (
                     1::integer, 'J_CLEAR_LOG'::text, ''::text, ''::text, true
                 ) RETURNING jobid INTO jid;

        -- Steps
-- Inserting a step (jobid: NULL)
        INSERT INTO pgagent.pga_jobstep (
            jstjobid, jstname, jstenabled, jstkind,
            jstconnstr, jstdbname, jstonerror,
            jstcode, jstdesc
        ) VALUES (
                     jid, 'step1'::text, true, 's'::character(1),
                     ''::text, '{transform:DbName}'::name, 'f'::character(1),
                     'do
                     ' ||
                     '$' || '$' || '
begin
CALL "CALC"."P_CLEAR_LOG"();
end;' || '
$' || '$'::text, ''::text
                 ) ;

        -- Schedules
-- Inserting a schedule
        INSERT INTO pgagent.pga_schedule(
            jscjobid, jscname, jscdesc, jscenabled,
            jscstart, jscend,    jscminutes, jschours, jscweekdays, jscmonthdays, jscmonths
        ) VALUES (
                     jid, 'OnceMinute'::text, ''::text, true,
                     '2020-01-01 00:00:00+03'::timestamp with time zone, '3000-01-01 00:00:00+03'::timestamp with time zone,
                     -- Minutes
                     ARRAY[true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true]::boolean[],
                     -- Hours
                     ARRAY[false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false]::boolean[],
                     -- Week days
                     ARRAY[false,false,false,false,false,false,false]::boolean[],
                     -- Month days
                     ARRAY[false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false]::boolean[],
                     -- Months
                     ARRAY[false,false,false,false,false,false,false,false,false,false,false,false]::boolean[]
                 ) RETURNING jscid INTO scid;
    END
$$;