DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "CALC"."__CalcContextMigrations" WHERE "MigrationId" = '20241105123603_fix_operatorKpiV2') THEN
    create or replace function "CALC"."F_OPERATORS_KPI_INFO_V2"(
        _startdate timestamp with time zone DEFAULT (
            timezone(
                'UTC' :: text,
                (CURRENT_DATE) :: timestamp with time zone
            )
        ) :: timestamp with time zone,
        _enddate timestamp with time zone DEFAULT (
            timezone(
                'UTC' :: text,
                ((CURRENT_DATE + '1 day' :: interval)) :: timestamp with time zone
            )
        ) :: timestamp with time zone
    ) returns TABLE(
        "OperatorId" uuid,
        "FIO" text,
        "LOGIN" character varying,
        "ReceivedInWork" bigint,
        "CLOSED_REQUESTS" bigint,
        "NotResolvedRequests" bigint,
        "RequestSessions" bigint,
        "IN_WORK" bigint,
        "PROCESSED" bigint,
        "WAITING" bigint,
        "QUEUES_LIST" text,
        "CURRENT_STATUS" text,
        "SECONDS_IN_CURRENT_STATUS" numeric,
        "START_TIME" timestamp without time zone,
        "PAUSE_DURATION" bigint,
        "ONLINE_DURATION" bigint,
        "ONLINE_DURATION_TEXT" character varying,
        "IN_WORK_DURATION" bigint,
        "IN_LINE_DURATION" bigint,
        "UTILIZATION" numeric,
        "OCCUPANCY" numeric,
        "REDIRECTED_REQUESTS" bigint,
        "POSTPONED_REQUESTS" bigint,
        "DROPPED_REQUESTS" bigint,
        "ASA" numeric,
        "AHT" numeric,
        "ACW" numeric,
        "ACSI" numeric,
        "ASSI" numeric,
        "ART" numeric,
        "A1RT" numeric,
        "SALES_PERCENT" character varying,
        "NOW" timestamp with time zone,
        "HT_SUM" numeric,
        "CW_SUM" numeric,
        "CSI_SUM" numeric,
        "SSI_SUM" numeric,
        "RT_SUM" numeric,
        "1RT_SUM" numeric,
        "HT_COUNT" bigint,
        "CW_COUNT" bigint,
        "CSI_COUNT" bigint,
        "SSI_COUNT" bigint,
        "RT_COUNT" bigint,
        "1RT_COUNT" bigint,
        "CALLS_RECEIVED_COUNT" bigint,
        "CALLS_ACCEPTED_COUNT" bigint,
        "CALLS_MISSED_COUNT" bigint,
        "CONVERSATION_TIME" bigint,
        "WAIT_TIME_DURATION" bigint,
        "CALLS_OUTBOUND_COUNT" bigint,
        "AP" numeric,
        "AP_WEIGHTED_SCORE_SUM" numeric,
        "AP_TOTAL_SCORES_COUNT" bigint
    ) stable language sql
    as
    $function$

    WITH v_operator_status_queue AS (
        SELECT
            vosq."OperatorId",
            vosq."QUEUES_LIST",
            vosq."STATUS",
            vosq."SECONDS_IN_STATUS"
        FROM
            "CALC"."V_OPERATOR_STATUS_QUEUES" vosq
    ),

         work_session AS (
             SELECT
                 initial_statuses."OperatorId",
                 min(initial_statuses."StartDate") AS "START_TIME"
             FROM
                 "KPI"."OPERATOR_STATUSES" initial_statuses
             WHERE
                 initial_statuses."IsInitial" = true
               AND initial_statuses."StartDate" BETWEEN _startDate AND _endDate
             GROUP BY
                 initial_statuses."OperatorId"
         ),

         oper_status_statistic AS (
             SELECT
                 s."OperatorId",
                 SUM(
                         CASE
                             WHEN s."Status" <> 'InWork'::TEXT
                                 AND s."Status" <> 'Ready'::TEXT
                                 AND s."Status" <> 'Offline'::TEXT
                                 AND s."Status" <> 'Initial'::TEXT THEN s."StatusDuration"
                             ELSE 0
                             END
                 ) AS "PAUSE_DURATION",
                 SUM(
                         CASE
                             WHEN (
                                 s."Status" = 'InWork'::TEXT
                                     OR s."Status" = 'Ready'::TEXT
                                 ) THEN s."StatusDuration"
                             ELSE 0
                             END
                 ) AS "ONLINE_DURATION",
                 SUM(
                         CASE
                             WHEN s."Status" = 'InWork'::TEXT THEN s."StatusDuration"
                             ELSE 0
                             END
                 ) AS "IN_WORK_DURATION",
                 SUM(
                         CASE
                             WHEN s."Status" = 'InWork'::TEXT
                                 OR s."Status" = 'Ready'::TEXT THEN s."StatusDuration"
                             ELSE 0
                             END
                 ) AS "IN_LINE_DURATION",
                 COALESCE(
                         SUM(
                                 CASE
                                     WHEN s."Status" = 'InWork'::TEXT
                                         OR s."Status" = 'Ready'::TEXT THEN s."StatusDuration"
                                     ELSE 0
                                     END
                         )::DOUBLE PRECISION / NULLIF(
                                 SUM(
                                         CASE
                                             WHEN s."Status" <> 'Offline'::TEXT
                                                 AND s."Status" <> 'Initial'::TEXT THEN s."StatusDuration"
                                             ELSE 0
                                             END
                                 )::DOUBLE PRECISION,
                                 0::DOUBLE PRECISION
                                               ) * 100::DOUBLE PRECISION,
                         0::DOUBLE PRECISION
                 )::NUMERIC(10, 2) AS "UTILIZATION",
                 COALESCE(
                         SUM(
                                 CASE
                                     WHEN s."Status" = 'InWork'::TEXT THEN s."StatusDuration"
                                     ELSE 0
                                     END
                         )::DOUBLE PRECISION / NULLIF(
                                 SUM(
                                         CASE
                                             WHEN s."Status" = 'InWork'::TEXT
                                                 OR s."Status" = 'Ready'::TEXT THEN s."StatusDuration"
                                             ELSE 0
                                             END
                                 )::DOUBLE PRECISION,
                                 0::DOUBLE PRECISION
                                               ) * 100::DOUBLE PRECISION,
                         0::DOUBLE PRECISION
                 )::NUMERIC(10, 2) AS "OCCUPANCY"
             FROM
                 "KPI"."OPERATOR_STATUSES" s
             WHERE
                 s."StartDate" BETWEEN _startDate AND _endDate
             GROUP BY
                 s."OperatorId"
         ),

         requests_info AS (
             SELECT
                 o_kpi."OperatorId",
                 AVG(
                         CASE
                             WHEN (
                                 o_kpi."ContactStartTime" BETWEEN _startDate AND _endDate
                                 ) THEN o_kpi."HT"
                             ELSE NULL::BIGINT
                             END
                 ) AS "AHT",
                 SUM(
                         CASE
                             WHEN (
                                 o_kpi."ContactStartTime" BETWEEN _startDate AND _endDate
                                 ) THEN o_kpi."HT"
                             ELSE NULL::BIGINT
                             END
                 ) AS "HT_SUM",
                 COUNT(
                         CASE
                             WHEN (
                                      o_kpi."ContactStartTime" BETWEEN _startDate AND _endDate
                                      )
                                 AND (
                                      o_kpi."HT" IS NULL
                                          OR o_kpi."HT" = 0
                                      ) THEN NULL
                             ELSE o_kpi."OperatorId"
                             END
                 ) AS "HT_COUNT"
             FROM
                 "KPI"."OPERATOR_KPI" o_kpi
             WHERE
                 o_kpi."ContactEndTime" BETWEEN _startDate AND _endDate
             GROUP BY
                 o_kpi."OperatorId"
         ),

         requests_call_info AS (
             SELECT
                 call_state."OperatorId" AS "OperatorId",
                 COUNT(
                         CASE
                             WHEN (
                                 (call_state."Direction" IS NULL OR call_state."Direction" = 0)
                                     AND
                                 (
                                     call_state."TimeCallAccepted" IS NOT NULL
                                         OR call_state."TimeCallMissed" IS NOT NULL
                                     )
                                 ) THEN call_state."Id"
                             ELSE NULL
                             END
                 ) AS "CALLS_RECEIVED_COUNT",
                 COUNT(
                         CASE
                             WHEN (
                                 (call_state."Direction" IS NULL OR call_state."Direction" = 0)
                                     AND
                                 (
                                     call_state."TimeCallAccepted" IS NOT NULL
                                     )
                                 ) THEN call_state."Id"
                             ELSE NULL
                             END
                 ) AS "CALLS_ACCEPTED_COUNT",
                 COUNT(
                         CASE
                             WHEN (
                                 (call_state."Direction" IS NULL OR call_state."Direction" = 0)
                                     AND
                                 (
                                     call_state."TimeCallMissed" IS NOT NULL
                                     )
                                 ) THEN call_state."Id"
                             ELSE NULL
                             END
                 ) AS "CALLS_MISSED_COUNT",
                 COALESCE(SUM(call_state."Duration"), 0) AS "CONVERSATION_TIME",
                 AVG(call_state."ACW") AS "ACW",
                 SUM(call_state."ACW") AS "CW_SUM",
                 COUNT(call_state."ACW") AS "CW_COUNT",
                 COUNT(
                         CASE
                             WHEN call_state."Direction" = 1 THEN call_state."Id"
                             ELSE NULL
                             END
                 ) AS "CALLS_OUTBOUND_COUNT"
             FROM
                 "KPI"."OPERATOR_CALL_STATE" call_state
             WHERE
                 (call_state."TimeCallAccepted" BETWEEN _startDate AND _endDate)
                OR (call_state."TimeCallMissed" BETWEEN _startDate AND _endDate)
             GROUP BY
                 call_state."OperatorId"
         ),

         redirected_postponed_info AS (
             SELECT
                 "REQUEST_EVENT"."OperatorId",
                 count(
                         CASE
                             WHEN "REQUEST_EVENT"."IsRedirected" = true
                                 THEN "REQUEST_EVENT"."RequestId"
                             ELSE NULL::BIGINT
                             END
                 ) AS "REDIRECTED_COUNT",
                 count(
                         CASE
                             WHEN "REQUEST_EVENT"."IsPostponed" = true
                                 THEN "REQUEST_EVENT"."RequestId"
                             ELSE NULL::BIGINT
                             END
                 ) AS "POSTPONED_COUNT",
                 count(
                         CASE
                             WHEN "REQUEST_EVENT"."IsDropped" = true
                                 THEN "REQUEST_EVENT"."RequestId"
                             ELSE NULL::BIGINT
                             END
                 ) AS "DROPPED_COUNT"
             FROM
                 "KPI"."REQUEST_EVENT"
             WHERE
                 "REQUEST_EVENT"."EventTime" BETWEEN _startDate AND _endDate
             GROUP BY
                 "REQUEST_EVENT"."OperatorId"
         ),

         first_reaction_dates AS (
             SELECT
                 "OPERATOR_RESPONSE"."RequestId",
                 min("OPERATOR_RESPONSE"."ReactionStartDate") AS "FirstReactionStartDate"
             FROM
                 "KPI"."OPERATOR_RESPONSE"
             WHERE
                 "OPERATOR_RESPONSE"."ReactionStartDate" BETWEEN _startDate AND _endDate
             GROUP BY
                 "OPERATOR_RESPONSE"."RequestId"
         ),

         first_rt AS (
             SELECT
                 all_rt."OperatorId",
                 AVG(all_rt."RT") AS "A1RT",
                 SUM(all_rt."RT") AS "1RT_SUM",
                 count(
                         CASE
                             WHEN all_rt."RT" IS NULL
                                 OR all_rt."RT" = 0 THEN NULL
                             ELSE all_rt."OperatorId"
                             END
                 ) AS "1RT_COUNT"
             FROM
                 "KPI"."OPERATOR_RESPONSE" all_rt
                     RIGHT JOIN first_reaction_dates ON
                     first_reaction_dates."RequestId" = all_rt."RequestId"
                         AND first_reaction_dates."FirstReactionStartDate" = all_rt."ReactionStartDate"
             GROUP BY
                 all_rt."OperatorId"
         ),

         operator_response_all AS (
             SELECT
                 all_rt."OperatorId",
                 AVG(all_rt."RT") AS "ART",
                 SUM(all_rt."RT") AS "RT_SUM",
                 count(
                         CASE
                             WHEN all_rt."RT" IS NULL
                                 OR all_rt."RT" = 0 THEN NULL
                             ELSE all_rt."OperatorId"
                             END
                 ) AS "RT_COUNT"
             FROM
                 "KPI"."OPERATOR_RESPONSE" all_rt
             WHERE
                 all_rt."ReactionStartDate" BETWEEN _startDate AND _endDate
             GROUP BY
                 all_rt."OperatorId"
         ),

         operator_voice_response AS (
             SELECT
                 all_rt."OperatorId",
                 AVG(all_rt."RT") AS "VOICE_ART",
                 SUM(all_rt."RT") AS "VOICE_RT_SUM",
                 count(
                         CASE
                             WHEN all_rt."RT" IS NULL
                                 OR all_rt."RT" = 0 THEN NULL
                             ELSE all_rt."OperatorId"
                             END
                 ) AS "VOICE_RT_COUNT"
             FROM
                 "KPI"."OPERATOR_RESPONSE" all_rt
             WHERE
                 all_rt."ChannelType" = 10
               AND all_rt."ReactionStartDate" BETWEEN _startDate AND _endDate
             GROUP BY
                 all_rt."OperatorId"
         ),

         operator_wait_custom AS (
             SELECT
                 "OPERATOR_ID" AS "OperatorId",
                 extract(
                         epoch
                         from ((now() at time zone 'utc') - "DATETIME_VALUE")
                 )::BIGINT as wait_time_duration
             FROM
                 "AWP_INFRA"."OPERATOR_CUSTOM_ATTRIBUTES"
             WHERE
                 "CODE" = 'LastWorkTime'
         ),

         csi_info_evaluation_pre AS ( -- Присваиваем ранг каждой записи на базе CreateDate, чтобы выбрать последнюю оценку (rank = 1)
             SELECT
                 eval."OperatorId",
                 eval."Score",
                 RANK() OVER (PARTITION BY eval."RequestGuidId", eval."OperatorId" ORDER BY eval."CreateDate" DESC) AS "Rank"
             FROM
                 "CALC"."V_EXTERNAL_EVALUATIONS" AS eval
                     LEFT JOIN "KPI"."REQUEST_KPI" r_kpi ON r_kpi."Id" = eval."RequestId"
             WHERE
                 eval."OperatorId" IS NOT NULL
               AND r_kpi."TimeRegistered" BETWEEN _startDate AND _endDate
         ),

         csi_info_evaluation_result AS ( -- Получаем последнюю оценку по обращению
             SELECT
                 eval."OperatorId",
                 AVG(eval."Score") AS "ACSI",
                 SUM(eval."Score") AS "CSI_SUM",
                 COUNT(eval."Score") AS "CSI_COUNT"
             FROM csi_info_evaluation_pre eval
             WHERE "Rank" = 1
             GROUP BY eval."OperatorId"
         ),

         pre_sessions_req AS ( -- Получаем список событий по обработке обращений (неуникальный) с которыми работал оператор в заданный промежуток
             SELECT
                 "Id",
                 "OperatorId",
                 "RequestId",
                 "ContactStartTime",
                 "ContactEndTime"
             FROM
                 "KPI"."OPERATOR_KPI"
             WHERE
                 "ContactStartTime" BETWEEN _startDate AND _endDate
         ),

         pre_received_req AS ( -- Получаем только уникальные обращения из pre_sessions_req
             SELECT
                 sub."Id",
                 sub."TimeClosed",
                 sub."Status",
                 sub."CSI",
                 sub."SSI",
                 sub."ClosedById",
                 sub."RequestId"
             FROM (
                 SELECT
                     psr."Id",
                     r."TimeClosed",
                     r."Status",
                     r_kpi."CSI",
                     r_kpi."SSI",
                     r_kpi."ClosedById",
                     r."Id" AS "RequestId",
                     ROW_NUMBER() OVER (PARTITION BY psr."RequestId" ORDER BY psr."Id" DESC) AS rn
                 FROM
                     pre_sessions_req AS psr
                     LEFT JOIN "CRPM_DATA"."Requests" AS r ON psr."RequestId" = r."Id"
                     LEFT JOIN "KPI"."REQUEST_KPI" AS r_kpi ON psr."RequestId" = r_kpi."Id"
             ) sub
             WHERE
                 sub.rn = 1
         ),

         req_sessions_and_received_res AS ( -- Вычисляем Поступило в работу с актуализацией + Кол-во сессий
             SELECT
                 r."OperatorId",
                 COUNT(riw."Id") AS "ReceivedInWork",
                 COUNT(riw."Id") FILTER(WHERE riw."TimeClosed" IS NOT NULL) AS "ResolvedRequests",
                 COUNT(riw."Id") FILTER(WHERE riw."TimeClosed" IS NULL) AS "NotResolvedRequests",
                 COUNT(r."RequestId") AS "RequestSessions",
                 COUNT(r."RequestId") FILTER(WHERE r."ContactEndTime" IS NOT NULL) AS "ProcessedRequests",
                 COUNT(r."RequestId") FILTER(WHERE r."ContactEndTime" IS NULL) AS "InWorkRequests",
                 COUNT(r."RequestId") FILTER (
                     WHERE (
                               (
                                   (
                                       SELECT rss."Code"
                                       FROM "CRPM_CFG"."RequestStateStatuses" rss
                                       WHERE rss."Id" = riw."Status"
                                   )
                               )::text
                               ) = ANY (
                               ARRAY [
                                   'OPERATOR.POSTPONE'::character varying::text,
                                   'DIVISION.WAIT'::character varying::text,
                                   'OPERATOR.WAIT.CLIENT'::character varying::text,
                                   'SUPERVISOR'::character varying::text,
                                   'CLAIM.OPERATOR.WAIT'::character varying::text]
                               )
                     ) AS "WaitingRequests"
             FROM
                 pre_sessions_req AS r
                     LEFT JOIN pre_received_req AS riw ON r."Id" = riw."Id"
             GROUP BY
                 r."OperatorId"
         ),

         requests_ssi_info_from_rkpi AS (
             SELECT
                 riw."ClosedById" AS "OperatorId",
                 riw."RequestId",
                 riw."SSI"
             FROM pre_received_req AS riw
             WHERE
                 riw."ClosedById" IS NOT NULL
         ),

         operator_ssi_info_from_eval AS (
             SELECT
                 e."RunForOperatorId" AS "OperatorId",
                 e."ScoreResult" AS "SSI",
                 (e."Context"->>'requestId')::bigint AS "RequestId"
             FROM "CALC"."V_EVALUATIONS" AS e
             WHERE e."PublishedAt" BETWEEN _startDate AND _endDate
         ),

         pre_ssi_info_result AS (
             SELECT
                 r."OperatorId",
                 COALESCE(eval."SSI", r."SSI") AS "SSI"
             FROM requests_ssi_info_from_rkpi AS r
                      LEFT JOIN operator_ssi_info_from_eval AS eval ON r."RequestId" = eval."RequestId"
         ),

         ssi_info_result AS (
             SELECT
                 "OperatorId",
                 AVG("SSI") AS "ASSI",
                 SUM("SSI") AS "SSI_SUM",
                 COUNT("SSI") AS "SSI_COUNT"
             FROM pre_ssi_info_result
             GROUP BY "OperatorId"
         ),

        agent_performance_req AS (
            SELECT
                eval."OperatorId",
                SUM(
                    CASE 
                        WHEN eval."Score" = 1 THEN 0
                        WHEN eval."Score" = 2 THEN 0.25
                        WHEN eval."Score" = 3 THEN 0.50
                        WHEN eval."Score" = 4 THEN 0.75
                        WHEN eval."Score" = 5 THEN 1.00
                        ELSE 0
                    END
                ) AS weighted_score_sum,
                COUNT(eval."Score") AS total_scores
            FROM
                "CALC"."V_EXTERNAL_EVALUATIONS" eval
            WHERE
                eval."OperatorId" IS NOT NULL
                AND eval."CreateDate" BETWEEN _startDate AND _endDate
            GROUP BY
                eval."OperatorId"
        ),

        ap_calc AS (
            SELECT
                ap."OperatorId",
                CASE 
                    WHEN ap.total_scores = 0 THEN 0
                    ELSE (ap.weighted_score_sum / ap.total_scores) * 100
                END AS "AP"
            FROM
                agent_performance_req ap
        ),

         res AS (
             SELECT
                 operators."ID" AS "OperatorId",
                 operators."FIO",
                 operators."LOGIN",
                 COALESCE(rsars."ReceivedInWork", 0)::BIGINT AS "ReceivedInWork", -- "Поступило в работу"
                 COALESCE(rsars."ResolvedRequests", 0)::BIGINT AS "CLOSED_REQUESTS", -- "Поступило в работу (решено/закрыто)"
                 COALESCE(rsars."NotResolvedRequests", 0)::BIGINT AS "NotResolvedRequests", -- "Поступило в работу (не решено)"
                 COALESCE(rsars."RequestSessions", 0)::BIGINT AS "RequestSessions", -- "Кол-во сессий"
                 COALESCE(rsars."InWorkRequests", 0)::BIGINT AS "IN_WORK", -- "В работе"
                 COALESCE(rsars."ProcessedRequests", 0)::BIGINT AS "PROCESSED", -- "Обработано"
                 COALESCE(rsars."WaitingRequests", 0)::BIGINT AS "WAITING", -- "Ожидает ответа от оператора"
                 v_operator_status_queue."QUEUES_LIST",
                 v_operator_status_queue."STATUS" AS "CURRENT_STATUS",
                 COALESCE(v_operator_status_queue."SECONDS_IN_STATUS", 0::NUMERIC(10, 2)::DOUBLE PRECISION)::NUMERIC(10, 2) AS "SECONDS_IN_CURRENT_STATUS",
                 work_session."START_TIME" AS "START_TIME",
                 COALESCE(oper_status_statistic."PAUSE_DURATION", 0::BIGINT) AS "PAUSE_DURATION",
                 COALESCE(oper_status_statistic."ONLINE_DURATION", 0::BIGINT) AS "ONLINE_DURATION",

                 to_char(
                         make_interval(
                                 secs => COALESCE(
                                         oper_status_statistic."ONLINE_DURATION",
                                         0::BIGINT
                                         )
                         ),
                         'HH24:MI:SS'
                 ) AS "ONLINE_DURATION_TEXT",

                 COALESCE(oper_status_statistic."IN_WORK_DURATION", 0::BIGINT) AS "IN_WORK_DURATION",
                 COALESCE(oper_status_statistic."IN_LINE_DURATION", 0::BIGINT) AS "IN_LINE_DURATION",
                 COALESCE(oper_status_statistic."UTILIZATION", 0::NUMERIC) AS "UTILIZATION",
                 COALESCE(oper_status_statistic."OCCUPANCY", 0::NUMERIC) AS "OCCUPANCY",
                 COALESCE(redirected_postponed_info."REDIRECTED_COUNT", 0::BIGINT) AS "REDIRECTED_REQUESTS",
                 COALESCE(redirected_postponed_info."POSTPONED_COUNT", 0::BIGINT) AS "POSTPONED_REQUESTS",
                 COALESCE(redirected_postponed_info."DROPPED_COUNT", 0::BIGINT) AS "DROPPED_REQUESTS",
                 0::NUMERIC(10, 2) AS "ASA",
                 COALESCE(requests_info."AHT", 0::NUMERIC)::NUMERIC(10, 2) AS "AHT",
                 COALESCE(requests_call_info."ACW", 0::NUMERIC)::NUMERIC(10, 2) AS "ACW",
                 COALESCE(cier."ACSI", 0::NUMERIC)::NUMERIC(10, 2) AS "ACSI",
                 COALESCE(sir."ASSI", 0::NUMERIC)::NUMERIC(10, 2) AS "ASSI",
                 COALESCE(operator_response_all."ART"::NUMERIC(15, 2), 0::NUMERIC) AS "ART",
                 COALESCE(first_rt."A1RT"::NUMERIC(15, 2), 0::NUMERIC) AS "A1RT",
                 '-' AS "SALES_PERCENT",
                 timezone('UTC'::TEXT, now()::TIMESTAMP WITH TIME zone)::TIMESTAMP WITH TIME zone AS "NOW",
                 COALESCE(requests_info."HT_SUM", 0::NUMERIC)::NUMERIC(10, 2) AS "HT_SUM",
                 COALESCE(requests_call_info."CW_SUM", 0::NUMERIC)::NUMERIC(10, 2) AS "CW_SUM",
                 COALESCE(cier."CSI_SUM", 0::NUMERIC)::NUMERIC(10, 2) AS "CSI_SUM",
                 COALESCE(sir."SSI_SUM", 0::NUMERIC)::NUMERIC(10, 2) AS "SSI_SUM",
                 COALESCE(operator_response_all."RT_SUM", 0::NUMERIC)::NUMERIC(10, 2) AS "RT_SUM",
                 COALESCE(first_rt."1RT_SUM", 0::NUMERIC)::NUMERIC(10, 2) AS "1RT_SUM",
                 COALESCE(requests_info."HT_COUNT"::BIGINT, 0::BIGINT) AS "HT_COUNT",
                 COALESCE(requests_call_info."CW_COUNT"::BIGINT, 0::BIGINT) AS "CW_COUNT",
                 COALESCE(cier."CSI_COUNT"::BIGINT, 0::BIGINT) AS "CSI_COUNT",
                 COALESCE(sir."SSI_COUNT"::BIGINT, 0::BIGINT) AS "SSI_COUNT",
                 COALESCE(operator_response_all."RT_COUNT"::BIGINT, 0::BIGINT) AS "RT_COUNT",
                 COALESCE(first_rt."1RT_COUNT"::BIGINT, 0::BIGINT) AS "1RT_COUNT",
                 COALESCE(requests_call_info."CALLS_RECEIVED_COUNT"::BIGINT, 0::BIGINT) AS "CALLS_RECEIVED_COUNT",
                 COALESCE(requests_call_info."CALLS_ACCEPTED_COUNT"::BIGINT, 0::BIGINT) AS "CALLS_ACCEPTED_COUNT",
                 COALESCE(requests_call_info."CALLS_MISSED_COUNT"::BIGINT, 0::BIGINT) AS "CALLS_MISSED_COUNT",
                 COALESCE(requests_call_info."CONVERSATION_TIME"::BIGINT, 0::BIGINT) AS "CONVERSATION_TIME",
                 COALESCE(operator_wait_custom.wait_time_duration, 0::BIGINT) AS "WAIT_TIME_DURATION",
                 COALESCE(requests_call_info."CALLS_OUTBOUND_COUNT"::BIGINT, 0::BIGINT) AS "CALLS_OUTBOUND_COUNT",
                 COALESCE(ap_calc."AP", 0)::NUMERIC(10, 2) AS "AP",
                 COALESCE(agent_performance_req."weighted_score_sum", 0)::NUMERIC(10, 2) AS "AP_WEIGHTED_SCORE_SUM",
                 COALESCE(agent_performance_req."total_scores", 0)::BIGINT AS "AP_TOTAL_SCORES_COUNT"
             FROM
                 "CALC"."INFRA_Operators_FIO" operators
                     LEFT JOIN v_operator_status_queue ON v_operator_status_queue."OperatorId" = operators."ID"
                     LEFT JOIN oper_status_statistic ON oper_status_statistic."OperatorId" = operators."ID"
                     LEFT JOIN work_session ON work_session."OperatorId" = operators."ID"
                     LEFT JOIN requests_info ON requests_info."OperatorId" = operators."ID"
                     LEFT JOIN requests_call_info ON requests_call_info."OperatorId" = operators."ID"
                     LEFT JOIN redirected_postponed_info ON redirected_postponed_info."OperatorId" = operators."ID"
                     LEFT JOIN operator_response_all ON operator_response_all."OperatorId" = operators."ID"
                     LEFT JOIN first_rt ON first_rt."OperatorId" = operators."ID"
                     LEFT JOIN operator_wait_custom ON operator_wait_custom."OperatorId" = operators."ID"
                     LEFT JOIN req_sessions_and_received_res AS rsars ON rsars."OperatorId" = operators."ID"
                     LEFT JOIN ssi_info_result AS sir ON sir."OperatorId" = operators."ID"
                     LEFT JOIN csi_info_evaluation_result cier ON cier."OperatorId" = operators."ID"
                     LEFT JOIN ap_calc ON ap_calc."OperatorId" = operators."ID"
                     LEFT JOIN agent_performance_req ON agent_performance_req."OperatorId" = operators."ID"
         )

    SELECT
        "OperatorId",
        "FIO",
        "LOGIN",
        "ReceivedInWork",
        "CLOSED_REQUESTS",
        "NotResolvedRequests",
        "RequestSessions",
        "IN_WORK",
        (res."PROCESSED" - (res."REDIRECTED_REQUESTS" + res."POSTPONED_REQUESTS" + res."DROPPED_REQUESTS")) AS "PROCESSED",
        "WAITING",
        "QUEUES_LIST",
        "CURRENT_STATUS",
        "SECONDS_IN_CURRENT_STATUS",
        "START_TIME",
        "PAUSE_DURATION",
        "ONLINE_DURATION",
        "ONLINE_DURATION_TEXT",
        "IN_WORK_DURATION",
        "IN_LINE_DURATION",
        "UTILIZATION",
        "OCCUPANCY",
        "REDIRECTED_REQUESTS",
        "POSTPONED_REQUESTS",
        "DROPPED_REQUESTS",
        "ASA",
        "AHT",
        "ACW",
        "ACSI",
        "ASSI",
        "ART",
        "A1RT",
        "SALES_PERCENT",
        "NOW",
        "HT_SUM",
        "CW_SUM",
        "CSI_SUM",
        "SSI_SUM",
        "RT_SUM",
        "1RT_SUM",
        "HT_COUNT",
        "CW_COUNT",
        "CSI_COUNT",
        "SSI_COUNT",
        "RT_COUNT",
        "1RT_COUNT",
        "CALLS_RECEIVED_COUNT", -- Поступило звонков (Входящих)
        "CALLS_ACCEPTED_COUNT", -- Принято звонков (Входящих)
        "CALLS_MISSED_COUNT", -- Пропущено звонков (Входящих)
        "CONVERSATION_TIME",
        "WAIT_TIME_DURATION",
        "CALLS_OUTBOUND_COUNT", -- Совершено звонков (Исходящих)
        "AP", -- Agent Performance
        "AP_WEIGHTED_SCORE_SUM", -- Сумма взвешенных оценок для каждого оператора (для расчёта ИТОГО по колонке agent performance)
        "AP_TOTAL_SCORES_COUNT" -- Общее количество оценок для каждого оператора (для расчёта ИТОГО по колонке agent performance)
    FROM
        res
    ORDER BY
        "OperatorId";

    $function$
    ;

    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "CALC"."__CalcContextMigrations" WHERE "MigrationId" = '20241105123603_fix_operatorKpiV2') THEN
    INSERT INTO "CALC"."__CalcContextMigrations" ("MigrationId", "ProductVersion")
    VALUES ('20241105123603_fix_operatorKpiV2', '8.0.2');
    END IF;
END $EF$;
