DO $EF$
DECLARE 
    v_work_id smallint;
    v_status_chatbot_id smallint;
    v_status_pvoo_id smallint;
    v_status_closed_id smallint;
BEGIN
    -- Получаем ID работы
    SELECT "Id" INTO v_work_id
    FROM "CRPM_CFG"."RequestStateWorks"
    WHERE "Title" = 'Обработка сценарным чат-ботом';

    -- Получаем ID статусов
    SELECT "Id" INTO v_status_chatbot_id
    FROM "CRPM_CFG"."RequestStateStatuses"
    WHERE "Title" = 'Обработка чат-ботом';

    SELECT "Id" INTO v_status_pvoo_id
    FROM "CRPM_CFG"."RequestStateStatuses"
    WHERE "Title" = 'Отправка ПВОО';

    SELECT "Id" INTO v_status_closed_id
    FROM "CRPM_CFG"."RequestStateStatuses"
    WHERE "Title" = 'Закрыто / Обработано Чат Ботом';

    -- Вставляем записи если они отсутствуют
    INSERT INTO "CRPM_CFG"."WorkSuccessCodes" ("WorkId", "Code", "TargetStatusId", "ForceStateChange")
    SELECT v_work_id, 'CHATBOT.TRANSFER', v_status_chatbot_id, false
    WHERE NOT EXISTS (
        SELECT 1 FROM "CRPM_CFG"."WorkSuccessCodes"
        WHERE "WorkId" = v_work_id AND "Code" = 'CHATBOT.TRANSFER'
    );

    INSERT INTO "CRPM_CFG"."WorkSuccessCodes" ("WorkId", "Code", "TargetStatusId", "ForceStateChange")
    SELECT v_work_id, 'CHATBOT.OPERATOR', v_status_pvoo_id, false
    WHERE NOT EXISTS (
        SELECT 1 FROM "CRPM_CFG"."WorkSuccessCodes"
        WHERE "WorkId" = v_work_id AND "Code" = 'CHATBOT.OPERATOR'
    );

    INSERT INTO "CRPM_CFG"."WorkSuccessCodes" ("WorkId", "Code", "TargetStatusId", "ForceStateChange")
    SELECT v_work_id, 'CHATBOT.CLOSE', v_status_closed_id, false
    WHERE NOT EXISTS (
        SELECT 1 FROM "CRPM_CFG"."WorkSuccessCodes"
        WHERE "WorkId" = v_work_id AND "Code" = 'CHATBOT.CLOSE'
    );

END $EF$;