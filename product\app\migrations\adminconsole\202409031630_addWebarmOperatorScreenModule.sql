INSERT INTO "AWP_INFRA"."Modules" (
  "Id", "Name", "Initialization", "Description", "IsOptional", "IsDisabled", "ClientTypes"
)
  SELECT uuid_generate_v4(),
  'OperatorScreenModule',
  '{
     "wsHost": "ws://localhost:8980",
     "saveBlobOptions": { "type": "video/webm; codecs=vp9" },
     "audio": true,
     "debugMode": false,
     "isDesktopAppRequired": false
  }',
  'Модуль взаимодействия вебарм с desktop-приложением, реализующим доступ к экранам рабочей станции',
  true,
  false,
  2
WHERE
  NOT EXISTS (
    SELECT "Id" FROM "AWP_INFRA"."Modules" WHERE "Name"='OperatorScreenModule'
  );

-- Добавление прав на модуль
INSERT INTO "AWP_INFRA"."ModuleUserRole" (
	"ModuleId", "UserRoleId"
)
  SELECT
    (SELECT "Id" FROM "AWP_INFRA"."Modules" WHERE "Name"='OperatorScreenModule') as "ModuleId",
    u."Id" as "UserRoleId"
  FROM "AWP_INFRA"."UserRoles" u
  WHERE
    NOT EXISTS (
      SELECT "ModuleId" FROM "AWP_INFRA"."ModuleUserRole" WHERE "ModuleId"=(SELECT "Id" FROM "AWP_INFRA"."Modules" WHERE "Name"='OperatorScreenModule')
    );

