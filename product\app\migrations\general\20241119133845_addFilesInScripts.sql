DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "SCRIPTS"."__EFMigrationsHistory" WHERE "MigrationId" = '20241119133845_addFilesInScripts') THEN
    CREATE TABLE "SCRIPTS"."AutomationServiceFiles" (
    "Id" bigint GENERATED BY DEFAULT AS IDENTITY,
    "ScriptVariableId" bigint,
    "ScriptAutomationServiceId" bigint,
    "FileName" text NOT NULL,
    CONSTRAINT "PK_AutomationServiceFiles" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_AutomationServiceFiles_ScriptSteps_ScriptAutomationServiceId" FOREIGN KEY ("ScriptAutomationServiceId") REFERENCES "SCRIPTS"."ScriptSteps" ("Id"),
    CONSTRAINT "FK_AutomationServiceFiles_ScriptVariables_ScriptVariableId" FOREIGN KEY ("ScriptVariableId") REFERENCES "SCRIPTS"."ScriptVariables" ("Id")
    );

    CREATE TABLE "SCRIPTS"."VariableAttachmentScriptStep" (
    "StepId" bigint NOT NULL,
    "ScriptVariableId" bigint NOT NULL,
    CONSTRAINT "PK_VariableAttachmentScriptStep" PRIMARY KEY ("ScriptVariableId", "StepId"),
    CONSTRAINT "FK_VariableAttachmentScriptStep_ScriptSteps_StepId" FOREIGN KEY ("StepId") REFERENCES "SCRIPTS"."ScriptSteps" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_VariableAttachmentScriptStep_ScriptVariables_ScriptVariable~" FOREIGN KEY ("ScriptVariableId") REFERENCES "SCRIPTS"."ScriptVariables" ("Id") ON DELETE CASCADE
    );

    CREATE INDEX "IX_AutomationServiceFiles_ScriptAutomationServiceId" ON "SCRIPTS"."AutomationServiceFiles" ("ScriptAutomationServiceId");

    CREATE INDEX "IX_AutomationServiceFiles_ScriptVariableId" ON "SCRIPTS"."AutomationServiceFiles" ("ScriptVariableId");

    CREATE INDEX "IX_VariableAttachmentScriptStep_StepId" ON "SCRIPTS"."VariableAttachmentScriptStep" ("StepId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "SCRIPTS"."__EFMigrationsHistory" WHERE "MigrationId" = '20241119133845_addFilesInScripts') THEN
    INSERT INTO "SCRIPTS"."__EFMigrationsHistory" ("MigrationId", "ProductVersion")
    VALUES ('20241119133845_addFilesInScripts', '8.0.6');
    END IF;
END $EF$;
