DO $OTHERBOTSFIX$
BEGIN

-- Добавление обработки кода результата 'CHATBOT.CRASH' в чат-боте для работа на этапе 'Обработка сценарным ботом'
RAISE NOTICE '[OtherBotsFix] Start add WorkSuccessCodes CHATBOT.CRASH handling for ScriptBotWork state';

INSERT INTO "CRPM_CFG"."WorkSuccessCodes" ("WorkId", "ForceStateChange", "Code", "TargetStatusId", "RowVersion")
SELECT
  (SELECT "Id" FROM "CRPM_CFG"."RequestStateWorks" WHERE "RequestStateId"=(SELECT "Id" FROM "CRPM_CFG"."RequestStates" WHERE "Code"='SCRIPTBOT' AND "SchemeId"=1)), -- Работа 'Обработка сценарным чат-ботом'
  NULL,
  'CHATBOT.CRASH',
  (SELECT "Id" FROM "CRPM_CFG"."RequestStateStatuses" WHERE "Code" = 'CHATBOT'), -- Обработка чат-ботом
  638689145658339618
WHERE NOT EXISTS (SELECT 1 FROM "CRPM_CFG"."WorkSuccessCodes" WHERE "Code"='CHATBOT.CRASH' AND "WorkId"=(SELECT "Id" FROM "CRPM_CFG"."RequestStateWorks" WHERE "RequestStateId"=(SELECT "Id" FROM "CRPM_CFG"."RequestStates" WHERE "Code"='SCRIPTBOT' AND "SchemeId"=1)));

RAISE NOTICE '[OtherBotsFix] End add WorkSuccessCodes CHATBOT.CRASH handling for ScriptBotWork state';
-------------------------------------------------------------------------------------------

-- Добавление кода ошибки 'CHATBOT.ERROR.INFRASTRUCTURE' в чат-боте для работа на этапе 'Обработка чат-ботом'
RAISE NOTICE '[OtherBotsFix] Start add WorkErrorCodes CHATBOT.ERROR.INFRASTRUCTURE handling for ScriptBotWork state';

INSERT INTO "CRPM_CFG"."WorkErrorCodes" ("RetryCount", "RetryDelay", "WorkId", "ForceStateChange", "Code", "TargetStatusId", "RowVersion")
SELECT 3, 120, (SELECT "Id" FROM "CRPM_CFG"."RequestStateWorks" WHERE "RequestStateId"=(SELECT "Id" FROM "CRPM_CFG"."RequestStates" WHERE "Code"='CHATBOT' AND "SchemeId"=1)), NULL, 'CHATBOT.ERROR.INFRASTRUCTURE', 32, 638689148818153586
WHERE NOT EXISTS (SELECT 1 FROM "CRPM_CFG"."WorkErrorCodes" WHERE "Code"='CHATBOT.ERROR.INFRASTRUCTURE' AND "WorkId"=(SELECT "Id" FROM "CRPM_CFG"."RequestStateWorks" WHERE "RequestStateId"=(SELECT "Id" FROM "CRPM_CFG"."RequestStates" WHERE "Code"='CHATBOT' AND "SchemeId"=1)));

RAISE NOTICE '[OtherBotsFix] End add WorkErrorCodes CHATBOT.ERROR.INFRASTRUCTURE handling for ScriptBotWork state';
-------------------------------------------------------------------------------------------

-- Добавление кода ошибки 'CHATBOT.ERROR.GENERIC' в чат-боте для работа на этапе 'Обработка чат-ботом'
RAISE NOTICE '[OtherBotsFix] Start add WorkErrorCodes CHATBOT.ERROR.GENERIC handling for ScriptBotWork state';

INSERT INTO "CRPM_CFG"."WorkErrorCodes" ("RetryCount", "RetryDelay", "WorkId", "ForceStateChange", "Code", "TargetStatusId", "RowVersion")
SELECT NULL, NULL, (SELECT "Id" FROM "CRPM_CFG"."RequestStateWorks" WHERE "RequestStateId"=(SELECT "Id" FROM "CRPM_CFG"."RequestStates" WHERE "Code"='CHATBOT' AND "SchemeId"=1)), NULL, 'CHATBOT.ERROR.GENERIC', 32, 638689149547725586
WHERE NOT EXISTS (SELECT 1 FROM "CRPM_CFG"."WorkErrorCodes" WHERE "Code"='CHATBOT.ERROR.GENERIC' AND "WorkId"=(SELECT "Id" FROM "CRPM_CFG"."RequestStateWorks" WHERE "RequestStateId"=(SELECT "Id" FROM "CRPM_CFG"."RequestStates" WHERE "Code"='CHATBOT' AND "SchemeId"=1)));

RAISE NOTICE '[OtherBotsFix] End add WorkErrorCodes CHATBOT.ERROR.GENERIC handling for ScriptBotWork state';

END $OTHERBOTSFIX$;
