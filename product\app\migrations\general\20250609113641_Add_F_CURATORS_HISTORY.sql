CREATE OR REPLACE FUNCTION "DATA_MARTS"."F_CURATORS_HISTORY"()
    RETURNS TABLE("OperatorId" uuid, "OperatorFio" text, "CuratorId" uuid, "CuratorFio" text, "StartTime" timestamp without time zone, "EndTime" timestamp without time zone) 
    LANGUAGE 'plpgsql'
    COST 100
    VOLATILE PARALLEL UNSAFE
    ROWS 1000

AS $BODY$
	DECLARE
		CuratorIdAttributeCode TEXT := 'CuratorId';
BEGIN
	RETURN QUERY

	/* История по кураторам */
    WITH curators_history_query AS (
		SELECT "OPERATOR_ID" as "OperatorId", (CASE WHEN caChanges."DELETE_AUDIT_ID" IS NULL THEN "GUID_VALUE" ELSE null END) as "CuratorId", "CHANGE_DATE" as "StartTime" FROM "AWP_INFRA_AUD"."OPERATOR_CUSTOM_ATTR_CHANGES" caChanges
		INNER JOIN "AWP_INFRA_AUD"."AUDITS" audits ON audits."ID"=COALESCE(caChanges."INSERT_AUDIT_ID", caChanges."UPDATE_AUDIT_ID", caChanges."DELETE_AUDIT_ID")
		WHERE "CODE"=CuratorIdAttributeCode
    ),
	
	/* Удаленные операторы */
    deleted_operators AS (
	 	SELECT "ID",	    
		CASE
			WHEN "MIDDLE_NAME" IS NOT NULL AND "LAST_NAME" IS NOT NULL AND "FIRST_NAME" IS NOT NULL THEN ((("LAST_NAME" || ' '::text) || "FIRST_NAME") || ' '::text) || "MIDDLE_NAME"
			WHEN "LAST_NAME" IS NOT NULL AND "FIRST_NAME" IS NOT NULL AND "MIDDLE_NAME" IS NULL THEN ("LAST_NAME" || ' '::text) || "FIRST_NAME"
			WHEN "MIDDLE_NAME" IS NOT NULL AND "FIRST_NAME" IS NOT NULL AND "LAST_NAME" IS NULL THEN ("FIRST_NAME" || ' '::text) || "MIDDLE_NAME"
			WHEN "MIDDLE_NAME" IS NOT NULL AND "LAST_NAME" IS NOT NULL AND "FIRST_NAME" IS NULL THEN ("LAST_NAME" || ' '::text) || "MIDDLE_NAME"
			WHEN "MIDDLE_NAME" IS NULL AND "LAST_NAME" IS NOT NULL AND "FIRST_NAME" IS NULL THEN "LAST_NAME"
			ELSE ("LAST_NAME" || ' '::text) || "FIRST_NAME"
		END AS "FIO"
		FROM "AWP_INFRA_AUD"."OPERATOR_CHANGES"		
		WHERE "DELETE_AUDIT_ID" IS NOT NULL		
	)
	
	
	SELECT 
		curators_history_query."OperatorId", 
		COALESCE((SELECT "FIO" FROM "CALC"."INFRA_Operators_FIO" WHERE "CALC"."INFRA_Operators_FIO"."ID"=curators_history_query."OperatorId"), (SELECT "FIO" FROM deleted_operators WHERE curators_history_query."OperatorId"=deleted_operators."ID")) as "OperatorFio",
		curators_history_query."CuratorId",
		COALESCE((SELECT "FIO" FROM "CALC"."INFRA_Operators_FIO" WHERE "CALC"."INFRA_Operators_FIO"."ID"=curators_history_query."CuratorId"), (SELECT "FIO" FROM deleted_operators WHERE curators_history_query."CuratorId"=deleted_operators."ID")) as "CuratorFio",
		curators_history_query."StartTime", 
		(SELECT subquery."StartTime" FROM curators_history_query subquery WHERE subquery."StartTime" > curators_history_query."StartTime" AND (subquery."CuratorId" IS NULL OR subquery."CuratorId" <> curators_history_query."CuratorId") LIMIT 1) AS "EndTime"
	FROM curators_history_query
	WHERE curators_history_query."CuratorId" IS NOT NULL
	ORDER BY "OperatorFio", curators_history_query."StartTime" ASC;
END
$BODY$;