-- del old configuration
DELETE FROM "AWP_INFRA"."Configurations" WHERE "Name"='RequestPriorityIncreaseValue';

-- rename exists configuration
UPDATE "AWP_INFRA"."Configurations"
SET "Name"='RequestMonitoringCheckPeriod',
    "Description"='Периодичность проверки повышения приоритета обращений в секундах'
WHERE "Name"='RequestCheckPriorityPeriod';

-- add new configurations
INSERT INTO "AWP_INFRA"."Configurations" ("Id", "Name", "Value", "Description")
SELECT
    '440a6d83-1ed7-41c6-8537-14199c9030a2'::uuid,
        'RequestDecisionSettings',
    '{
    "IsEnabled": false,
    "IncreasePriorityValue": 10000
}
',
    'Настройка повышения приоритета по времени решения обращения'
    WHERE
NOT EXISTS (
  SELECT "Name" FROM "AWP_INFRA"."Configurations"
  WHERE "Name"='RequestDecisionSettings'
);

INSERT INTO "AWP_INFRA"."Configurations" ("Id", "Name", "Value", "Description")
SELECT
    '6ab9654c-8e74-4ffe-a71d-9ac56b693f93'::uuid,
        'RequestEscalationSettings', '{
    "IsEnabled": false,
    "IncreasePriorityValue": 10000
}
',
    'Настройка повышения приоритета по времени ответа клиенту (SA)'
    WHERE
NOT EXISTS (
  SELECT "Name" FROM "AWP_INFRA"."Configurations"
  WHERE "Name"='RequestEscalationSettings'
);
