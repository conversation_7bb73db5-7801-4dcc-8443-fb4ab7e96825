--  добавляем новую колонку в "EXTERNAL_EVALUATIONS"
ALTER TABLE "EXTERNAL_EVALUATIONS"."ExternalEvaluations" ADD "RequestId" bigint NOT NULL DEFAULT 0;

CREATE INDEX "IX_ExternalEvaluations_RequestId" ON "EXTERNAL_EVALUATIONS"."ExternalEvaluations" ("RequestId");

INSERT INTO "EXTERNAL_EVALUATIONS"."__MigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250321155436_AddRequestId', '9.0.0');


--  наполняем её данными
UPDATE "EXTERNAL_EVALUATIONS"."ExternalEvaluations"
SET "RequestId" = (select "Id" from "CRPM_DATA"."REQUEST_CA_GUID" reqCa where reqCa."Value3" = "RequestGuidId")
-- (где where не сработает - останется 0, но это обозначает, что на текущий момент это какая-то левая/потерянная привязка к обращению, с этим ничего уже не сделать)
where (select "Id" from "CRPM_DATA"."REQUEST_CA_GUID" reqCa where reqCa."Value3" = "RequestGuidId") is not null;

INSERT INTO "EXTERNAL_EVALUATIONS"."__MigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250324114000_SetRequestId', '9.0.0');


--  дропаем вьюху "V_EXTERNAL_EVALUATIONS", потому что после добавления новой колонки и удаления старой она становится равна самой таблице "EXTERNAL_EVALUATIONS"
DROP VIEW "CALC"."V_EXTERNAL_EVALUATIONS";


--  дропаем старую колонку из "EXTERNAL_EVALUATIONS"
DROP INDEX "EXTERNAL_EVALUATIONS"."IX_ExternalEvaluations_RequestGuidId";

ALTER TABLE "EXTERNAL_EVALUATIONS"."ExternalEvaluations" DROP COLUMN "RequestGuidId";

INSERT INTO "EXTERNAL_EVALUATIONS"."__MigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250324084935_RemoveRequestGuidId', '9.0.0');


--  удаляем кастом для RequestGuidId из карты кастомов обращений в CRPM
UPDATE "CRPM_CFG"."Settings"
SET "Configuration" = replace("Configuration", N'<Mapping Code="RequestGuidId" Type="Guid" Index="3" />', N'')
WHERE "Title" = N'CustomAttributesMapForRequests';


-- Обновляем функции/вьюхи, которые зависели от "V_EXTERNAL_EVALUATIONS"

-- MIGR "F_MONITORING_OPERATORS"
CREATE OR REPLACE FUNCTION "CALC"."F_MONITORING_OPERATORS"(_queueid smallint DEFAULT NULL::smallint)
 RETURNS TABLE("OPERATOR_ID" uuid, "FIO" text, "LOGIN" character varying, "QUEUE_ID" smallint, "QUEUE_TITLE" text, "CURRENT_STATUS" text, "CURRENT_STATUS_CODE" text, "SECONDS_IN_CURRENT_STATUS" numeric, "MAX_SECONDS_IN_CURRENT_STATUS" numeric, "CLOSED_REQUESTS" bigint, "CLOSED_REQUESTS_LAST_HOUR" bigint, "PROCESSING_REQUESTS" bigint, "ASA" numeric, "ART" numeric, "ART_ALARM" numeric, "ART_WARNING" numeric, "MAX_RT" bigint, "RT_SUM" numeric, "RT_COUNT" bigint, "CALLS_MISSED_COUNT" bigint, "CALLS_MISSED_COUNT_LAST_HOUR" bigint, "AHT" numeric, "ACSI" numeric, "AHT_ALARM" numeric, "AHT_WARNING" numeric, "ACSI_ALARM" numeric, "ACSI_WARNING" NUMERIC, "OPERATOR_ROOT_GROUP_ID" uuid, "OPERATOR_ROOT_GROUP_NAME" text)
 LANGUAGE sql
AS $function$

WITH constants (_startDate, _endDate) as (
    values (timezone('UTC'::text, CURRENT_DATE::timestamp with time zone), timezone('UTC'::text, now()))
),

     v_operator_status_queue AS (
         SELECT
             ob."OPERATOR_ID" AS "OperatorId",
             queues."queueId" AS "QueueId",
             queues."title" as "Title",
             ob."STATUS",
             ob."SECONDS_IN_STATUS"
         FROM "CALC"."V_OPERATOR_STATUSES" ob
                  LEFT JOIN (
             SELECT
                 rq."Id" as "queueId",
                 erq."ExecutorId" AS operator_id,
                 rq."Title" as title
             FROM "CALC"."CFG_RequestQueues" rq
                      JOIN "CALC"."CFG_ExecutorRequestQueues" erq ON rq."Id" = erq."QueueId"
         ) queues ON ob."OPERATOR_ID" = queues.operator_id
     ),

     requests_closed_info AS (
         SELECT
             r_kpi."LastQueueId" AS "QueueId",
             r_kpi."ClosedById" AS "OperatorId",
             COUNT(r_kpi."Id") AS "CLOSED_COUNT",
             COUNT(r_kpi."Id") FILTER (WHERE r_kpi."TimeClosed" >= _endDate - '1 hour'::interval) AS "CLOSED_COUNT_LAST_HOUR"
         FROM constants, "KPI"."REQUEST_KPI" r_kpi
         WHERE r_kpi."TimeClosed" BETWEEN _startDate AND _endDate AND r_kpi."LastQueueId" = _queueId
         GROUP BY r_kpi."ClosedById", r_kpi."LastQueueId"
     ),

     requests_info AS (
         SELECT o_kpi."OperatorId",
                AVG(
                        CASE
                            WHEN (
                                o_kpi."ContactStartTime" BETWEEN _startDate AND _endDate
                                ) THEN o_kpi."HT"
                            ELSE NULL::BIGINT
                            END
                ) AS "AHT"
         FROM constants, "KPI"."OPERATOR_KPI" o_kpi
         WHERE o_kpi."QueueId" = _queueId AND o_kpi."ContactEndTime" BETWEEN _startDate AND _endDate
         GROUP BY o_kpi."OperatorId"
     ),

     requests_in_work AS (
         SELECT
             COUNT(wrk."RequestId") AS "ProccessingRequestsCount",
             wrk."ExecutorId"
         FROM "CRPM_DATA"."Works" wrk
                  JOIN "CRPM_DATA"."Requests" r ON wrk."RequestId" = r."Id" AND r."QueueId" = _queueId
                  JOIN "CRPM_CFG"."RequestStateStatuses" AS rss ON rss."Id" = r."Status" AND rss."Code"::text = 'OPERATOR.WORK'::character varying::text
         GROUP BY wrk."ExecutorId"
     ),

     requests_call_info AS (
         SELECT call_state."OperatorId" AS "OperatorId",
                COUNT(call_state."Id") FILTER (
                    WHERE
                    (call_state."Direction" IS NULL OR call_state."Direction" = 0)
                        AND call_state."TimeCallMissed" IS NOT NULL
                    ) "CALLS_MISSED_COUNT",
                COUNT(call_state."Id") FILTER (
                    WHERE
                    (call_state."Direction" IS NULL OR call_state."Direction" = 0)
                        AND call_state."TimeCallMissed" IS NOT NULL
                        AND call_state."TimeCallMissed" >= _enddate - '1 hour'::interval
                    ) "CALLS_MISSED_COUNT_LAST_HOUR"
         FROM constants, "KPI"."OPERATOR_CALL_STATE" call_state
                             JOIN "CRPM_DATA"."Requests" r ON call_state."RequestId" = r."Id" AND r."QueueId" = _queueId
         WHERE (
             call_state."TimeCallAccepted" BETWEEN _startDate AND _endDate
             )
            OR (
             call_state."TimeCallMissed" BETWEEN _startDate AND _endDate
             )
         GROUP BY call_state."OperatorId"
     ),

     csi_info_evaluation_pre AS (
         SELECT
             "RequestId",
             "OperatorId",
             "Score",
             RANK() OVER (PARTITION BY "RequestId" ORDER BY "Date" DESC) AS "Rank"
         FROM constants, "EXTERNAL_EVALUATIONS"."ExternalEvaluations"
         WHERE "Date" BETWEEN _startDate AND _endDate
     ),

     csi_info_evaluation_result AS (
         SELECT
             "RequestId",
             "OperatorId",
             "Score"
         FROM csi_info_evaluation_pre
         WHERE "Rank" = 1
     ),

     csi_info_rkpi_result AS (
         SELECT
             r_kpi."Id" AS "RequestId",
             r_kpi."ClosedById" AS "OperatorId",
             r_kpi."CSI" AS "Score"
         FROM constants, "KPI"."REQUEST_KPI" r_kpi
         WHERE
             r_kpi."ClosedById" IS NOT NULL
           AND r_kpi."TimeRegistered" BETWEEN _startDate AND _endDate
           AND r_kpi."LastQueueId" = _queueId
     ),

     requests_csi_info AS (
         SELECT
             r."OperatorId",
             AVG(COALESCE(e."Score", r."Score")) AS "ACSI"
         FROM csi_info_rkpi_result AS r
                  LEFT JOIN csi_info_evaluation_result AS e ON r."RequestId" = e."RequestId"
         GROUP BY r."OperatorId"
     ),

     operator_response_all AS (
         SELECT all_rt."OperatorId",
                AVG(all_rt."RT") AS "ART",
                MAX(all_rt."RT") AS "MAX_RT",
                SUM(all_rt."RT") AS "RT_SUM",
                count(
                        CASE
                            WHEN all_rt."RT" IS NULL
                                OR all_rt."RT" = 0 THEN NULL
                            ELSE all_rt."OperatorId"
                            END
                ) AS "RT_COUNT"
         FROM constants, "KPI"."OPERATOR_RESPONSE" all_rt
         WHERE all_rt."ReactionStartDate" BETWEEN _startDate AND _endDate AND all_rt."QueueId" = _queueId
         GROUP BY all_rt."OperatorId"
     ),
     
     operator_root_group AS (
         	SELECT operatorgroup."Name", operatorgroup."Id", rq."Id" as "QueueId"
    		FROM 
    		    "CRPM_CFG"."CustomAttributes" ca
    		    JOIN "CRPM_CFG"."RequestQueues" rq ON rq."Id" = ca."QueueId"
    		    JOIN "AWP_INFRA"."OperatorGroups" operatorgroup ON operatorgroup."Id"::character varying::text = ca."Value"::text
    		WHERE 
    		    ca."Key" = 'Division'::text)

SELECT
    operators."ID" AS "OPERATOR_ID",
    operators."FIO",
    operators."LOGIN",
    rq."Id" AS "QUEUE_ID",
    rq."Title" AS "QUEUE_TITLE",
    ob."STATUS" AS "CURRENT_STATUS",
    ob."STATUS_CODE" AS "CURRENT_STATUS_CODE",
    COALESCE(ob."SECONDS_IN_STATUS", 0::NUMERIC(10, 2)::DOUBLE PRECISION)::NUMERIC(10, 2) AS "SECONDS_IN_CURRENT_STATUS",
    0::numeric AS "MAX_SECONDS_IN_CURRENT_STATUS",
    COALESCE(requests_closed_info."CLOSED_COUNT", 0::BIGINT) AS "CLOSED_REQUESTS",
    COALESCE(requests_closed_info."CLOSED_COUNT_LAST_HOUR", 0::BIGINT) AS "CLOSED_REQUESTS_LAST_HOUR",
    COALESCE(requests_in_work."ProccessingRequestsCount", 0::BIGINT) AS "PROCESSING_REQUESTS",
    0::NUMERIC(10, 2) AS "ASA",
    COALESCE(operator_response_all."ART"::NUMERIC(15, 2), 0::NUMERIC) AS "ART",
    queueasa."AlarmART"::numeric AS "ART_ALARM",
    queueasa."WarningART"::numeric AS "ART_WARNING",
    COALESCE(operator_response_all."MAX_RT", 0::BIGINT) AS "MAX_RT",
    COALESCE(operator_response_all."RT_SUM", 0::NUMERIC)::NUMERIC(10, 2) AS "RT_SUM",
    COALESCE(operator_response_all."RT_COUNT"::BIGINT, 0::BIGINT) AS "RT_COUNT",
    COALESCE(requests_call_info."CALLS_MISSED_COUNT"::BIGINT, 0::BIGINT) AS "CALLS_MISSED_COUNT", -- Пропущено звонков (Входящих)
    COALESCE(requests_call_info."CALLS_MISSED_COUNT_LAST_HOUR"::BIGINT, 0::BIGINT) AS "CALLS_MISSED_COUNT_LAST_HOUR", -- Пропущено звонков (Входящих) за последний час
    COALESCE(requests_info."AHT", 0::NUMERIC)::NUMERIC(10, 2) AS "AHT",
    COALESCE(requests_csi_info."ACSI", 0::NUMERIC)::NUMERIC(10, 2) AS "ACSI",
    queueasa."AlarmAHT"::numeric AS "AHT_ALARM",
    queueasa."WarningAHT"::numeric AS "AHT_WARNING",
    queueasa."AlarmACSI"::numeric AS "ACSI_ALARM",
    queueasa."WarningACSI"::numeric AS "ACSI_WARNING",
    operator_root_group."Id" AS "OPERATOR_ROOT_GROUP_ID",
    operator_root_group."Name" AS "OPERATOR_ROOT_GROUP_NAME"
FROM
    "CALC"."INFRA_Operators_FIO" operators
        JOIN "CALC"."V_OPERATOR_STATUSES" ob ON ob."OPERATOR_ID" = operators."ID"
        JOIN "CALC"."CFG_RequestQueues" rq ON rq."Id" = _queueId
        LEFT JOIN requests_closed_info ON requests_closed_info."OperatorId" = operators."ID"
        LEFT JOIN requests_in_work ON requests_in_work."ExecutorId" = operators."ID"
        LEFT JOIN requests_call_info ON requests_call_info."OperatorId" = operators."ID"
        LEFT JOIN requests_info ON requests_info."OperatorId" = operators."ID"
        LEFT JOIN requests_csi_info ON requests_csi_info."OperatorId" = operators."ID"
        LEFT JOIN operator_response_all ON operator_response_all."OperatorId" = operators."ID"
        LEFT JOIN "CALC"."CFG_Queue_ASA" AS queueasa ON queueasa."Id" = _queueId
        LEFT JOIN v_operator_status_queue ON v_operator_status_queue."OperatorId" = operators."ID" AND v_operator_status_queue."QueueId" = _queueId
        LEFT JOIN operator_root_group ON operator_root_group."QueueId" = _queueId
WHERE
    requests_closed_info."OperatorId" IS NOT NULL
   OR requests_in_work."ExecutorId" IS NOT NULL
   OR requests_call_info."OperatorId" IS NOT NULL
   OR v_operator_status_queue."OperatorId" IS NOT NULL
   OR operator_response_all."OperatorId" IS NOT NULL
   OR requests_info."OperatorId" IS NOT NULL
   OR requests_csi_info."ACSI" IS NOT NULL
ORDER BY
    operators."ID";

$function$
;


-- MIGR "F_OPERATORS_KPI_INFO"
CREATE OR REPLACE FUNCTION "CALC"."F_OPERATORS_KPI_INFO"(_startdate timestamp without time zone DEFAULT timezone('UTC'::text, (CURRENT_DATE)::timestamp with time zone), _enddate timestamp without time zone DEFAULT (timezone('UTC'::text, (CURRENT_DATE)::timestamp with time zone) + '1 day'::interval))
 RETURNS TABLE("OperatorId" uuid, "FIO" text, "LOGIN" character varying, "QUEUES_LIST" text, "CURRENT_STATUS" text, "SECONDS_IN_CURRENT_STATUS" numeric, "START_TIME" timestamp without time zone, "PAUSE_DURATION" bigint, "ONLINE_DURATION" bigint, "ONLINE_DURATION_TEXT" character varying, "IN_WORK_DURATION" bigint, "IN_LINE_DURATION" bigint, "UTILIZATION" numeric, "OCCUPANCY" numeric, "CONTACTS_COUNT" bigint, "CLOSED_REQUESTS" bigint, "REDIRECTED_REQUESTS" bigint, "POSTPONED_REQUESTS" bigint, "DROPPED_REQUESTS" bigint, "ASA" numeric, "AHT" numeric, "ACW" numeric, "ACSI" numeric, "ASSI" numeric, "ART" numeric, "A1RT" numeric, "VOICE_ART" numeric, "SALES_PERCENT" character varying, "NOW" timestamp with time zone, "HT_SUM" numeric, "CW_SUM" numeric, "CSI_SUM" numeric, "SSI_SUM" numeric, "RT_SUM" numeric, "1RT_SUM" numeric, "VOICE_RT_SUM" numeric, "HT_COUNT" bigint, "CW_COUNT" bigint, "CSI_COUNT" bigint, "SSI_COUNT" bigint, "RT_COUNT" bigint, "1RT_COUNT" bigint, "VOICE_RT_COUNT" bigint, "CALLS_RECEIVED_COUNT" bigint, "CALLS_ACCEPTED_COUNT" bigint, "CALLS_MISSED_COUNT" bigint, "CONVERSATION_TIME" bigint, "WAIT_TIME_DURATION" bigint, "CALLS_OUTBOUND_COUNT" bigint, "CURRENT_STATUS_CODE" text, "ON_BREAK" bigint, "DIVISION_NAME" text, "DIVISION_ID" uuid)
 LANGUAGE sql
 STABLE
AS $function$
    WITH v_operator_status_queue AS (
        SELECT
            vosq."OperatorId",
            vosq."QUEUES_LIST",
            vosq."STATUS",
            vosq."SECONDS_IN_STATUS",
            vosq."STATUS_CODE"
        FROM
            "CALC"."V_OPERATOR_STATUS_QUEUES" vosq
    ),

         work_session AS (
             SELECT
                 initial_statuses."OperatorId",
                 min(initial_statuses."StartDate") AS "START_TIME"
             FROM
                 "KPI"."OPERATOR_STATUSES" initial_statuses
             WHERE
                 initial_statuses."IsInitial" = true
               AND initial_statuses."StartDate" BETWEEN _startDate AND _endDate
             GROUP BY
                 initial_statuses."OperatorId"
         ),

         oper_status_statistic AS (
             SELECT s."OperatorId",
                    SUM(
                            CASE
                                WHEN s."Status" <> 'InWork'::TEXT
                                    AND s."Status" <> 'Ready'::TEXT
                                    AND s."Status" <> 'Offline'::TEXT
                                    AND s."Status" <> 'Initial'::TEXT THEN s."StatusDuration"
                                ELSE 0
                                END
                    ) AS "PAUSE_DURATION",
                    SUM(
                            CASE
                                WHEN (
                                    s."Status" = 'InWork'::TEXT
                                        OR s."Status" = 'Ready'::TEXT
                                    ) THEN s."StatusDuration"
                                ELSE 0
                                END
                    ) AS "ONLINE_DURATION",
                    SUM(
                            CASE
                                WHEN s."Status" = 'InWork'::TEXT THEN s."StatusDuration"
                                ELSE 0
                                END
                    ) AS "IN_WORK_DURATION",
                    SUM(
                            CASE
                                WHEN s."Status" = 'InWork'::TEXT
                                    OR s."Status" = 'Ready'::TEXT THEN s."StatusDuration"
                                ELSE 0
                                END
                    ) AS "IN_LINE_DURATION",
                    COALESCE(
                            SUM(
                                    CASE
                                        WHEN s."Status" = 'InWork'::TEXT
                                            OR s."Status" = 'Ready'::TEXT THEN s."StatusDuration"
                                        ELSE 0
                                        END
                            )::DOUBLE PRECISION / NULLIF(
                                    SUM(
                                            CASE
                                                WHEN s."Status" <> 'Offline'::TEXT
                                                    AND s."Status" <> 'Initial'::TEXT THEN s."StatusDuration"
                                                ELSE 0
                                                END
                                    )::DOUBLE PRECISION,
                                    0::DOUBLE PRECISION
                                                  ) * 100::DOUBLE PRECISION,
                            0::DOUBLE PRECISION
                    )::NUMERIC(10, 2) AS "UTILIZATION",
                    COALESCE(
                            SUM(
                                    CASE
                                        WHEN s."Status" = 'InWork'::TEXT THEN s."StatusDuration"
                                        ELSE 0
                                        END
                            )::DOUBLE PRECISION / NULLIF(
                                    SUM(
                                            CASE
                                                WHEN s."Status" = 'InWork'::TEXT
                                                    OR s."Status" = 'Ready'::TEXT THEN s."StatusDuration"
                                                ELSE 0
                                                END
                                    )::DOUBLE PRECISION,
                                    0::DOUBLE PRECISION
                                                  ) * 100::DOUBLE PRECISION,
                            0::DOUBLE PRECISION
                    )::NUMERIC(10, 2) AS "OCCUPANCY"
             FROM "KPI"."OPERATOR_STATUSES" s
             WHERE s."StartDate" BETWEEN _startDate AND _endDate
             GROUP BY s."OperatorId"
         ),

         requests_info AS (
             SELECT request_info_1."OperatorId", request_info_2."AHT", request_info_1."HT_SUM", request_info_1."HT_COUNT", request_info_1."CONTACTS_COUNT" FROM (SELECT o_kpi."OperatorId",
                                                                                                                                                                        SUM(
                                                                                                                                                                                CASE
                                                                                                                                                                                    WHEN (
                                                                                                                                                                                        o_kpi."ContactStartTime" BETWEEN _startDate AND _endDate
                                                                                                                                                                                        ) THEN o_kpi."HT"
                                                                                                                                                                                    ELSE NULL::BIGINT
                                                                                                                                                                                    END
                                                                                                                                                                       	) AS "HT_SUM",
                                                                                                                                                                        COUNT(
                                                                                                                                                                                CASE
                                                                                                                                                                                    WHEN (
                                                                                                                                                                                             o_kpi."ContactStartTime" BETWEEN _startDate AND _endDate
                                                                                                                                                                                             )
                                                                                                                                                                                        AND (
                                                                                                                                                                                             o_kpi."HT" IS NULL
                                                                                                                                                                                                 OR o_kpi."HT" = 0
                                                                                                                                                                                             ) THEN NULL
                                                                                                                                                                                    ELSE o_kpi."OperatorId"
                                                                                                                                                                                    END
                                                                                                                                                                        ) AS "HT_COUNT",
                                                                                                                                                                        COUNT(
                                                                                                                                                                                DISTINCT CASE
                                                                                                                                                                                             WHEN (
                                                                                                                                                                                                 o_kpi."ContactStartTime" BETWEEN _startDate AND _endDate
                                                                                                                                                                                                 ) THEN o_kpi."RequestId"
                                                                                                                                                                                             ELSE NULL::BIGINT
                                                                                                                                                                            END
                                                                                                                                                                        ) AS "CONTACTS_COUNT"
                                                                                                                                                                 FROM "KPI"."OPERATOR_KPI" o_kpi
                                                                                                                                                                 WHERE o_kpi."ContactEndTime" BETWEEN _startDate AND _endDate
                                                                                                                                                                 GROUP BY o_kpi."OperatorId") as request_info_1
                                                                                                                                                                    LEFT JOIN
                                                                                                                                                                (SELECT stat."OperatorId", AVG (stat."HT_SUM") as "AHT" FROM (SELECT o_kpi."OperatorId",
                                                                                                                                                                                                                                     SUM(
                                                                                                                                                                                                                                             CASE
                                                                                                                                                                                                                                                 WHEN (
                                                                                                                                                                                                                                                     o_kpi."ContactStartTime" BETWEEN _startDate AND _endDate
                                                                                                                                                                                                                                                     ) THEN o_kpi."HT"
                                                                                                                                                                                                                                                 ELSE NULL::BIGINT
                                                                                                                                                                                                                                                 END
                                                                                                                                                                                                                                     ) AS "HT_SUM"
                                                                                                                                                                                                                              FROM "KPI"."OPERATOR_KPI" o_kpi
                                                                                                                                                                                                                              WHERE o_kpi."ContactEndTime" BETWEEN _startDate AND _endDate
                                                                                                                                                                                                                              GROUP BY o_kpi."RequestId",o_kpi."OperatorId") as stat
                                                                                                                                                                 GROUP BY stat."OperatorId") as request_info_2 ON request_info_1."OperatorId" = request_info_2."OperatorId"
         ),

         csi_info_evaluation_pre AS ( -- Присваиваем ранг каждой записи на базе CreateDate, чтобы выбрать последнюю оценку (rank = 1)
             SELECT
                 eval."OperatorId",
                 eval."Score",
                 RANK() OVER (PARTITION BY eval."RequestId", eval."OperatorId" ORDER BY eval."Date" DESC) AS "Rank"
             FROM
                 "EXTERNAL_EVALUATIONS"."ExternalEvaluations" AS eval
                     LEFT JOIN "KPI"."REQUEST_KPI" r_kpi ON r_kpi."Id" = eval."RequestId"
             WHERE
                 eval."OperatorId" IS NOT NULL
               AND r_kpi."TimeRegistered" BETWEEN _startDate AND _endDate
         ),

         csi_info_evaluation_result AS (
             SELECT
                 "OperatorId",
                 "Score"
             FROM csi_info_evaluation_pre
             WHERE "Rank" = 1
         ),

         requests_csi_info AS ( -- Подсчёт CSI
             SELECT
                 eval."OperatorId",
                 AVG(eval."Score") AS "ACSI",
                 SUM(eval."Score") AS "CSI_SUM",
                 COUNT(eval."Score") AS "CSI_COUNT"
             FROM csi_info_evaluation_result AS eval
             GROUP BY eval."OperatorId" -- Проверить корректность группировки из rkpi
         ),

         requests_ssi_info_from_rkpi AS (
             SELECT
                 r_kpi."ClosedById" AS "OperatorId",
                 r_kpi."SSI",
                 r_kpi."Id" AS "RequestId"
             FROM "KPI"."REQUEST_KPI" r_kpi
             WHERE
                 r_kpi."TimeRegistered" BETWEEN _startDate AND _endDate
               AND r_kpi."ClosedById" IS NOT NULL
         ),

         operator_ssi_info_from_eval AS (
             SELECT
                 e."RunForOperatorId" AS "OperatorId",
                 e."ScoreResult" AS "SSI",
                 (e."Context"->>'requestId')::bigint AS "RequestId"
             FROM "CALC"."V_EVALUATIONS" AS e
             WHERE e."PublishedAt" BETWEEN _startDate AND _endDate
         ),

         pre_ssi_info_result AS (
             SELECT
                 r."OperatorId",
                 COALESCE(eval."SSI", r."SSI") AS "SSI"
             FROM requests_ssi_info_from_rkpi AS r
                      LEFT JOIN operator_ssi_info_from_eval AS eval ON r."RequestId" = eval."RequestId"
         ),

         ssi_info_result AS (
             SELECT
                 "OperatorId",
                 AVG("SSI") AS "ASSI",
                 SUM("SSI") AS "SSI_SUM",
                 COUNT("SSI") AS "SSI_COUNT"
             FROM pre_ssi_info_result
             GROUP BY "OperatorId"
         ),

         requests_closed_info AS (
             SELECT r_kpi."ClosedById" AS "OperatorId",
                    COUNT(r_kpi."Id") AS "CLOSED_COUNT"
             FROM "KPI"."REQUEST_KPI" r_kpi
             WHERE r_kpi."TimeClosed" BETWEEN _startDate AND _endDate
             GROUP BY r_kpi."ClosedById"
         ),

         requests_call_info AS (
             SELECT call_state."OperatorId" AS "OperatorId",
                    COUNT(
                            CASE
                                WHEN (
                                    (call_state."Direction" IS NULL OR call_state."Direction" = 0)
                                        AND
                                    (
                                        call_state."TimeCallAccepted" IS NOT NULL
                                            OR call_state."TimeCallMissed" IS NOT NULL
                                        )
                                    ) THEN call_state."Id"
                                ELSE NULL
                                END
                    ) AS "CALLS_RECEIVED_COUNT",
                    COUNT(
                            CASE
                                WHEN (
                                    (call_state."Direction" IS NULL OR call_state."Direction" = 0)
                                        AND
                                    (
                                        call_state."TimeCallAccepted" IS NOT NULL
                                        )
                                    ) THEN call_state."Id"
                                ELSE NULL
                                END
                    ) AS "CALLS_ACCEPTED_COUNT",
                    COUNT(
                            CASE
                                WHEN (
                                    (call_state."Direction" IS NULL OR call_state."Direction" = 0)
                                        AND
                                    (
                                        call_state."TimeCallMissed" IS NOT NULL
                                        )
                                    ) THEN call_state."Id"
                                ELSE NULL
                                END
                    ) AS "CALLS_MISSED_COUNT",
                    COALESCE(SUM(call_state."Duration"), 0) AS "CONVERSATION_TIME",
                    AVG(call_state."ACW") AS "ACW",
                    SUM(call_state."ACW") AS "CW_SUM",
                    COUNT(call_state."ACW") AS "CW_COUNT",
                    COUNT(
                            CASE
                                WHEN call_state."Direction" = 1 THEN 1
                                ELSE NULL
                                END
                    ) AS "CALLS_OUTBOUND_COUNT"
             FROM "KPI"."OPERATOR_CALL_STATE" call_state
             WHERE (
                 call_state."TimeCallAccepted" BETWEEN _startDate AND _endDate
                 )
                OR (
                 call_state."TimeCallMissed" BETWEEN _startDate AND _endDate
                 )
             GROUP BY call_state."OperatorId"
         ),

         redirected_postponed_info AS (
             SELECT "REQUEST_EVENT"."OperatorId",
                    count(
                            CASE
                                WHEN "REQUEST_EVENT"."IsRedirected" = true THEN "REQUEST_EVENT"."RequestId"
                                ELSE NULL::BIGINT
                                END
                    ) AS "REDIRECTED_COUNT",
                    count(
                            DISTINCT CASE
                                         WHEN "REQUEST_EVENT"."IsPostponed" = true THEN "REQUEST_EVENT"."RequestId"
                                         ELSE NULL::BIGINT
                        END
                    ) AS "POSTPONED_COUNT",
                    count(
                            CASE
                                WHEN "REQUEST_EVENT"."IsDropped" = true THEN "REQUEST_EVENT"."RequestId"
                                ELSE NULL::BIGINT
                                END
                    ) AS "DROPPED_COUNT"
             FROM "KPI"."REQUEST_EVENT"
             WHERE "REQUEST_EVENT"."EventTime" BETWEEN _startDate AND _endDate
             GROUP BY "REQUEST_EVENT"."OperatorId"
         ),

         first_reaction_dates AS (
             SELECT "OPERATOR_RESPONSE"."RequestId",
                    min("OPERATOR_RESPONSE"."ReactionStartDate") AS "FirstReactionStartDate"
             FROM "KPI"."OPERATOR_RESPONSE"
             WHERE "OPERATOR_RESPONSE"."ReactionStartDate" BETWEEN _startDate AND _endDate
             GROUP BY "OPERATOR_RESPONSE"."RequestId"
         ),

         first_rt AS (
             SELECT all_rt."OperatorId",
                    AVG(all_rt."RT") AS "A1RT",
                    SUM(all_rt."RT") AS "1RT_SUM",
                    count(
                            CASE
                                WHEN all_rt."RT" IS NULL
                                    OR all_rt."RT" = 0 THEN NULL
                                ELSE all_rt."OperatorId"
                                END
                    ) AS "1RT_COUNT"
             FROM "KPI"."OPERATOR_RESPONSE" all_rt
                      RIGHT JOIN first_reaction_dates ON first_reaction_dates."RequestId" = all_rt."RequestId"
                 AND first_reaction_dates."FirstReactionStartDate" = all_rt."ReactionStartDate"
             GROUP BY all_rt."OperatorId"
         ),

         operator_response_all AS (
             SELECT all_rt."OperatorId",
                    AVG(all_rt."RT") AS "ART",
                    SUM(all_rt."RT") AS "RT_SUM",
                    count(
                            CASE
                                WHEN all_rt."RT" IS NULL
                                    OR all_rt."RT" = 0 THEN NULL
                                ELSE all_rt."OperatorId"
                                END
                    ) AS "RT_COUNT"
             FROM "KPI"."OPERATOR_RESPONSE" all_rt
             WHERE all_rt."ReactionStartDate" BETWEEN _startDate AND _endDate
             GROUP BY all_rt."OperatorId"
         ),

         operator_voice_response AS (
             SELECT all_rt."OperatorId",
                    AVG(all_rt."RT") AS "VOICE_ART",
                    SUM(all_rt."RT") AS "VOICE_RT_SUM",
                    count(
                            CASE
                                WHEN all_rt."RT" IS NULL
                                    OR all_rt."RT" = 0 THEN NULL
                                ELSE all_rt."OperatorId"
                                END
                    ) AS "VOICE_RT_COUNT"
             FROM "KPI"."OPERATOR_RESPONSE" all_rt
             WHERE all_rt."ChannelType" = 10
               AND all_rt."ReactionStartDate" BETWEEN _startDate AND _endDate
             GROUP BY all_rt."OperatorId"
         ),

         operator_wait_custom AS (
             SELECT
                 "OPERATOR_ID" AS "OperatorId",
                 extract(
                         epoch
                         from ((now() at time zone 'utc') - "DATETIME_VALUE")
                 )::BIGINT as wait_time_duration
             FROM
                 "AWP_INFRA"."OPERATOR_CUSTOM_ATTRIBUTES"
             WHERE
                 "CODE" = 'LastWorkTime'
         ),
         on_break as (with onBreak as (select "OperatorStatusId" as "StatusId"
                                       from "AWP_INFRA"."CustomAttributes"
                                       where "Name" like 'OnBreak'),
                           previos as (select "UID",
                                              "OPERATOR_ID",
                                              "ACTUAL_STATUS_ID",
                                              "ACTUAL_STATUS_SET_CODE",
                                              "PREVIOUS_STATUS_ID",
                                              "DATE_FROM",
                                              lag("DATE_FROM") over (PARTITION BY "OPERATOR_ID" ORDER BY "DATE_FROM")       as lag,
                                              lag("DATE_FROM") over (PARTITION BY "OPERATOR_ID" ORDER BY "DATE_FROM" desc ) as lagDesc
                                       from "AWP_INFRA_AUD"."ACTUAL_OPERATOR_STAT_CHANGES"
                                       where "DATE_FROM" BETWEEN now() - interval '1 day' AND now()),
                           res as (select "OPERATOR_ID",
                                          "ACTUAL_STATUS_ID",
                                          "PREVIOUS_STATUS_ID",
                                          "DATE_FROM",
                                          lag,
                                          lagDesc
                                   from previos
                                   where "PREVIOUS_STATUS_ID" IN (select "StatusId" from onBreak)
                                   -- and lag is not null
                                   union all
                                   select "OPERATOR_ID", "ACTUAL_STATUS_ID", "PREVIOUS_STATUS_ID", now(), "DATE_FROM", lagDesc
                                   from previos
                                   where "ACTUAL_STATUS_ID" IN (select "StatusId" from onBreak)
                                     and lagDesc is null)

                      select "OPERATOR_ID", sum(EXTRACT(EPOCH FROM "DATE_FROM") - EXTRACT(EPOCH FROM lag)) as diff
                      from res
                      group by "OPERATOR_ID"),

           operator_root_group as (
    		    SELECT og."Name",
				   og."Id",
				   ogo."OperatorId",
				   COUNT(*) OVER (PARTITION BY ogo."OperatorId") AS cnt
				FROM "AWP_INFRA"."OperatorGroups" og
				LEFT JOIN "AWP_INFRA"."OperatorGroupOperator" ogo ON ogo."OperatorGroupId" = og."Id"
				WHERE og."ParentId" IS NULL)
    SELECT DISTINCT ON ("OperatorId")
        operators."ID" AS "OperatorId",
        operators."FIO",
        operators."LOGIN",
        v_operator_status_queue."QUEUES_LIST",
        v_operator_status_queue."STATUS" AS "CURRENT_STATUS",
        COALESCE(v_operator_status_queue."SECONDS_IN_STATUS", 0::NUMERIC(10, 2)::DOUBLE PRECISION)::NUMERIC(10, 2) AS "SECONDS_IN_CURRENT_STATUS",
        work_session."START_TIME" AS "START_TIME",
        COALESCE(oper_status_statistic."PAUSE_DURATION", 0::BIGINT) AS "PAUSE_DURATION",
        COALESCE(oper_status_statistic."ONLINE_DURATION", 0::BIGINT) AS "ONLINE_DURATION",
        to_char(
                make_interval(
                        secs => COALESCE(
                                oper_status_statistic."ONLINE_DURATION",
                                0::BIGINT
                                )
                ),
                'HH24:MI:SS'
        ) AS "ONLINE_DURATION_TEXT",
        COALESCE(oper_status_statistic."IN_WORK_DURATION", 0::BIGINT) AS "IN_WORK_DURATION",
        COALESCE(oper_status_statistic."IN_LINE_DURATION", 0::BIGINT) AS "IN_LINE_DURATION",
        COALESCE(oper_status_statistic."UTILIZATION", 0::NUMERIC) AS "UTILIZATION",
        COALESCE(oper_status_statistic."OCCUPANCY", 0::NUMERIC) AS "OCCUPANCY",
        COALESCE(requests_info."CONTACTS_COUNT", 0::BIGINT) AS "CONTACTS_COUNT",
        COALESCE(requests_closed_info."CLOSED_COUNT", 0::BIGINT) AS "CLOSED_REQUESTS",
        COALESCE(redirected_postponed_info."REDIRECTED_COUNT", 0::BIGINT) AS "REDIRECTED_REQUESTS",
        COALESCE(redirected_postponed_info."POSTPONED_COUNT", 0::BIGINT) AS "POSTPONED_REQUESTS",
        COALESCE(redirected_postponed_info."DROPPED_COUNT", 0::BIGINT) AS "DROPPED_REQUESTS",
        0::NUMERIC(10, 2) AS "ASA",
        COALESCE(requests_info."AHT", 0::NUMERIC)::NUMERIC(10, 2) AS "AHT",
        COALESCE(requests_call_info."ACW", 0::NUMERIC)::NUMERIC(10, 2) AS "ACW",
        COALESCE(requests_csi_info."ACSI", 0::NUMERIC)::NUMERIC(10, 2) AS "ACSI",
        COALESCE(ssi_info_result."ASSI", 0::NUMERIC)::NUMERIC(10, 2) AS "ASSI",
        COALESCE(operator_response_all."ART"::NUMERIC(15, 2), 0::NUMERIC) AS "ART",
        COALESCE(first_rt."A1RT"::NUMERIC(15, 2), 0::NUMERIC) AS "A1RT",
        COALESCE(operator_voice_response."VOICE_ART", 0::NUMERIC)::NUMERIC(10, 2) AS "VOICE_ART",
        '-' AS "SALES_PERCENT",
        timezone('UTC'::TEXT, now()::TIMESTAMP WITH TIME zone)::TIMESTAMP WITH TIME zone AS "NOW",
        COALESCE(requests_info."HT_SUM", 0::NUMERIC)::NUMERIC(10, 2) AS "HT_SUM",
        COALESCE(requests_call_info."CW_SUM", 0::NUMERIC)::NUMERIC(10, 2) AS "CW_SUM",
        COALESCE(requests_csi_info."CSI_SUM", 0::NUMERIC)::NUMERIC(10, 2) AS "CSI_SUM",
        COALESCE(ssi_info_result."SSI_SUM", 0::NUMERIC)::NUMERIC(10, 2) AS "SSI_SUM",
        COALESCE(operator_response_all."RT_SUM", 0::NUMERIC)::NUMERIC(10, 2) AS "RT_SUM",
        COALESCE(first_rt."1RT_SUM", 0::NUMERIC)::NUMERIC(10, 2) AS "1RT_SUM",
        COALESCE(operator_voice_response."VOICE_RT_SUM", 0::NUMERIC)::NUMERIC(10, 2) AS "VOICE_RT_SUM",
        COALESCE(requests_info."HT_COUNT"::BIGINT, 0::BIGINT) AS "HT_COUNT",
        COALESCE(requests_call_info."CW_COUNT"::BIGINT, 0::BIGINT) AS "CW_COUNT",
        COALESCE(requests_csi_info."CSI_COUNT"::BIGINT, 0::BIGINT) AS "CSI_COUNT",
        COALESCE(ssi_info_result."SSI_COUNT"::BIGINT, 0::BIGINT) AS "SSI_COUNT",
        COALESCE(operator_response_all."RT_COUNT"::BIGINT, 0::BIGINT) AS "RT_COUNT",
        COALESCE(first_rt."1RT_COUNT"::BIGINT, 0::BIGINT) AS "1RT_COUNT",
        COALESCE(operator_voice_response."VOICE_RT_COUNT"::BIGINT, 0::BIGINT) AS "VOICE_RT_COUNT",
        COALESCE(requests_call_info."CALLS_RECEIVED_COUNT"::BIGINT, 0::BIGINT) AS "CALLS_RECEIVED_COUNT", -- Поступило звонков (Входящих)
        COALESCE(requests_call_info."CALLS_ACCEPTED_COUNT"::BIGINT, 0::BIGINT) AS "CALLS_ACCEPTED_COUNT", -- Принято звонков (Входящих)
        COALESCE(requests_call_info."CALLS_MISSED_COUNT"::BIGINT, 0::BIGINT) AS "CALLS_MISSED_COUNT", -- Пропущено звонков (Входящих)
        COALESCE(requests_call_info."CONVERSATION_TIME"::BIGINT, 0::BIGINT) AS "CONVERSATION_TIME",
        COALESCE(operator_wait_custom.wait_time_duration, 0::BIGINT) AS "WAIT_TIME_DURATION",
        COALESCE(requests_call_info."CALLS_OUTBOUND_COUNT"::BIGINT, 0::BIGINT) AS "CALLS_OUTBOUND_COUNT",
        v_operator_status_queue."STATUS_CODE" AS "CURRENT_STATUS_CODE",
        COALESCE(on_break.diff::BIGINT, 0::BIGINT) AS "ON_BREAK",
        CASE 
	        WHEN operator_root_group.cnt > 1 THEN NULL 
	        ELSE operator_root_group."Name" 
	    END AS "DIVISION_NAME",
        CASE 
	        WHEN operator_root_group.cnt > 1 THEN NULL 
	        ELSE operator_root_group."Id" 
	    END AS "DIVISION_ID"
    FROM
        "CALC"."INFRA_Operators_FIO" operators
            LEFT JOIN v_operator_status_queue ON v_operator_status_queue."OperatorId" = operators."ID"
            LEFT JOIN oper_status_statistic ON oper_status_statistic."OperatorId" = operators."ID"
            LEFT JOIN work_session ON work_session."OperatorId" = operators."ID"
            LEFT JOIN requests_info ON requests_info."OperatorId" = operators."ID"
            LEFT JOIN requests_csi_info ON requests_csi_info."OperatorId" = operators."ID"
            LEFT JOIN ssi_info_result ON ssi_info_result."OperatorId" = operators."ID"
            LEFT JOIN requests_closed_info ON requests_closed_info."OperatorId" = operators."ID"
            LEFT JOIN requests_call_info ON requests_call_info."OperatorId" = operators."ID"
            LEFT JOIN redirected_postponed_info ON redirected_postponed_info."OperatorId" = operators."ID"
            LEFT JOIN operator_response_all ON operator_response_all."OperatorId" = operators."ID"
            LEFT JOIN first_rt ON first_rt."OperatorId" = operators."ID"
            LEFT JOIN operator_voice_response ON operator_voice_response."OperatorId" = operators."ID"
            LEFT JOIN operator_wait_custom ON operator_wait_custom."OperatorId" = operators."ID"
            LEFT JOIN on_break on on_break."OPERATOR_ID" = operators."ID"
            LEFT JOIN operator_root_group ON operator_root_group."OperatorId" = operators."ID"
    ORDER BY
        operators."ID";
    $function$
;


-- MIGR "F_OPERATORS_KPI_INFO_V2"
create or replace function "CALC"."F_OPERATORS_KPI_INFO_V2"(
    _startdate timestamp with time zone DEFAULT (
        timezone(
            'UTC' :: text,
            (CURRENT_DATE) :: timestamp with time zone
        )
    ) :: timestamp with time zone,
    _enddate timestamp with time zone DEFAULT (
        timezone(
            'UTC' :: text,
            ((CURRENT_DATE + '1 day' :: interval)) :: timestamp with time zone
        )
    ) :: timestamp with time zone
) returns TABLE(
    "OperatorId" uuid,
    "FIO" text,
    "LOGIN" character varying,
    "ReceivedInWork" bigint,
    "CLOSED_REQUESTS" bigint,
    "NotResolvedRequests" bigint,
    "RequestSessions" bigint,
    "IN_WORK" bigint,
    "PROCESSED" bigint,
    "WAITING" bigint,
    "QUEUES_LIST" text,
    "CURRENT_STATUS" text,
    "SECONDS_IN_CURRENT_STATUS" numeric,
    "START_TIME" timestamp without time zone,
    "PAUSE_DURATION" bigint,
    "ONLINE_DURATION" bigint,
    "ONLINE_DURATION_TEXT" character varying,
    "IN_WORK_DURATION" bigint,
    "IN_LINE_DURATION" bigint,
    "UTILIZATION" numeric,
    "OCCUPANCY" numeric,
    "REDIRECTED_REQUESTS" bigint,
    "POSTPONED_REQUESTS" bigint,
    "DROPPED_REQUESTS" bigint,
    "ASA" numeric,
    "AHT" numeric,
    "ACW" numeric,
    "ACSI" numeric,
    "ASSI" numeric,
    "ART" numeric,
    "A1RT" numeric,
    "SALES_PERCENT" character varying,
    "NOW" timestamp with time zone,
    "HT_SUM" numeric,
    "CW_SUM" numeric,
    "CSI_SUM" numeric,
    "SSI_SUM" numeric,
    "RT_SUM" numeric,
    "1RT_SUM" numeric,
    "HT_COUNT" bigint,
    "CW_COUNT" bigint,
    "CSI_COUNT" bigint,
    "SSI_COUNT" bigint,
    "RT_COUNT" bigint,
    "1RT_COUNT" bigint,
    "CALLS_RECEIVED_COUNT" bigint,
    "CALLS_ACCEPTED_COUNT" bigint,
    "CALLS_MISSED_COUNT" bigint,
    "CONVERSATION_TIME" bigint,
    "WAIT_TIME_DURATION" bigint,
    "CALLS_OUTBOUND_COUNT" bigint,
    "AP" numeric,
    "AP_WEIGHTED_SCORE_SUM" numeric,
    "AP_TOTAL_SCORES_COUNT" bigint
) stable language sql
as
$function$

WITH v_operator_status_queue AS (
    SELECT
        vosq."OperatorId",
        vosq."QUEUES_LIST",
        vosq."STATUS",
        vosq."SECONDS_IN_STATUS"
    FROM
        "CALC"."V_OPERATOR_STATUS_QUEUES" vosq
),

     work_session AS (
         SELECT
             initial_statuses."OperatorId",
             min(initial_statuses."StartDate") AS "START_TIME"
         FROM
             "KPI"."OPERATOR_STATUSES" initial_statuses
         WHERE
             initial_statuses."IsInitial" = true
           AND initial_statuses."StartDate" BETWEEN _startDate AND _endDate
         GROUP BY
             initial_statuses."OperatorId"
     ),

     oper_status_statistic AS (
         SELECT
             s."OperatorId",
             SUM(
                     CASE
                         WHEN s."Status" <> 'InWork'::TEXT
                             AND s."Status" <> 'Ready'::TEXT
                             AND s."Status" <> 'Offline'::TEXT
                             AND s."Status" <> 'Initial'::TEXT THEN s."StatusDuration"
                         ELSE 0
                         END
             ) AS "PAUSE_DURATION",
             SUM(
                     CASE
                         WHEN (
                             s."Status" = 'InWork'::TEXT
                                 OR s."Status" = 'Ready'::TEXT
                             ) THEN s."StatusDuration"
                         ELSE 0
                         END
             ) AS "ONLINE_DURATION",
             SUM(
                     CASE
                         WHEN s."Status" = 'InWork'::TEXT THEN s."StatusDuration"
                         ELSE 0
                         END
             ) AS "IN_WORK_DURATION",
             SUM(
                     CASE
                         WHEN s."Status" = 'InWork'::TEXT
                             OR s."Status" = 'Ready'::TEXT THEN s."StatusDuration"
                         ELSE 0
                         END
             ) AS "IN_LINE_DURATION",
             COALESCE(
                     SUM(
                             CASE
                                 WHEN s."Status" = 'InWork'::TEXT
                                     OR s."Status" = 'Ready'::TEXT THEN s."StatusDuration"
                                 ELSE 0
                                 END
                     )::DOUBLE PRECISION / NULLIF(
                             SUM(
                                     CASE
                                         WHEN s."Status" <> 'Offline'::TEXT
                                             AND s."Status" <> 'Initial'::TEXT THEN s."StatusDuration"
                                         ELSE 0
                                         END
                             )::DOUBLE PRECISION,
                             0::DOUBLE PRECISION
                                           ) * 100::DOUBLE PRECISION,
                     0::DOUBLE PRECISION
             )::NUMERIC(10, 2) AS "UTILIZATION",
             COALESCE(
                     SUM(
                             CASE
                                 WHEN s."Status" = 'InWork'::TEXT THEN s."StatusDuration"
                                 ELSE 0
                                 END
                     )::DOUBLE PRECISION / NULLIF(
                             SUM(
                                     CASE
                                         WHEN s."Status" = 'InWork'::TEXT
                                             OR s."Status" = 'Ready'::TEXT THEN s."StatusDuration"
                                         ELSE 0
                                         END
                             )::DOUBLE PRECISION,
                             0::DOUBLE PRECISION
                                           ) * 100::DOUBLE PRECISION,
                     0::DOUBLE PRECISION
             )::NUMERIC(10, 2) AS "OCCUPANCY"
         FROM
             "KPI"."OPERATOR_STATUSES" s
         WHERE
             s."StartDate" BETWEEN _startDate AND _endDate
         GROUP BY
             s."OperatorId"
     ),

     requests_info AS (
         SELECT
             o_kpi."OperatorId",
             AVG(
                     CASE
                         WHEN (
                             o_kpi."ContactStartTime" BETWEEN _startDate AND _endDate
                             ) THEN o_kpi."HT"
                         ELSE NULL::BIGINT
                         END
             ) AS "AHT",
             SUM(
                     CASE
                         WHEN (
                             o_kpi."ContactStartTime" BETWEEN _startDate AND _endDate
                             ) THEN o_kpi."HT"
                         ELSE NULL::BIGINT
                         END
             ) AS "HT_SUM",
             COUNT(
                     CASE
                         WHEN (
                                  o_kpi."ContactStartTime" BETWEEN _startDate AND _endDate
                                  )
                             AND (
                                  o_kpi."HT" IS NULL
                                      OR o_kpi."HT" = 0
                                  ) THEN NULL
                         ELSE o_kpi."OperatorId"
                         END
             ) AS "HT_COUNT"
         FROM
             "KPI"."OPERATOR_KPI" o_kpi
         WHERE
             o_kpi."ContactEndTime" BETWEEN _startDate AND _endDate
         GROUP BY
             o_kpi."OperatorId"
     ),

     requests_call_info AS (
         SELECT
             call_state."OperatorId" AS "OperatorId",
             COUNT(
                     CASE
                         WHEN (
                             (call_state."Direction" IS NULL OR call_state."Direction" = 0)
                                 AND
                             (
                                 call_state."TimeCallAccepted" IS NOT NULL
                                     OR call_state."TimeCallMissed" IS NOT NULL
                                 )
                             ) THEN call_state."Id"
                         ELSE NULL
                         END
             ) AS "CALLS_RECEIVED_COUNT",
             COUNT(
                     CASE
                         WHEN (
                             (call_state."Direction" IS NULL OR call_state."Direction" = 0)
                                 AND
                             (
                                 call_state."TimeCallAccepted" IS NOT NULL
                                 )
                             ) THEN call_state."Id"
                         ELSE NULL
                         END
             ) AS "CALLS_ACCEPTED_COUNT",
             COUNT(
                     CASE
                         WHEN (
                             (call_state."Direction" IS NULL OR call_state."Direction" = 0)
                                 AND
                             (
                                 call_state."TimeCallMissed" IS NOT NULL
                                 )
                             ) THEN call_state."Id"
                         ELSE NULL
                         END
             ) AS "CALLS_MISSED_COUNT",
             COALESCE(SUM(call_state."Duration"), 0) AS "CONVERSATION_TIME",
             AVG(call_state."ACW") AS "ACW",
             SUM(call_state."ACW") AS "CW_SUM",
             COUNT(call_state."ACW") AS "CW_COUNT",
             COUNT(
                     CASE
                         WHEN call_state."Direction" = 1 THEN call_state."Id"
                         ELSE NULL
                         END
             ) AS "CALLS_OUTBOUND_COUNT"
         FROM
             "KPI"."OPERATOR_CALL_STATE" call_state
         WHERE
             (call_state."TimeCallAccepted" BETWEEN _startDate AND _endDate)
            OR (call_state."TimeCallMissed" BETWEEN _startDate AND _endDate)
         GROUP BY
             call_state."OperatorId"
     ),

     redirected_postponed_info AS (
         SELECT
             "REQUEST_EVENT"."OperatorId",
             count(
                     CASE
                         WHEN "REQUEST_EVENT"."IsRedirected" = true
                             THEN "REQUEST_EVENT"."RequestId"
                         ELSE NULL::BIGINT
                         END
             ) AS "REDIRECTED_COUNT",
             count(
                     CASE
                         WHEN "REQUEST_EVENT"."IsPostponed" = true
                             THEN "REQUEST_EVENT"."RequestId"
                         ELSE NULL::BIGINT
                         END
             ) AS "POSTPONED_COUNT",
             count(
                     CASE
                         WHEN "REQUEST_EVENT"."IsDropped" = true
                             THEN "REQUEST_EVENT"."RequestId"
                         ELSE NULL::BIGINT
                         END
             ) AS "DROPPED_COUNT"
         FROM
             "KPI"."REQUEST_EVENT"
         WHERE
             "REQUEST_EVENT"."EventTime" BETWEEN _startDate AND _endDate
         GROUP BY
             "REQUEST_EVENT"."OperatorId"
     ),

     first_reaction_dates AS (
         SELECT
             "OPERATOR_RESPONSE"."RequestId",
             min("OPERATOR_RESPONSE"."ReactionStartDate") AS "FirstReactionStartDate"
         FROM
             "KPI"."OPERATOR_RESPONSE"
         WHERE
             "OPERATOR_RESPONSE"."ReactionStartDate" BETWEEN _startDate AND _endDate
         GROUP BY
             "OPERATOR_RESPONSE"."RequestId"
     ),

     first_rt AS (
         SELECT
             all_rt."OperatorId",
             AVG(all_rt."RT") AS "A1RT",
             SUM(all_rt."RT") AS "1RT_SUM",
             count(
                     CASE
                         WHEN all_rt."RT" IS NULL
                             OR all_rt."RT" = 0 THEN NULL
                         ELSE all_rt."OperatorId"
                         END
             ) AS "1RT_COUNT"
         FROM
             "KPI"."OPERATOR_RESPONSE" all_rt
                 RIGHT JOIN first_reaction_dates ON
                 first_reaction_dates."RequestId" = all_rt."RequestId"
                     AND first_reaction_dates."FirstReactionStartDate" = all_rt."ReactionStartDate"
         GROUP BY
             all_rt."OperatorId"
     ),

     operator_response_all AS (
         SELECT
             all_rt."OperatorId",
             AVG(all_rt."RT") AS "ART",
             SUM(all_rt."RT") AS "RT_SUM",
             count(
                     CASE
                         WHEN all_rt."RT" IS NULL
                             OR all_rt."RT" = 0 THEN NULL
                         ELSE all_rt."OperatorId"
                         END
             ) AS "RT_COUNT"
         FROM
             "KPI"."OPERATOR_RESPONSE" all_rt
         WHERE
             all_rt."ReactionStartDate" BETWEEN _startDate AND _endDate
         GROUP BY
             all_rt."OperatorId"
     ),

     operator_voice_response AS (
         SELECT
             all_rt."OperatorId",
             AVG(all_rt."RT") AS "VOICE_ART",
             SUM(all_rt."RT") AS "VOICE_RT_SUM",
             count(
                     CASE
                         WHEN all_rt."RT" IS NULL
                             OR all_rt."RT" = 0 THEN NULL
                         ELSE all_rt."OperatorId"
                         END
             ) AS "VOICE_RT_COUNT"
         FROM
             "KPI"."OPERATOR_RESPONSE" all_rt
         WHERE
             all_rt."ChannelType" = 10
           AND all_rt."ReactionStartDate" BETWEEN _startDate AND _endDate
         GROUP BY
             all_rt."OperatorId"
     ),

     operator_wait_custom AS (
         SELECT
             "OPERATOR_ID" AS "OperatorId",
             extract(
                     epoch
                     from ((now() at time zone 'utc') - "DATETIME_VALUE")
             )::BIGINT as wait_time_duration
         FROM
             "AWP_INFRA"."OPERATOR_CUSTOM_ATTRIBUTES"
         WHERE
             "CODE" = 'LastWorkTime'
     ),

     csi_info_evaluation_pre AS ( -- Присваиваем ранг каждой записи на базе Date, чтобы выбрать последнюю оценку (rank = 1)
         SELECT
             eval."OperatorId",
             eval."Score",
             RANK() OVER (PARTITION BY eval."RequestId", eval."OperatorId" ORDER BY eval."Date" DESC) AS "Rank"
         FROM
             "EXTERNAL_EVALUATIONS"."ExternalEvaluations" AS eval
                 LEFT JOIN "KPI"."REQUEST_KPI" r_kpi ON r_kpi."Id" = eval."RequestId"
         WHERE
             eval."OperatorId" IS NOT NULL
           AND r_kpi."TimeRegistered" BETWEEN _startDate AND _endDate
     ),

     csi_info_evaluation_result AS ( -- Получаем последнюю оценку по обращению
         SELECT
             eval."OperatorId",
             AVG(eval."Score") AS "ACSI",
             SUM(eval."Score") AS "CSI_SUM",
             COUNT(eval."Score") AS "CSI_COUNT"
         FROM csi_info_evaluation_pre eval
         WHERE "Rank" = 1
         GROUP BY eval."OperatorId"
     ),

     pre_sessions_req AS ( -- Получаем список событий по обработке обращений (неуникальный) с которыми работал оператор в заданный промежуток
         SELECT
             "Id",
             "OperatorId",
             "RequestId",
             "ContactStartTime",
             "ContactEndTime"
         FROM
             "KPI"."OPERATOR_KPI"
         WHERE
             "ContactStartTime" BETWEEN _startDate AND _endDate
     ),

     pre_received_req AS ( -- Получаем только уникальные обращения из pre_sessions_req
         SELECT
             sub."Id",
             sub."TimeClosed",
             sub."Status",
             sub."CSI",
             sub."SSI",
             sub."ClosedById",
             sub."RequestId"
         FROM (
             SELECT
                 psr."Id",
                 r."TimeClosed",
                 r."Status",
                 r_kpi."CSI",
                 r_kpi."SSI",
                 r_kpi."ClosedById",
                 r."Id" AS "RequestId",
                 ROW_NUMBER() OVER (PARTITION BY psr."RequestId" ORDER BY psr."Id" DESC) AS rn
             FROM
                 pre_sessions_req AS psr
                 LEFT JOIN "CRPM_DATA"."Requests" AS r ON psr."RequestId" = r."Id"
                 LEFT JOIN "KPI"."REQUEST_KPI" AS r_kpi ON psr."RequestId" = r_kpi."Id"
         ) sub
         WHERE
             sub.rn = 1
     ),

     req_sessions_and_received_res AS ( -- Вычисляем Поступило в работу с актуализацией + Кол-во сессий
         SELECT
             r."OperatorId",
             COUNT(riw."Id") AS "ReceivedInWork",
             COUNT(riw."Id") FILTER(WHERE riw."TimeClosed" IS NOT NULL) AS "ResolvedRequests",
             COUNT(riw."Id") FILTER(WHERE riw."TimeClosed" IS NULL) AS "NotResolvedRequests",
             COUNT(r."RequestId") AS "RequestSessions",
             COUNT(r."RequestId") FILTER(WHERE r."ContactEndTime" IS NOT NULL) AS "ProcessedRequests",
             COUNT(r."RequestId") FILTER(WHERE r."ContactEndTime" IS NULL) AS "InWorkRequests",
             COUNT(r."RequestId") FILTER (
                 WHERE (
                           (
                               (
                                   SELECT rss."Code"
                                   FROM "CRPM_CFG"."RequestStateStatuses" rss
                                   WHERE rss."Id" = riw."Status"
                               )
                           )::text
                           ) = ANY (
                           ARRAY [
                               'OPERATOR.POSTPONE'::character varying::text,
                               'DIVISION.WAIT'::character varying::text,
                               'OPERATOR.WAIT.CLIENT'::character varying::text,
                               'SUPERVISOR'::character varying::text,
                               'CLAIM.OPERATOR.WAIT'::character varying::text]
                           )
                 ) AS "WaitingRequests"
         FROM
             pre_sessions_req AS r
                 LEFT JOIN pre_received_req AS riw ON r."Id" = riw."Id"
         GROUP BY
             r."OperatorId"
     ),

     requests_ssi_info_from_rkpi AS (
         SELECT
             riw."ClosedById" AS "OperatorId",
             riw."RequestId",
             riw."SSI"
         FROM pre_received_req AS riw
         WHERE
             riw."ClosedById" IS NOT NULL
     ),

     operator_ssi_info_from_eval AS (
         SELECT
             e."RunForOperatorId" AS "OperatorId",
             e."ScoreResult" AS "SSI",
             (e."Context"->>'requestId')::bigint AS "RequestId"
         FROM "CALC"."V_EVALUATIONS" AS e
         WHERE e."PublishedAt" BETWEEN _startDate AND _endDate
     ),

     pre_ssi_info_result AS (
         SELECT
             r."OperatorId",
             COALESCE(eval."SSI", r."SSI") AS "SSI"
         FROM requests_ssi_info_from_rkpi AS r
                  LEFT JOIN operator_ssi_info_from_eval AS eval ON r."RequestId" = eval."RequestId"
     ),

     ssi_info_result AS (
         SELECT
             "OperatorId",
             AVG("SSI") AS "ASSI",
             SUM("SSI") AS "SSI_SUM",
             COUNT("SSI") AS "SSI_COUNT"
         FROM pre_ssi_info_result
         GROUP BY "OperatorId"
     ),

    agent_performance_req AS (
        SELECT
            eval."OperatorId",
            SUM(
                CASE 
                    WHEN eval."Score" = 1 THEN 0
                    WHEN eval."Score" = 2 THEN 0.25
                    WHEN eval."Score" = 3 THEN 0.50
                    WHEN eval."Score" = 4 THEN 0.75
                    WHEN eval."Score" = 5 THEN 1.00
                    ELSE 0
                END
            ) AS weighted_score_sum,
            COUNT(eval."Score") AS total_scores
        FROM
            "EXTERNAL_EVALUATIONS"."ExternalEvaluations" eval
        WHERE
            eval."OperatorId" IS NOT NULL
            AND eval."Date" BETWEEN _startDate AND _endDate
        GROUP BY
            eval."OperatorId"
    ),

    ap_calc AS (
        SELECT
            ap."OperatorId",
            CASE 
                WHEN ap.total_scores = 0 THEN 0
                ELSE (ap.weighted_score_sum / ap.total_scores) * 100
            END AS "AP"
        FROM
            agent_performance_req ap
    ),

     res AS (
         SELECT
             operators."ID" AS "OperatorId",
             operators."FIO",
             operators."LOGIN",
             COALESCE(rsars."ReceivedInWork", 0)::BIGINT AS "ReceivedInWork", -- "Поступило в работу"
             COALESCE(rsars."ResolvedRequests", 0)::BIGINT AS "CLOSED_REQUESTS", -- "Поступило в работу (решено/закрыто)"
             COALESCE(rsars."NotResolvedRequests", 0)::BIGINT AS "NotResolvedRequests", -- "Поступило в работу (не решено)"
             COALESCE(rsars."RequestSessions", 0)::BIGINT AS "RequestSessions", -- "Кол-во сессий"
             COALESCE(rsars."InWorkRequests", 0)::BIGINT AS "IN_WORK", -- "В работе"
             COALESCE(rsars."ProcessedRequests", 0)::BIGINT AS "PROCESSED", -- "Обработано"
             COALESCE(rsars."WaitingRequests", 0)::BIGINT AS "WAITING", -- "Ожидает ответа от оператора"
             v_operator_status_queue."QUEUES_LIST",
             v_operator_status_queue."STATUS" AS "CURRENT_STATUS",
             COALESCE(v_operator_status_queue."SECONDS_IN_STATUS", 0::NUMERIC(10, 2)::DOUBLE PRECISION)::NUMERIC(10, 2) AS "SECONDS_IN_CURRENT_STATUS",
             work_session."START_TIME" AS "START_TIME",
             COALESCE(oper_status_statistic."PAUSE_DURATION", 0::BIGINT) AS "PAUSE_DURATION",
             COALESCE(oper_status_statistic."ONLINE_DURATION", 0::BIGINT) AS "ONLINE_DURATION",

             to_char(
                     make_interval(
                             secs => COALESCE(
                                     oper_status_statistic."ONLINE_DURATION",
                                     0::BIGINT
                                     )
                     ),
                     'HH24:MI:SS'
             ) AS "ONLINE_DURATION_TEXT",

             COALESCE(oper_status_statistic."IN_WORK_DURATION", 0::BIGINT) AS "IN_WORK_DURATION",
             COALESCE(oper_status_statistic."IN_LINE_DURATION", 0::BIGINT) AS "IN_LINE_DURATION",
             COALESCE(oper_status_statistic."UTILIZATION", 0::NUMERIC) AS "UTILIZATION",
             COALESCE(oper_status_statistic."OCCUPANCY", 0::NUMERIC) AS "OCCUPANCY",
             COALESCE(redirected_postponed_info."REDIRECTED_COUNT", 0::BIGINT) AS "REDIRECTED_REQUESTS",
             COALESCE(redirected_postponed_info."POSTPONED_COUNT", 0::BIGINT) AS "POSTPONED_REQUESTS",
             COALESCE(redirected_postponed_info."DROPPED_COUNT", 0::BIGINT) AS "DROPPED_REQUESTS",
             0::NUMERIC(10, 2) AS "ASA",
             COALESCE(requests_info."AHT", 0::NUMERIC)::NUMERIC(10, 2) AS "AHT",
             COALESCE(requests_call_info."ACW", 0::NUMERIC)::NUMERIC(10, 2) AS "ACW",
             COALESCE(cier."ACSI", 0::NUMERIC)::NUMERIC(10, 2) AS "ACSI",
             COALESCE(sir."ASSI", 0::NUMERIC)::NUMERIC(10, 2) AS "ASSI",
             COALESCE(operator_response_all."ART"::NUMERIC(15, 2), 0::NUMERIC) AS "ART",
             COALESCE(first_rt."A1RT"::NUMERIC(15, 2), 0::NUMERIC) AS "A1RT",
             '-' AS "SALES_PERCENT",
             timezone('UTC'::TEXT, now()::TIMESTAMP WITH TIME zone)::TIMESTAMP WITH TIME zone AS "NOW",
             COALESCE(requests_info."HT_SUM", 0::NUMERIC)::NUMERIC(10, 2) AS "HT_SUM",
             COALESCE(requests_call_info."CW_SUM", 0::NUMERIC)::NUMERIC(10, 2) AS "CW_SUM",
             COALESCE(cier."CSI_SUM", 0::NUMERIC)::NUMERIC(10, 2) AS "CSI_SUM",
             COALESCE(sir."SSI_SUM", 0::NUMERIC)::NUMERIC(10, 2) AS "SSI_SUM",
             COALESCE(operator_response_all."RT_SUM", 0::NUMERIC)::NUMERIC(10, 2) AS "RT_SUM",
             COALESCE(first_rt."1RT_SUM", 0::NUMERIC)::NUMERIC(10, 2) AS "1RT_SUM",
             COALESCE(requests_info."HT_COUNT"::BIGINT, 0::BIGINT) AS "HT_COUNT",
             COALESCE(requests_call_info."CW_COUNT"::BIGINT, 0::BIGINT) AS "CW_COUNT",
             COALESCE(cier."CSI_COUNT"::BIGINT, 0::BIGINT) AS "CSI_COUNT",
             COALESCE(sir."SSI_COUNT"::BIGINT, 0::BIGINT) AS "SSI_COUNT",
             COALESCE(operator_response_all."RT_COUNT"::BIGINT, 0::BIGINT) AS "RT_COUNT",
             COALESCE(first_rt."1RT_COUNT"::BIGINT, 0::BIGINT) AS "1RT_COUNT",
             COALESCE(requests_call_info."CALLS_RECEIVED_COUNT"::BIGINT, 0::BIGINT) AS "CALLS_RECEIVED_COUNT",
             COALESCE(requests_call_info."CALLS_ACCEPTED_COUNT"::BIGINT, 0::BIGINT) AS "CALLS_ACCEPTED_COUNT",
             COALESCE(requests_call_info."CALLS_MISSED_COUNT"::BIGINT, 0::BIGINT) AS "CALLS_MISSED_COUNT",
             COALESCE(requests_call_info."CONVERSATION_TIME"::BIGINT, 0::BIGINT) AS "CONVERSATION_TIME",
             COALESCE(operator_wait_custom.wait_time_duration, 0::BIGINT) AS "WAIT_TIME_DURATION",
             COALESCE(requests_call_info."CALLS_OUTBOUND_COUNT"::BIGINT, 0::BIGINT) AS "CALLS_OUTBOUND_COUNT",
             COALESCE(ap_calc."AP", 0)::NUMERIC(10, 2) AS "AP",
             COALESCE(agent_performance_req."weighted_score_sum", 0)::NUMERIC(10, 2) AS "AP_WEIGHTED_SCORE_SUM",
             COALESCE(agent_performance_req."total_scores", 0)::BIGINT AS "AP_TOTAL_SCORES_COUNT"
         FROM
             "CALC"."INFRA_Operators_FIO" operators
                 LEFT JOIN v_operator_status_queue ON v_operator_status_queue."OperatorId" = operators."ID"
                 LEFT JOIN oper_status_statistic ON oper_status_statistic."OperatorId" = operators."ID"
                 LEFT JOIN work_session ON work_session."OperatorId" = operators."ID"
                 LEFT JOIN requests_info ON requests_info."OperatorId" = operators."ID"
                 LEFT JOIN requests_call_info ON requests_call_info."OperatorId" = operators."ID"
                 LEFT JOIN redirected_postponed_info ON redirected_postponed_info."OperatorId" = operators."ID"
                 LEFT JOIN operator_response_all ON operator_response_all."OperatorId" = operators."ID"
                 LEFT JOIN first_rt ON first_rt."OperatorId" = operators."ID"
                 LEFT JOIN operator_wait_custom ON operator_wait_custom."OperatorId" = operators."ID"
                 LEFT JOIN req_sessions_and_received_res AS rsars ON rsars."OperatorId" = operators."ID"
                 LEFT JOIN ssi_info_result AS sir ON sir."OperatorId" = operators."ID"
                 LEFT JOIN csi_info_evaluation_result cier ON cier."OperatorId" = operators."ID"
                 LEFT JOIN ap_calc ON ap_calc."OperatorId" = operators."ID"
                 LEFT JOIN agent_performance_req ON agent_performance_req."OperatorId" = operators."ID"
     )

SELECT
    "OperatorId",
    "FIO",
    "LOGIN",
    "ReceivedInWork",
    "CLOSED_REQUESTS",
    "NotResolvedRequests",
    "RequestSessions",
    "IN_WORK",
    (res."PROCESSED" - (res."REDIRECTED_REQUESTS" + res."POSTPONED_REQUESTS" + res."DROPPED_REQUESTS")) AS "PROCESSED",
    "WAITING",
    "QUEUES_LIST",
    "CURRENT_STATUS",
    "SECONDS_IN_CURRENT_STATUS",
    "START_TIME",
    "PAUSE_DURATION",
    "ONLINE_DURATION",
    "ONLINE_DURATION_TEXT",
    "IN_WORK_DURATION",
    "IN_LINE_DURATION",
    "UTILIZATION",
    "OCCUPANCY",
    "REDIRECTED_REQUESTS",
    "POSTPONED_REQUESTS",
    "DROPPED_REQUESTS",
    "ASA",
    "AHT",
    "ACW",
    "ACSI",
    "ASSI",
    "ART",
    "A1RT",
    "SALES_PERCENT",
    "NOW",
    "HT_SUM",
    "CW_SUM",
    "CSI_SUM",
    "SSI_SUM",
    "RT_SUM",
    "1RT_SUM",
    "HT_COUNT",
    "CW_COUNT",
    "CSI_COUNT",
    "SSI_COUNT",
    "RT_COUNT",
    "1RT_COUNT",
    "CALLS_RECEIVED_COUNT", -- Поступило звонков (Входящих)
    "CALLS_ACCEPTED_COUNT", -- Принято звонков (Входящих)
    "CALLS_MISSED_COUNT", -- Пропущено звонков (Входящих)
    "CONVERSATION_TIME",
    "WAIT_TIME_DURATION",
    "CALLS_OUTBOUND_COUNT", -- Совершено звонков (Исходящих)
    "AP", -- Agent Performance
    "AP_WEIGHTED_SCORE_SUM", -- Сумма взвешенных оценок для каждого оператора (для расчёта ИТОГО по колонке agent performance)
    "AP_TOTAL_SCORES_COUNT" -- Общее количество оценок для каждого оператора (для расчёта ИТОГО по колонке agent performance)
FROM
    res
ORDER BY
    "OperatorId"

$function$;


-- MIGR "F_QUEUES_REPORT_KPI"
create or replace function "CALC"."F_QUEUES_REPORT_KPI"(_startdate timestamp without time zone, _enddate timestamp without time zone)
    returns TABLE("ID" smallint, "TITLE" character varying, "QUEUE_TIME_DELETED" timestamp without time zone, "REGISTERED" bigint, "PENDING_OPERATOR_RESPONSE" bigint, "PROCESSING" bigint, "CLOSED_REQUESTS" bigint, "CLOSED_REQUESTS_BY_OPERATOR" bigint, "CLOSED_REQUESTS_BY_SUPERVISOR" bigint, "CLOSED_REQUESTS_BY_BOT" bigint, "CLOSED_REQUESTS_BY_SYSTEM" bigint, "CLOSED_REQUESTS_BY_BOT_PERCENT" bigint, "REDIRECTED" bigint, "REDIRECTED_PERCENT" numeric, "ADT" numeric, "MIN_DT" numeric, "MAX_DT" numeric, "ASA" numeric, "SA_MIN" bigint, "SA_MAX" bigint, "ASA_ALARM" numeric, "ASA_WARNING" numeric, "SA_COUNT" bigint, "SA_SUM" bigint, "A1RT" numeric, "A1RT_MIN" numeric, "A1RT_MAX" numeric, "A1RT_SUM" numeric, "A1RT_COUNT" bigint, "ART" numeric, "ART_ALARM" numeric, "ART_WARNING" numeric, "ART_SUM" numeric, "ART_COUNT" numeric, "AHT" numeric, "HT_MIN" bigint, "HT_MAX" bigint, "AHT_ALARM" numeric, "AHT_WARNING" numeric, "HT_COUNT" bigint, "HT_SUM" bigint, "ACW" numeric, "CW_MIN" bigint, "CW_MAX" bigint, "CW_COUNT" bigint, "CW_SUM" bigint, "SL" numeric, "SL_PERCENT" bigint, "REPEATED" bigint, "FCR" bigint, "FCR_PERCENT" bigint, "LOST" bigint, "LOST_PERCENT" numeric, "ESCALATED_BY_DISTRIBUTION" bigint, "ESCALATED_BY_DECISION" bigint, "ACSI" numeric, "ACSI_ALARM" numeric, "ACSI_WARNING" numeric, "CSI_COUNT" bigint, "CSI_SUM" bigint, "CSI_GROUP_COUNT" bigint, "CSI_TOTAL_COUNT" bigint, "ASSI" numeric, "SSI_GROUP_COUNT" bigint, "SSI_COUNT" bigint, "SSI_SUM" bigint, "CSAT" numeric, "CSAT_COUNT" bigint, "CDSAT" numeric, "CDSAT_COUNT" bigint)
    stable
    language sql
as
$function$

WITH usertypeenum AS (
    SELECT
        0 AS "Unknown",
        1 AS "Operator",
        2 AS "Supervisor",
        3 AS "Bot",
        4 AS "System"
),

     actual_status AS (
         SELECT
             "V_OPERATOR_STATUSES"."STATUS_CODE" AS "Status",
             "V_OPERATOR_STATUSES"."OPERATOR_ID" AS "OperatorId"
         FROM
             "CALC"."V_OPERATOR_STATUSES"
     ),

     redirected_info AS (
         SELECT
             "REQUEST_EVENT"."SourceQueueId",
             COUNT(
                     DISTINCT CASE
                                  WHEN "REQUEST_EVENT"."IsRedirected" = true THEN "REQUEST_EVENT"."RequestId"
                                  ELSE NULL::bigint
                 END
             ) AS "REDIRECTED_COUNT"
         FROM
             "KPI"."REQUEST_EVENT"
         WHERE
             "REQUEST_EVENT"."EventTime" BETWEEN _startDate AND _endDate
         GROUP BY
             "REQUEST_EVENT"."SourceQueueId"
     ),

     all_request_with_kpi_groupby_queueid AS (
         SELECT
             r."QueueId",
             COUNT(1) AS "REGISTERED",

             COUNT(1) FILTER (
                 WHERE
                 r."TimeClosed" IS NOT NULL
                 ) AS "CLOSED_REQUESTS",

             COUNT(1) FILTER (
                 WHERE
                 r."TimeClosed" IS NOT NULL AND
                 r_kpi."ClosedByUserType" = (
                     (
                         SELECT usertypeenum."Operator"
                         FROM usertypeenum
                     )
                 )
                 ) AS "CLOSED_REQUESTS_BY_OPERATOR",

             COUNT(1) FILTER (
                 WHERE
                 r."TimeClosed" IS NOT NULL AND
                 r_kpi."ClosedByUserType" = (
                     (
                         SELECT usertypeenum."Supervisor"
                         FROM usertypeenum
                     )
                 )
                 ) AS "CLOSED_REQUESTS_BY_SUPERVISOR",

             COUNT(1) FILTER (
                 WHERE
                 r."TimeClosed" IS NOT NULL
                     AND
                 (
                     (
                         r_kpi."ClosedByUserType" = 4 -- System
                             OR r_kpi."ClosedByUserType" = 0 -- Unknown
                         )
                         OR
                     (
                         r_kpi."ClosedByUserType" IS NULL
                             AND r_kpi."TimeBotWorkEnded" IS NULL
                         )
                     )
                 ) AS "CLOSED_BY_SYSTEM",

             COUNT(1) FILTER (
                 WHERE
                 r."TimeClosed" IS NOT NULL
                     AND
                 (
                     r_kpi."ClosedByUserType" = 3 -- Bot
                         OR
                     (
                         r_kpi."ClosedByUserType" IS NULL
                             AND r_kpi."TimeBotWorkEnded" IS NOT NULL
                         )
                     )
                 ) AS "CLOSED_BY_BOT",

             COUNT(r."RepeatedRequestId") AS "REPEATED",

             COUNT(1) FILTER (
                 WHERE
                 r."RepeatedRequestId" IS NULL
                     AND r."TimeClosed" IS NOT NULL
                 ) AS "FCR",

             COUNT(1) FILTER (
                 WHERE r."Status" IN (
                     (
                         SELECT rss."Id"
                         FROM "CRPM_CFG"."RequestStateStatuses" rss
                         WHERE rss."Code"::text IN (
                                                    'OPERATOR.WAIT'::text,
                                                    'CLAIM.OPERATOR.WAIT'::text)
                     )
                 )
                 ) AS "PENDING_OPERATOR_RESPONSE",

             AVG(r_kpi."HT") AS "AHT",
             COUNT(r_kpi."HT") AS "Count_HT",
             SUM(r_kpi."HT") AS "Sum_HT",

             MIN(r_kpi."HT") AS "MIN_HT",
             MAX(r_kpi."HT") AS "MAX_HT",

             AVG(r_kpi."SA") FILTER (WHERE r."TimeClosed" IS NOT NULL AND (r_kpi."TimeOperatorFirstResponse" IS NULL OR r_kpi."TimeOperatorFirstResponse" BETWEEN _startDate AND _endDate)) AS "ASA",
             MIN(r_kpi."SA") FILTER (WHERE r."TimeClosed" IS NOT NULL AND (r_kpi."TimeOperatorFirstResponse" IS NULL OR r_kpi."TimeOperatorFirstResponse" BETWEEN _startDate AND _endDate)) AS "MIN_SA",
             MAX(r_kpi."SA") FILTER (WHERE r."TimeClosed" IS NOT NULL AND (r_kpi."TimeOperatorFirstResponse" IS NULL OR r_kpi."TimeOperatorFirstResponse" BETWEEN _startDate AND _endDate)) AS "MAX_SA",
             COUNT(r_kpi."SA") FILTER (WHERE r."TimeClosed" IS NOT NULL AND (r_kpi."TimeOperatorFirstResponse" IS NULL OR r_kpi."TimeOperatorFirstResponse" BETWEEN _startDate AND _endDate)) AS "Count_SA",
             SUM(r_kpi."SA") FILTER (WHERE r."TimeClosed" IS NOT NULL AND (r_kpi."TimeOperatorFirstResponse" IS NULL OR r_kpi."TimeOperatorFirstResponse" BETWEEN _startDate AND _endDate)) AS "Sum_SA",

             MIN(
                     COALESCE(
                             (
                                 SELECT queueasa."AlarmASA"
                                 FROM "CALC"."CFG_Queue_ASA" queueasa
                                 WHERE queueasa."Id" = r."QueueId"
                             ),
                             0::double precision
                     )
             ) AS "AlarmASA",

             MIN(
                     COALESCE(
                             (
                                 SELECT queueasa."WarningASA"
                                 FROM "CALC"."CFG_Queue_ASA" queueasa
                                 WHERE queueasa."Id" = r."QueueId"
                             ),
                             0::double precision
                     )
             ) AS "WarningASA",

             MIN(
                     COALESCE(
                             (
                                 SELECT queueasa."AlarmAHT"
                                 FROM "CALC"."CFG_Queue_ASA" queueasa
                                 WHERE queueasa."Id" = r."QueueId"
                             ),
                             0::double precision
                     )
             ) AS "AlarmAHT",

             MIN(
                     COALESCE(
                             (
                                 SELECT queueasa."WarningAHT"
                                 FROM "CALC"."CFG_Queue_ASA" queueasa
                                 WHERE queueasa."Id" = r."QueueId"
                             ),
                             0::double precision
                     )
             ) AS "WarningAHT",

             MIN(
                     COALESCE(
                             (
                                 SELECT queueasa."AlarmACSI"
                                 FROM "CALC"."CFG_Queue_ASA" queueasa
                                 WHERE queueasa."Id" = r."QueueId"
                             ),
                             0::double precision
                     )
             ) AS "AlarmACSI",

             MIN(
                     COALESCE(
                             (
                                 SELECT queueasa."WarningACSI"
                                 FROM "CALC"."CFG_Queue_ASA" queueasa
                                 WHERE queueasa."Id" = r."QueueId"
                             ),
                             0::double precision
                     )
             ) AS "WarningACSI",

             MIN(
                     COALESCE(
                             (
                                 SELECT queueasa."AlarmART"
                                 FROM "CALC"."CFG_Queue_ASA" queueasa
                                 WHERE queueasa."Id" = r."QueueId"
                             ),
                             0::double precision
                     )
             ) AS "AlarmART",

             MIN(
                     COALESCE(
                             (
                                 SELECT queueasa."WarningART"
                                 FROM "CALC"."CFG_Queue_ASA" queueasa
                                 WHERE queueasa."Id" = r."QueueId"
                             ),
                             0::double precision
                     )
             ) AS "WarningART",

             COALESCE(
                     AVG(
                             date_part(
                                     'epoch'::text,
                                     COALESCE(
                                             r_kpi."TimeOperatorFirstDistribution"::timestamp with time zone,
                                             CASE
                                                 WHEN
                                                     r."TimeClosed" = '0001-01-01 00:00:00'::timestamp without time zone
                                                         OR r."TimeClosed" IS NULL
                                                     THEN timezone('utc'::text, now())::timestamp with time zone
                                                 ELSE r."TimeClosed"::timestamp with time zone
                                                 END
                                     ) - COALESCE(
                                             r_kpi."TimeBotWorkEnded"::timestamp with time zone,
                                             r_kpi."TimeCreated"::timestamp with time zone
                                         )
                             )
                     ),
                     0::double precision
             )::numeric(20, 0) AS "AverageInQueueTime",

             COALESCE(
                     MAX(
                             date_part(
                                     'epoch'::text,
                                     COALESCE(
                                             r_kpi."TimeOperatorFirstDistribution"::timestamp with time zone,
                                             CASE
                                                 WHEN
                                                     r."TimeClosed" = '0001-01-01 00:00:00'::timestamp without time zone
                                                         OR r."TimeClosed" IS NULL
                                                     THEN timezone('utc'::text, now())::timestamp with time zone
                                                 ELSE r."TimeClosed"::timestamp with time zone
                                                 END
                                     ) - COALESCE(
                                             r_kpi."TimeBotWorkEnded"::timestamp with time zone,
                                             r_kpi."TimeCreated"::timestamp with time zone
                                         )
                             )
                     ),
                     0::double precision
             )::numeric(20, 0) AS "MaxInQueueTime",

             COALESCE(
                     MIN(
                             date_part(
                                     'epoch'::text,
                                     COALESCE(
                                             r_kpi."TimeOperatorFirstDistribution"::timestamp with time zone,
                                             CASE
                                                 WHEN
                                                     r."TimeClosed" = '0001-01-01 00:00:00'::timestamp without time zone
                                                         OR r."TimeClosed" IS NULL
                                                     THEN timezone('utc'::text, now())::timestamp with time zone
                                                 ELSE r."TimeClosed"::timestamp with time zone
                                                 END
                                     ) - COALESCE(
                                             r_kpi."TimeBotWorkEnded"::timestamp with time zone,
                                             r_kpi."TimeCreated"::timestamp with time zone
                                         )
                             )
                     ),
                     0::double precision
             )::numeric(20, 0) AS "MinInQueueTime",

             COUNT(r_kpi."TimeLost") AS "LOST",

             COALESCE(
                     COUNT(r_kpi."TimeLost")::numeric / NULLIF(COUNT(r_kpi."Id"), 0) * 100,
                     0
             )::bigint AS "LOST_PERCENT",

             COUNT(1) FILTER (
                 WHERE (
                           (
                               (
                                   SELECT rss."Code"
                                   FROM "CRPM_CFG"."RequestStateStatuses" rss
                                   WHERE rss."Id" = r."Status"
                               )
                           )::text
                           ) = ANY (
                           ARRAY [
                               'OPERATOR.WORK'::character varying::text,
                               'OPERATOR.PAUSE'::character varying::text,
                               'AUTOPROCESSING'::character varying::text,
                               'THEMES.WAIT'::character varying::text,
                               'PVOO.SEND'::character varying::text,
                               'REROUTING'::character varying::text,
                               'CHATBOT'::character varying::text,
                               'CHATBOT.WAIT'::character varying::text,
                               'DIVISION.WAIT'::character varying::text,
                               'THEMES'::character varying::text,
                               'CLAIM.OPERATOR.WORK'::character varying::text,
                               'SCRIPTBOT.WAIT'::character varying::text,
                               'SCRIPTBOT.PROCESSING'::character varying::text]
                           )
                 ) AS "PROCESSING",

             COUNT(1) FILTER (
                 WHERE
                 (r_kpi."SA" IS NOT NULL AND r_kpi."SA" <> 0)
                     AND r_kpi."SA"::double precision <= (
                     (
                         SELECT queueasa."AlarmASA"
                         FROM "CALC"."CFG_Queue_ASA" queueasa
                         WHERE queueasa."Id" = r."QueueId"
                     )
                 )
                 ) AS "SL",

             COUNT(1) FILTER (
                 WHERE
                 bulk_requests_view."RequestOverdue" = 1
                 ) AS "ESCALATED_BY_DISTRIBUTION",

             COUNT(1) FILTER (
                 WHERE
                 bulk_requests_view."RequestDecisionOverdue" = 1
                 ) AS "ESCALATED_BY_DECISION"

         FROM
             "CRPM_DATA"."Requests" r
                 LEFT JOIN "KPI"."REQUEST_KPI" r_kpi ON r."Id" = r_kpi."Id"
                 LEFT JOIN "CALC"."BULK_REQUESTSVIEW" bulk_requests_view ON r."Id" = bulk_requests_view."Id"
         WHERE
             r_kpi."TimeRegistered" <= _endDate AND
             (
                 r."TimeClosed" IS NULL OR
                 r."TimeClosed" BETWEEN _startDate AND _endDate
                 )
         GROUP BY
             r."QueueId"
     ),

     art_req AS ( -- Попробовать по ID
         SELECT
             q_art."QueueId",
             SUM(q_art."sumRT") AS "sumRT",
             SUM(q_art."countRT") AS "countRT"
         FROM (
                  SELECT (
                             SELECT r."QueueId"
                             FROM "CRPM_DATA"."Requests" r
                             WHERE r."Id" = ore."RequestId"
                         ) AS "QueueId",
                         SUM(ore."RT") AS "sumRT",
                         COUNT(ore."RT") AS "countRT"
                  FROM "KPI"."OPERATOR_RESPONSE" ore
                  WHERE ore."ReactionStartDate" BETWEEN _startDate AND _endDate
                  GROUP BY ore."RequestId"
              ) q_art
         GROUP BY q_art."QueueId"
     ),

     a1rt_req AS ( -- Попробовать по ID
         SELECT q_art."QueueId",
                AVG(q_art."RT") AS "A1RT",
                SUM(q_art."RT") AS "sumRT",
                COALESCE(MAX(q_art."RT"), 0::bigint)::numeric(10, 0) AS "MAX_1RT",
                COALESCE(MIN(q_art."RT"), 0::bigint)::numeric(10, 0) AS "MIN_1RT",
                COUNT(q_art."RequestId") AS "countRT"
         FROM (
                  SELECT DISTINCT ON (ore."RequestId") (
                                                           SELECT r."QueueId"
                                                           FROM "CRPM_DATA"."Requests" r
                                                           WHERE r."Id" = ore."RequestId"
                                                       ) AS "QueueId",
                                                       ore."RT",
                                                       ore."RequestId"
                  FROM "KPI"."OPERATOR_RESPONSE" ore
                  WHERE ore."ReactionStartDate" BETWEEN _startDate AND _endDate
                  ORDER BY ore."RequestId",
                           ore."ReactionStartDate"
              ) q_art
         GROUP BY q_art."QueueId"
     ),

     acw_info AS (
         SELECT
             r."QueueId",
             AVG(ocs."ACW") AS "ACW",
             COUNT(ocs."ACW") AS "Count_CW",
             SUM(ocs."ACW") AS "Sum_CW",
             MIN(ocs."ACW") AS "MIN_CW",
             MAX(ocs."ACW") AS "MAX_CW"
         FROM
             "KPI"."OPERATOR_CALL_STATE" ocs
                 LEFT JOIN "CRPM_DATA"."Requests" AS r ON ocs."RequestId" = r."Id"
         WHERE
             ocs."TimeCallAccepted" BETWEEN _startDate AND _endDate
         GROUP BY
             r."QueueId"
     ),

     csi_info_evaluation_pre AS ( -- Присваиваем ранг каждой записи на базе Date, чтобы выбрать последнюю оценку (rank = 1)
         SELECT
             "RequestId",
             "Score",
             RANK() OVER (PARTITION BY "RequestId" ORDER BY "Date" DESC) AS "Rank"
         FROM "EXTERNAL_EVALUATIONS"."ExternalEvaluations"
     ),

     csi_info_evaluation_result AS (
         SELECT
             "RequestId",
             "Score"
         FROM csi_info_evaluation_pre
         WHERE "Rank" = 1
     ),

     csi_info AS ( -- Подсчёт CSI
         SELECT
             r."QueueId",
             AVG(e."Score") AS "ACSI",
             SUM(e."Score") AS "Sum_CSI",
             COUNT(e."Score") AS "Count_CSI",

             COUNT(
                     DISTINCT CASE
                                  WHEN e."Score" IS NULL THEN NULL::bigint
                                  ELSE e."Score"
                 END
             ) AS "CSI_GROUP_COUNT",

             COALESCE(COUNT(e."Score"), 0::bigint) AS "TOTAL_CSI_COUNT",

             COALESCE(
                     COUNT(
                             CASE
                                 WHEN e."Score" > 4 THEN e."Score"
                                 ELSE NULL::smallint
                                 END
                     )::numeric(10, 2) / NULLIF(COUNT(e."Score"), 0)::numeric,
                     0::numeric
             ) * 100::numeric(10, 2) AS "CSAT",

             COUNT(
                     CASE
                         WHEN e."Score" > 4 THEN e."Score"
                         ELSE NULL::smallint
                         END
             ) AS "Count_CSAT",

             COALESCE(
                     COUNT(
                             CASE
                                 WHEN e."Score" < 4 THEN e."Score"
                                 ELSE NULL::smallint
                                 END
                     )::numeric(10, 2) / NULLIF(COUNT(e."Score"), 0)::numeric,
                     0::numeric
             ) * 100::numeric(10, 2) AS "CDSAT",

             COUNT(
                     CASE
                         WHEN e."Score" < 4 THEN e."Score"
                         ELSE NULL::smallint
                         END
             ) AS "Count_CDSAT"
         FROM
             csi_info_evaluation_result AS e
                 LEFT JOIN "CRPM_DATA"."Requests" AS r ON r."Id" = e."RequestId"
         WHERE
             r."SchemaId" <> 3 -- division
           AND r."TimeClosed" BETWEEN _startDate AND _endDate
         GROUP BY r."QueueId"
     ),

     ssi_info_from_rkpi AS (
         SELECT
             "Id" AS "RequestId",
             "SSI"
         FROM
             "KPI"."REQUEST_KPI"
         WHERE
             "TimeRegistered" BETWEEN _startDate AND _endDate
     ),

     ssi_info_from_rkpi_with_queueId AS
         (
             SELECT
                 r_kpi."RequestId",
                 r."QueueId",
                 r_kpi."SSI"
             FROM
                 ssi_info_from_rkpi r_kpi
                     LEFT JOIN "CRPM_DATA"."Requests" AS r ON r."Id" = r_kpi."RequestId"
             WHERE r."SchemaId" <> 3 -- division
         ),

     ssi_info_from_eval AS (
         SELECT
             r_kpi."RequestId",
             r_kpi."QueueId",
             e."ScoreResult" AS "EvalSSI",
             r_kpi."SSI" AS "ReqSSI",
             e."PublishedAt"
         FROM
             ssi_info_from_rkpi_with_queueId AS r_kpi
                 LEFT JOIN "CALC"."V_EVALUATIONS" AS e ON r_kpi."RequestId" = (e."Context"->>'requestId')::bigint
         WHERE
             e."ScoreResult" IS NOT NULL OR r_kpi."SSI" IS NOT NULL
     ),

     ssi_info_filtered AS (
         SELECT
             "QueueId",
             "RequestId",
             (CASE
                  WHEN ("ReqSSI" IS NOT NULL AND "PublishedAt" IS NULL) THEN "ReqSSI"
                  ELSE COALESCE("EvalSSI", "ReqSSI")
                 END) AS "SSI"
         FROM
             ssi_info_from_eval
     ),

     pre_ssi_info_result AS (
         SELECT
             "QueueId",
             "RequestId",
             AVG("SSI") AS "SSI"
         FROM
             ssi_info_filtered
         GROUP BY "RequestId", "QueueId"
     ),

     ssi_info_result AS (
         SELECT
             "QueueId",
             COUNT(
                     DISTINCT CASE
                                  WHEN "SSI" IS NULL THEN NULL::bigint
                                  ELSE "RequestId"
                 END
             ) AS "SSI_GROUP_COUNT",
             AVG("SSI") AS "ASSI",
             COUNT("SSI") AS "Count_SSI",
             SUM("SSI") AS "Sum_SSI"
         FROM pre_ssi_info_result
         GROUP BY "QueueId"
     ),

     res AS (
         SELECT
             queues."Id" AS "ID",
             queues."Title" AS "TITLE",
             queues."TimeDeleted" as "QUEUE_TIME_DELETED",

             COALESCE(rbq."REGISTERED", 0::bigint) AS "REGISTERED",
             COALESCE(rbq."PENDING_OPERATOR_RESPONSE", 0::bigint) AS "PENDING_OPERATOR_RESPONSE",
             COALESCE(rbq."CLOSED_REQUESTS", 0::bigint) AS "CLOSED_REQUESTS",
             COALESCE(rbq."CLOSED_REQUESTS_BY_OPERATOR", 0::bigint) AS "CLOSED_REQUESTS_BY_OPERATOR",
             COALESCE(rbq."CLOSED_REQUESTS_BY_SUPERVISOR", 0::bigint) AS "CLOSED_REQUESTS_BY_SUPERVISOR",
             COALESCE(rbq."CLOSED_BY_BOT", 0::bigint) AS "CLOSED_REQUESTS_BY_BOT",
             COALESCE(rbq."CLOSED_BY_SYSTEM", 0::bigint) AS "CLOSED_REQUESTS_BY_SYSTEM",
             COALESCE(rbq."PROCESSING", 0::bigint) AS "PROCESSING",

             COALESCE(
                     rbq."CLOSED_BY_BOT"::numeric / NULLIF(rbq."CLOSED_REQUESTS", 0) * 100,
                     0
             )::bigint AS "CLOSED_REQUESTS_BY_BOT_PERCENT",

             COALESCE(rbq."LOST", 0::bigint) AS "LOST",
             COALESCE(rbq."LOST_PERCENT", 0::bigint::numeric) AS "LOST_PERCENT",

             COALESCE(rbq."AverageInQueueTime", 0::numeric) AS "ADT",
             COALESCE(rbq."MaxInQueueTime", 0::numeric) AS "MAX_DT",
             COALESCE(rbq."MinInQueueTime", 0::numeric) AS "MIN_DT",

             COALESCE(acwi."ACW"::numeric(10, 2), 0::numeric) AS "ACW",
             COALESCE(acwi."MIN_CW", 0)::bigint AS "CW_MIN",
             COALESCE(acwi."MAX_CW", 0)::bigint AS "CW_MAX",
             COALESCE(acwi."Count_CW", 0::bigint) AS "CW_COUNT",
             COALESCE(acwi."Sum_CW", 0::bigint) AS "CW_SUM",

             COALESCE(rbq."ASA"::numeric(10, 2), 0::numeric) AS "ASA",
             COALESCE(rbq."AlarmASA"::numeric(10, 2), 0) AS "ASA_ALARM",
             COALESCE(rbq."WarningASA", 0::double precision)::numeric(10, 2) AS "ASA_WARNING",
             COALESCE(rbq."Count_SA", 0::bigint) AS "SA_COUNT",
             COALESCE(rbq."Sum_SA", 0::bigint) AS "SA_SUM",
             COALESCE(rbq."MIN_SA", 0::bigint) AS "SA_MIN",
             COALESCE(rbq."MAX_SA", 0::bigint) AS "SA_MAX",

             COALESCE(rbq."AHT"::numeric(10, 2), 0::numeric) AS "AHT",
             COALESCE(rbq."AlarmAHT", 0::double precision)::numeric(10, 2) AS "AHT_ALARM",
             COALESCE(rbq."WarningAHT", 0::double precision)::numeric(10, 2) AS "AHT_WARNING",
             COALESCE(rbq."Count_HT", 0::bigint) AS "HT_COUNT",
             COALESCE(rbq."Sum_HT", 0::bigint) AS "HT_SUM",
             COALESCE(rbq."MIN_HT", 0)::bigint AS "HT_MIN",
             COALESCE(rbq."MAX_HT", 0)::bigint AS "HT_MAX",

             COALESCE(
                     (art_req."sumRT"::numeric / NULLIF(art_req."countRT", 0))::numeric(15, 2),
                     0::numeric) AS "ART",
             COALESCE(rbq."AlarmART", 0::double precision)::numeric(10, 2) AS "ART_ALARM",
             COALESCE(rbq."WarningART", 0::double precision)::numeric(10, 2) AS "ART_WARNING",
             COALESCE(art_req."sumRT", 0::bigint::numeric) AS "ART_SUM",
             COALESCE(art_req."countRT", 0::bigint::numeric) AS "ART_COUNT",

             COALESCE(a1rt_req."A1RT"::numeric(15, 2), 0::numeric) AS "A1RT",
             COALESCE(a1rt_req."sumRT", 0::bigint::numeric) AS "A1RT_SUM",
             COALESCE(a1rt_req."countRT", 0::bigint) AS "A1RT_COUNT",
             COALESCE(a1rt_req."MAX_1RT", 0::bigint::numeric) AS "A1RT_MAX",
             COALESCE(a1rt_req."MIN_1RT", 0::bigint::numeric) AS "A1RT_MIN",

             COALESCE(csii."ACSI"::numeric(10, 2), 0::numeric) AS "ACSI",
             COALESCE(rbq."AlarmACSI", 0::double precision)::numeric(10, 2) AS "ACSI_ALARM",
             COALESCE(rbq."WarningACSI", 0::double precision)::numeric(10, 2) AS "ACSI_WARNING",
             COALESCE(csii."Count_CSI", 0::bigint) AS "CSI_COUNT",
             COALESCE(csii."Sum_CSI"::bigint, 0::bigint) AS "CSI_SUM",
             COALESCE(csii."CSI_GROUP_COUNT", 0::bigint) AS "CSI_GROUP_COUNT",
             COALESCE(csii."TOTAL_CSI_COUNT", 0::bigint) AS "CSI_TOTAL_COUNT",

             COALESCE(ssii."ASSI"::numeric(10, 2), 0::numeric) AS "ASSI",
             COALESCE(ssii."SSI_GROUP_COUNT", 0)::bigint AS "SSI_GROUP_COUNT",
             COALESCE(ssii."Count_SSI", 0)::bigint AS "SSI_COUNT",
             COALESCE(ssii."Sum_SSI", 0)::bigint AS "SSI_SUM",

             COALESCE(csii."CSAT"::numeric(10, 2), 0::numeric) AS "CSAT",
             COALESCE(csii."Count_CSAT", 0::bigint) AS "CSAT_COUNT",

             COALESCE(csii."CDSAT"::numeric(10, 2), 0::numeric) AS "CDSAT",
             COALESCE(csii."Count_CDSAT", 0::bigint) AS "CDSAT_COUNT",

             COALESCE(
                     (
                         SELECT
                             ri."REDIRECTED_COUNT"
                         FROM
                             redirected_info ri
                         WHERE
                             ri."SourceQueueId" = queues."Id"
                     ),
                     0::bigint
             ) AS "REDIRECTED",

             (
                 CASE
                     WHEN COALESCE(
                                  (
                                      SELECT
                                          ri."REDIRECTED_COUNT"
                                      FROM
                                          redirected_info ri
                                      WHERE
                                          ri."SourceQueueId" = queues."Id"
                                  ), 0::bigint) = 0 THEN 0::numeric(10, 0)
                     ELSE (
                         CASE
                             WHEN rbq."REGISTERED" IS NULL
                                 OR rbq."REGISTERED" = 0 THEN 100::numeric(10, 0)
                             ELSE (
                                 CASE
                                     WHEN (
                                              (
                                                  SELECT ri."REDIRECTED_COUNT"
                                                  FROM redirected_info ri
                                                  WHERE ri."SourceQueueId" = queues."Id"
                                              )::numeric(10, 2) / rbq."REGISTERED"::numeric(10, 2)
                                              ) > 1 THEN 100::numeric(10, 0)
                                     ELSE (
                                         100::numeric(10, 2) * (
                                             SELECT ri."REDIRECTED_COUNT"
                                             FROM redirected_info ri
                                             WHERE ri."SourceQueueId" = queues."Id"
                                         )::numeric(10, 2) / rbq."REGISTERED"::numeric(10, 2)
                                         )::numeric(10, 0)
                                     END
                                 )::numeric(10, 0)
                             END
                         )::numeric(10, 0)
                     END
                 )::numeric(10, 0) AS "REDIRECTED_PERCENT",

             COALESCE(rbq."SL"::numeric(10, 2), 0::numeric) AS "SL",

             COALESCE(
                     rbq."SL"::numeric / NULLIF(rbq."REGISTERED", 0) * 100,
                     0
             )::bigint AS "SL_PERCENT",

             COALESCE(rbq."REPEATED", 0::bigint) AS "REPEATED",
             COALESCE(rbq."FCR", 0::bigint) AS "FCR",
             COALESCE(rbq."ESCALATED_BY_DISTRIBUTION", 0::bigint) AS "ESCALATED_BY_DISTRIBUTION",
             COALESCE(rbq."ESCALATED_BY_DECISION", 0::bigint) AS "ESCALATED_BY_DECISION"
         FROM
             "CALC"."CFG_RequestQueues" AS queues
                 LEFT JOIN all_request_with_kpi_groupby_queueid AS rbq ON rbq."QueueId" = queues."Id"
                 LEFT JOIN art_req ON art_req."QueueId" = queues."Id"
                 LEFT JOIN a1rt_req ON a1rt_req."QueueId" = queues."Id"
                 LEFT JOIN acw_info AS acwi ON acwi."QueueId" = queues."Id"
                 LEFT JOIN csi_info AS csii ON csii."QueueId" = queues."Id"
                 LEFT JOIN ssi_info_result AS ssii ON ssii."QueueId" = queues."Id"
        WHERE
            queues."IsService" = FALSE
     )

SELECT
    "ID", -- Id очереди
    "TITLE", -- Название очереди
    "QUEUE_TIME_DELETED", -- Время удаление очереди (Для фильтрации)

    "REGISTERED", -- Зарегистрировано
    "PENDING_OPERATOR_RESPONSE", -- Ожидает обработки
    "PROCESSING", -- В обработке

    "CLOSED_REQUESTS", -- Закрытые Всего
    "CLOSED_REQUESTS_BY_OPERATOR", -- Закрытые Оператором
    "CLOSED_REQUESTS_BY_SUPERVISOR", -- Закрытые Супервизором
    "CLOSED_REQUESTS_BY_BOT", -- Закрытые БОТом
    "CLOSED_REQUESTS_BY_SYSTEM", -- Закрытые Системой
    "CLOSED_REQUESTS_BY_BOT_PERCENT", -- % автоматиации (Доля обращений, которые были закрыты БОТом)

    "REDIRECTED", -- Переведено по очереди
    "REDIRECTED_PERCENT", -- Переведено %

    "ADT", -- ADT
    "MIN_DT", -- Min DT
    "MAX_DT", -- Max DT

    "ASA", -- ASA
    "SA_MIN",
    "SA_MAX",
    "ASA_ALARM",
    "ASA_WARNING",
    "SA_COUNT",
    "SA_SUM",

    "A1RT", -- A1RT | ❗ Проверить логику (Утрочить у Натальи почему сумма, если среднее)
    "A1RT_MIN",
    "A1RT_MAX",
    "A1RT_SUM",
    "A1RT_COUNT",

    "ART", -- ART
    "ART_ALARM",
    "ART_WARNING",
    "ART_SUM",
    "ART_COUNT",

    "AHT", -- AHT | ❗ Проверить логику (Уточнить у Натальи)
    "HT_MIN",
    "HT_MAX",
    "AHT_ALARM",
    "AHT_WARNING",
    "HT_COUNT",
    "HT_SUM",

    "ACW", -- ACW
    "CW_MIN",
    "CW_MAX",
    "CW_COUNT",
    "CW_SUM",

    "SL",
    "SL_PERCENT", -- % обращений, по которым клиент получил ответ за заданное для очереди целевое время ASA, от общего количества обращений в очереди 

    "REPEATED", -- Повторных

    "FCR",
    COALESCE(
            (r."REGISTERED" - r."REPEATED")::numeric / NULLIF(r."REGISTERED", 0) * 100,
            0
    )::bigint AS "FCR_PERCENT", -- %FCR

    "LOST", -- Потеряно
    "LOST_PERCENT", -- %LCR

    "ESCALATED_BY_DISTRIBUTION", -- Просрочено по распределению (Кол-во обращений очереди, которые были просрочены по сроку распределения)
    "ESCALATED_BY_DECISION", -- Просрочено по решению (Кол-во обращений очереди, которые были просрочены по сроку решения)

    "ACSI", -- ACSI | ❗ Проверить логику (Уточнить у Натальи)
    "ACSI_ALARM",
    "ACSI_WARNING",
    "CSI_COUNT",
    "CSI_SUM",
    "CSI_GROUP_COUNT",
    "CSI_TOTAL_COUNT",

    "ASSI", -- ASSI
    "SSI_GROUP_COUNT",
    "SSI_COUNT",
    "SSI_SUM",

    "CSAT", -- CSAT
    "CSAT_COUNT",

    "CDSAT", -- CDSAT
    "CDSAT_COUNT"
FROM
    res AS r;

$function$;


-- MIGR "F_QUEUES_KPI_INFO"
CREATE OR REPLACE FUNCTION "CALC"."F_QUEUES_KPI_INFO"(_startdate timestamp without time zone, _enddate timestamp without time zone)
 RETURNS TABLE("ID" smallint, "TITLE" character varying, "QUEUE_TIME_DELETED" timestamp without time zone, "TOTAL_REQUESTS" bigint, "CLOSED_REQUESTS" bigint, "CLOSED_REQUESTS_BY_OPERATOR" bigint, "CLOSED_REQUESTS_BY_SUPERVISOR" bigint, "CLOSED_REQUESTS_BY_SYSTEM" bigint, "CLOSED_REQUESTS_BY_BOT" bigint, "CLOSED_REQUESTS_BY_BOT_PERCENT" numeric, "OPENED_REQUESTS" bigint, "REQUESTS_IN_PROCESSING" bigint, "ASSIGNED_TO_CHAT_BOT" bigint, "ASSIGNED_TO_OPERATOR" bigint, "ASSIGNED_TO_SUPERVISOR" bigint, "REDIRECTED" bigint, "REDIRECTED_PERCENT" numeric, "POSTPONED" bigint, "REPEATED" bigint, "FCR" bigint, "FCR_PERCENT" bigint, "TOTAL_OPERATORS" bigint, "ONLINE_OPERATORS" bigint, "FREE_OPERATORS" bigint, "PENDING_OPERATOR_RESPONSE" bigint, "SL" numeric, "SL_PERCENT" numeric, "AHT" numeric, "AWT" numeric, "ASA" numeric, "ACW" numeric, "ACSI" numeric, "ASSI" numeric, "CSAT" numeric, "CDSAT" numeric, "ASA_ALARM" numeric, "ASA_WARNING" numeric, "AHT_ALARM" numeric, "AHT_WARNING" numeric, "ACSI_ALARM" numeric, "ACSI_WARNING" numeric, "ART_ALARM" numeric, "ART_WARNING" numeric, "AVERAGE_IN_QUEUE_TIME" numeric, "LOST" bigint, "LOST_PERCENT" numeric, "VOICE_COUNT" bigint, "VOICE_LOST" bigint, "VOICE_LOST_PERCENT" numeric, "IN_QUEUE_TIME" numeric, "IN_QUEUE_TIME_COUNT" bigint, "HT_COUNT" bigint, "HT_SUM" bigint, "HT_MIN" numeric, "HT_MAX" numeric, "WT_COUNT" bigint, "WT_SUM" bigint, "SA_COUNT" bigint, "SA_SUM" bigint, "SA_MIN" numeric, "SA_MAX" numeric, "CW_COUNT" bigint, "CW_SUM" bigint, "CSI_COUNT" bigint, "CSI_SUM" bigint, "CSI_GROUP_COUNT" bigint, "SSI_GROUP_COUNT" bigint, "SSI_COUNT" bigint, "SSI_SUM" bigint, "CSI_TOTAL_COUNT" bigint, "CSAT_COUNT" bigint, "CDSAT_COUNT" bigint, "ART" numeric, "ART_SUM" numeric, "ART_COUNT" numeric, "VOICE_ART" numeric, "VOICE_ART_SUM" numeric, "VOICE_ART_COUNT" numeric, "A1RT" numeric, "A1RT_SUM" numeric, "A1RT_COUNT" bigint, "A1RT_MAX" numeric, "TOTAL_OPERATORS_ALL" bigint, "ONLINE_OPERATORS_ALL" bigint, "FREE_OPERATORS_ALL" bigint, "NOW" timestamp with time zone, "DIVISION_NAME" text, "DIVISION_ID" uuid)
 LANGUAGE sql
 STABLE
AS $function$

WITH usertypeenum AS (
    SELECT
        0 AS "Unknown",
        1 AS "Operator",
        2 AS "Supervisor",
        3 AS "Bot",
        4 AS "System"
),

     actual_status AS (
         SELECT
             "V_OPERATOR_STATUSES"."STATUS_CODE" AS "Status",
             "V_OPERATOR_STATUSES"."OPERATOR_ID" AS "OperatorId"
         FROM
             "CALC"."V_OPERATOR_STATUSES"
     ),

     redirected_info AS (
         SELECT
             "REQUEST_EVENT"."SourceQueueId",
             COUNT(
                     DISTINCT CASE
                                  WHEN "REQUEST_EVENT"."IsRedirected" = true THEN "REQUEST_EVENT"."RequestId"
                                  ELSE NULL::bigint
                 END
             ) AS "REDIRECTED_COUNT"
         FROM
             "KPI"."REQUEST_EVENT"
         WHERE
             "REQUEST_EVENT"."EventTime" BETWEEN _startDate AND _endDate
         GROUP BY
             "REQUEST_EVENT"."SourceQueueId"
     ),

     pre_total_operators AS (
         SELECT
             DISTINCT erq."ExecutorId"
         FROM
             "CALC"."CFG_ExecutorRequestQueues" erq
     ),

     total_operators AS (
         SELECT
             COUNT(erq."ExecutorId") AS "TOTAL_OPERATORS",

             COUNT(
                     CASE
                         WHEN upper(actual_status."Status"::text) = upper('InWork'::text)
                             OR upper(actual_status."Status"::text) = upper('Ready'::text) THEN erq."ExecutorId"
                         ELSE NULL::uuid
                         END
             ) AS "ONLINE_OPERATORS",

             COUNT(
                     CASE
                         WHEN upper(actual_status."Status"::text) = upper('Ready'::text) THEN erq."ExecutorId"
                         ELSE NULL::uuid
                         END
             ) AS "FREE_OPERATORS"

         FROM
             pre_total_operators erq
                 JOIN actual_status ON actual_status."OperatorId" = erq."ExecutorId"
     ),

     total_operators_per_queue AS (
         SELECT
             erq."QueueId",
             COUNT(erq."ExecutorId") AS "TOTAL_OPERATORS",

             COUNT(
                     CASE
                         WHEN upper(actual_status."Status"::text) = upper('InWork'::text)
                             OR upper(actual_status."Status"::text) = upper('Ready'::text) THEN erq."ExecutorId"
                         ELSE NULL::uuid
                         END
             ) AS "ONLINE_OPERATORS",

             COUNT(
                     CASE
                         WHEN upper(actual_status."Status"::text) = upper('Ready'::text) THEN erq."ExecutorId"
                         ELSE NULL::uuid
                         END
             ) AS "FREE_OPERATORS"
         FROM
             "CALC"."CFG_ExecutorRequestQueues" erq
                 JOIN actual_status ON actual_status."OperatorId" = erq."ExecutorId"
         GROUP BY
             erq."QueueId"
     ),

     request_with_kpi_groupby_queueid_current AS (
         SELECT
             r."QueueId",
             COUNT(1) AS "TOTAL_REQUESTS",

             COUNT(1) FILTER (
                 WHERE r."TimeClosed" IS NOT NULL
                 ) AS "CLOSED_REQUESTS",

             COUNT(1) FILTER (
                 WHERE
                 r."TimeClosed" IS NOT NULL
                     AND r_kpi."ClosedByUserType" = (
                     (
                         SELECT usertypeenum."Operator"
                         FROM usertypeenum
                     )
                 )
                 ) AS "CLOSED_REQUESTS_BY_OPERATOR",

             COUNT(1) FILTER (
                 WHERE
                 r."TimeClosed" IS NOT NULL
                     AND r_kpi."ClosedByUserType" = (
                     (
                         SELECT usertypeenum."Supervisor"
                         FROM usertypeenum
                     )
                 )
                 ) AS "CLOSED_REQUESTS_BY_SUPERVISOR",

             COUNT(1) FILTER (
                 WHERE
                 r."TimeClosed" IS NOT NULL
                     AND
                 (
                     (
                         r_kpi."ClosedByUserType" = 4 -- System
                             OR r_kpi."ClosedByUserType" = 0 -- Unknown
                         )
                         OR
                     (
                         r_kpi."ClosedByUserType" IS NULL
                             AND r_kpi."TimeBotWorkEnded" IS NULL
                         )
                     )
                 ) AS "CLOSED_BY_SYSTEM",

             COUNT(1) FILTER (
                 WHERE
                 r."TimeClosed" IS NOT NULL
                     AND
                 (
                     r_kpi."ClosedByUserType" = 3 -- Bot
                         OR
                     (
                         r_kpi."ClosedByUserType" IS NULL
                             AND r_kpi."TimeBotWorkEnded" IS NOT NULL
                         )
                     )
                 ) AS "CLOSED_BY_BOT",

             COUNT(r."RepeatedRequestId") AS "REPEATED",
             COUNT(1) FILTER (
                 WHERE
                 r."RepeatedRequestId" IS NULL
                     AND r."TimeClosed" IS NOT NULL
                 ) AS "FCR",

             100 * (
                         COUNT(1) FILTER (
                     WHERE
                     r."RepeatedRequestId" IS NULL
                         AND r."TimeClosed" IS NOT NULL
                     )
                 ) / COUNT(1) AS "FCR_PERCENT",

             COUNT(1) FILTER (
                 WHERE
                 r_kpi."SA" IS NULL
                     AND r."Status" IN (
                     (
                         SELECT rss."Id"
                         FROM "CRPM_CFG"."RequestStateStatuses" rss
                         WHERE rss."Code"::text IN (
                                                    'OPERATOR.WAIT'::text,
                                                    'CLAIM.OPERATOR.WAIT'::text)
                     )
                 )
                 ) AS "PENDING_OPERATOR_RESPONSE",

             COUNT(1) FILTER (
                 WHERE
                 (r_kpi."SA" IS NOT NULL AND r_kpi."SA" <> 0)
                     AND r_kpi."SA"::double precision <= (
                     (
                         SELECT queueasa."AlarmASA"
                         FROM "CALC"."CFG_Queue_ASA" queueasa
                         WHERE queueasa."Id" = r."QueueId"
                     )
                 )
                 ) AS "SL",

             AVG(r_kpi."HT") AS "AHT",
             COUNT(r_kpi."HT") AS "Count_HT",
             SUM(r_kpi."HT") AS "Sum_HT",

             ROUND(
                     COALESCE(
                             MIN(
                                     CASE
                                         WHEN r_kpi."HT" <> 0 THEN r_kpi."HT"
                                         ELSE NULL::integer
                                         END
                             )::numeric(10, 0),
                             0::numeric(10, 0)
                     )
             ) AS "MIN_HT",

             ROUND(
                     COALESCE(
                             max(r_kpi."HT")::numeric(10, 0),
                             0::numeric(10, 0)
                     )
             ) AS "MAX_HT",

             AVG(r_kpi."WT") AS "AWT",
             COUNT(r_kpi."WT") AS "Count_WT",
             SUM(r_kpi."WT") AS "Sum_WT",
             AVG(r_kpi."SA") AS "ASA",
             COUNT(r_kpi."SA") AS "Count_SA",
             SUM(r_kpi."SA") AS "Sum_SA",

             ROUND(
                     COALESCE(
                             MIN(
                                     CASE
                                         WHEN r_kpi."SA" <> 0 THEN r_kpi."SA"
                                         ELSE NULL::integer
                                         END
                             ),
                             0
                     )::double precision
             )::numeric(10, 0) AS "MIN_SA",

             ROUND(COALESCE(max(r_kpi."SA"), 0)::double precision)::numeric(10, 0) AS "MAX_SA",

             MIN(
                     COALESCE(
                             (
                                 SELECT queueasa."AlarmASA"
                                 FROM "CALC"."CFG_Queue_ASA" queueasa
                                 WHERE queueasa."Id" = r."QueueId"
                             ),
                             0::double precision
                     )
             ) AS "AlarmASA",

             MIN(
                     COALESCE(
                             (
                                 SELECT queueasa."WarningASA"
                                 FROM "CALC"."CFG_Queue_ASA" queueasa
                                 WHERE queueasa."Id" = r."QueueId"
                             ),
                             0::double precision
                     )
             ) AS "WarningASA",

             MIN(
                     COALESCE(
                             (
                                 SELECT queueasa."AlarmAHT"
                                 FROM "CALC"."CFG_Queue_ASA" queueasa
                                 WHERE queueasa."Id" = r."QueueId"
                             ),
                             0::double precision
                     )
             ) AS "AlarmAHT",

             MIN(
                     COALESCE(
                             (
                                 SELECT queueasa."WarningAHT"
                                 FROM "CALC"."CFG_Queue_ASA" queueasa
                                 WHERE queueasa."Id" = r."QueueId"
                             ),
                             0::double precision
                     )
             ) AS "WarningAHT",

             MIN(
                     COALESCE(
                             (
                                 SELECT queueasa."AlarmACSI"
                                 FROM "CALC"."CFG_Queue_ASA" queueasa
                                 WHERE queueasa."Id" = r."QueueId"
                             ),
                             0::double precision
                     )
             ) AS "AlarmACSI",

             MIN(
                     COALESCE(
                             (
                                 SELECT queueasa."WarningACSI"
                                 FROM "CALC"."CFG_Queue_ASA" queueasa
                                 WHERE queueasa."Id" = r."QueueId"
                             ),
                             0::double precision
                     )
             ) AS "WarningACSI",

             MIN(
                     COALESCE(
                             (
                                 SELECT queueasa."AlarmART"
                                 FROM "CALC"."CFG_Queue_ASA" queueasa
                                 WHERE queueasa."Id" = r."QueueId"
                             ),
                             0::double precision
                     )
             ) AS "AlarmART",

             MIN(
                     COALESCE(
                             (
                                 SELECT queueasa."WarningART"
                                 FROM "CALC"."CFG_Queue_ASA" queueasa
                                 WHERE queueasa."Id" = r."QueueId"
                             ),
                             0::double precision
                     )
             ) AS "WarningART",

             COALESCE(
                     AVG(
                             date_part(
                                     'epoch'::text,
                                     COALESCE(
                                             r_kpi."TimeOperatorFirstDistribution"::timestamp with time zone,
                                             CASE
                                                 WHEN r."TimeClosed" = '0001-01-01 00:00:00'::timestamp without time zone
                                                     OR r."TimeClosed" IS NULL THEN timezone('utc'::text, now())::timestamp with time zone
                                                 ELSE r."TimeClosed"::timestamp with time zone
                                                 END
                                     ) - COALESCE(
                                             r_kpi."TimeBotWorkEnded"::timestamp with time zone,
                                             r."TimeCreated"::timestamp with time zone
                                         )
                             )
                     ),
                     0::double precision
             )::numeric(20, 0) AS "AverageInQueueTime",

             COUNT(r_kpi."TimeLost") AS "LOST",

             COALESCE(
                     COUNT(r_kpi."TimeLost")::double precision / NULLIF(
                             COUNT(r_kpi."Id")::double precision,
                             0::double precision
                                                                 ) * 100::double precision,
                     0::double precision
             )::numeric(10, 0) AS "LOST_PERCENT",

             COUNT(1) FILTER (
                 WHERE r_kpi."ChannelType" = 10
                 ) AS "VOICE_COUNT",

             COALESCE(
                     COUNT(
                             CASE
                                 WHEN r_kpi."ChannelType" = 10 THEN r_kpi."TimeLost"
                                 ELSE NULL::timestamp without time zone
                                 END
                     ),
                     0::bigint
             ) AS "VOICE_LOST",

             COALESCE(
                     COUNT(
                             CASE
                                 WHEN r_kpi."ChannelType" = 10 THEN r_kpi."TimeLost"
                                 ELSE NULL::timestamp without time zone
                                 END
                     )::double precision / NULLIF(
                             COUNT(r_kpi."Id")::double precision,
                             0::double precision
                                           ) * 100::double precision,
                     0::double precision
             )::numeric(10, 0) AS "VOICE_LOST_PERCENT",

             COALESCE(
                     AVG(
                             date_part(
                                     'epoch'::text,
                                     COALESCE(
                                             r_kpi."TimeOperatorFirstDistribution"::timestamp with time zone,
                                             CASE
                                                 WHEN r."TimeClosed" = '0001-01-01 00:00:00'::timestamp without time zone
                                                     OR r."TimeClosed" IS NULL THEN timezone('utc'::text, now())::timestamp with time zone
                                                 ELSE r."TimeClosed"::timestamp with time zone
                                                 END
                                     ) - COALESCE(r_kpi."TimeBotWorkEnded", r."TimeCreated")::timestamp with time zone
                             )
                     ),
                     0::double precision
             )::numeric(10, 0) AS "IN_QUEUE_TIME",

             CASE
                 WHEN AVG(
                         date_part(
                                 'epoch'::text,
                                 COALESCE(
                                         r_kpi."TimeOperatorFirstDistribution"::timestamp with time zone,
                                         CASE
                                             WHEN r."TimeClosed" = '0001-01-01 00:00:00'::timestamp without time zone
                                                 OR r."TimeClosed" IS NULL THEN timezone('utc'::text, now())::timestamp with time zone
                                             ELSE r."TimeClosed"::timestamp with time zone
                                             END
                                 ) - COALESCE(r_kpi."TimeBotWorkEnded", r."TimeCreated")::timestamp with time zone
                         )
                      ) IS NULL THEN 0
                 ELSE 1
                 END AS "IN_QUEUE_TIME_COUNT"
         FROM
             "CRPM_DATA"."Requests" r
                 LEFT JOIN "KPI"."REQUEST_KPI" r_kpi ON r."Id" = r_kpi."Id"
         WHERE
             r."TimeRegistered" BETWEEN _startDate AND _endDate
           AND r."SchemaId" <> 3 -- division
         GROUP BY
             r."QueueId"
     ),

     request_with_kpi_groupby_queueid_opened AS (
         SELECT
             r."QueueId",

             COUNT(1) FILTER (
                 WHERE(
                          ((
                              SELECT rss."Code"
                              FROM "CRPM_CFG"."RequestStateStatuses" rss
                              WHERE rss."Id" = r."Status"))::text
                          ) = 'OPERATOR.WAIT'::text
                 ) AS "OPENED_REQUESTS",

             COUNT(1) FILTER (
                 WHERE (
                           ((
                               SELECT rss."Code"
                               FROM "CRPM_CFG"."RequestStateStatuses" rss
                               WHERE rss."Id" = r."Status"))::text
                           ) = ANY (ARRAY [
                     'CHATBOT'::character varying::text,
                     'CHATBOT.WAIT'::character varying::text])
                 ) AS "ASSIGNED_TO_CHAT_BOT",

             COUNT(1) FILTER (
                 WHERE (
                           ((
                               SELECT rss."Code"
                               FROM "CRPM_CFG"."RequestStateStatuses" rss
                               WHERE rss."Id" = r."Status"))::text
                           ) = ANY (ARRAY [
                     'OPERATOR.WORK'::character varying::text,
                     'OPERATOR.PAUSE'::character varying::text,
                     'VOICE'::character varying::text,
                     'REQUEST.PROCESSING'::character varying::text])
                 ) AS "ASSIGNED_TO_OPERATOR",

             COUNT(1) FILTER (
                 WHERE (
                           ((
                               SELECT rss."Code"
                               FROM "CRPM_CFG"."RequestStateStatuses" rss
                               WHERE rss."Id" = r."Status"))::text
                           ) = 'SUPERVISOR'::text
                 ) AS "ASSIGNED_TO_SUPERVISOR",

             COUNT(1) FILTER (
                 WHERE (
                           ((
                               SELECT rss."Code"
                               FROM "CRPM_CFG"."RequestStateStatuses" rss
                               WHERE rss."Id" = r."Status"))::text
                           ) = 'OPERATOR.POSTPONE'::text
                 ) AS "POSTPONED",

             COUNT(1) FILTER (
                 WHERE(
                          ((
                              SELECT rss."Code"
                              FROM "CRPM_CFG"."RequestStateStatuses" rss
                              WHERE rss."Id" = r."Status"))::text
                          ) != 'OPERATOR.POSTPONE'::text
                     AND (
                             ((
                                 SELECT rss."Code"
                                 FROM "CRPM_CFG"."RequestStateStatuses" rss
                                 WHERE rss."Id" = r."Status"))::text
                             ) != 'OPERATOR.WAIT'::text
                 ) AS "REQUESTS_IN_PROCESSING"
         FROM
             "CRPM_DATA"."Requests" r
                 LEFT JOIN "KPI"."REQUEST_KPI" r_kpi ON r."Id" = r_kpi."Id"
         WHERE
             r."TimeClosed" IS NULL
           AND r."TimeRegistered" BETWEEN _startDate AND _endDate
           AND r."SchemaId" <> 3 -- division
         GROUP BY
             r."QueueId"
     ),

     art_req AS (
         SELECT
             q_art."QueueId",
             SUM(q_art."sumRT") AS "sumRT",
             SUM(q_art."countRT") AS "countRT",
             SUM(q_art."sumRT") / SUM(q_art."countRT") AS "ART"
         FROM (
                  SELECT (
                             SELECT r."QueueId"
                             FROM "CRPM_DATA"."Requests" r
                             WHERE r."Id" = ore."RequestId"
                         ) AS "QueueId",
                         SUM(ore."RT") AS "sumRT",
                         COUNT(ore."RT") AS "countRT"
                  FROM
                      "KPI"."OPERATOR_RESPONSE" ore
                  WHERE
                      ore."ReactionStartDate" BETWEEN _startDate AND _endDate
                  GROUP BY
                      ore."RequestId"
              ) AS q_art
         GROUP BY
             q_art."QueueId"
     ),

     art_req_voice AS (
         SELECT
             q_art_voice."QueueId",
             SUM(q_art_voice."sumRT") AS "sumRT",
             SUM(q_art_voice."countRT") AS "countRT",
             SUM(q_art_voice."sumRT") / SUM(q_art_voice."countRT") AS "ART"
         FROM (
                  SELECT (
                             SELECT r."QueueId"
                             FROM "CRPM_DATA"."Requests" r
                             WHERE r."Id" = ore."RequestId"
                         ) AS "QueueId",
                         SUM(ore."RT") AS "sumRT",
                         COUNT(ore."RT") AS "countRT"
                  FROM
                      "KPI"."OPERATOR_RESPONSE" ore
                  WHERE
                      ore."ReactionStartDate" BETWEEN _startDate AND _endDate
                    AND ore."ChannelType" = 10
                  GROUP BY
                      ore."RequestId"
              ) AS q_art_voice
         GROUP BY
             q_art_voice."QueueId"
     ),

     a1rt_req AS (
         SELECT
             q_art."QueueId",
             AVG(q_art."RT") AS "A1RT",
             SUM(q_art."RT") AS "sumRT",
             COALESCE(max(q_art."RT"), 0::bigint)::numeric(10, 0) AS "MAX_1RT",
             COUNT(q_art."RequestId") AS "countRT"
         FROM(
                 SELECT DISTINCT ON (ore."RequestId")
                     (   SELECT r."QueueId"
                         FROM "CRPM_DATA"."Requests" r
                         WHERE r."Id" = ore."RequestId"
                     ) AS "QueueId",
                     ore."RT",
                     ore."RequestId"
                 FROM
                     "KPI"."OPERATOR_RESPONSE" ore
                 WHERE
                     ore."ReactionStartDate" BETWEEN _startDate AND _endDate
                 ORDER BY
                     ore."RequestId",
                     ore."ReactionStartDate"
             ) AS q_art
         GROUP BY
             q_art."QueueId"
     ),

     acw_info AS (
         SELECT
             r."QueueId",
             AVG(ocs."ACW") AS "ACW",
             COUNT(ocs."ACW") AS "Count_CW",
             SUM(ocs."ACW") AS "Sum_CW"
         FROM
             "KPI"."OPERATOR_CALL_STATE" ocs
                 LEFT JOIN "CRPM_DATA"."Requests" AS r ON ocs."RequestId" = r."Id"
         WHERE
             ocs."TimeCallAccepted" BETWEEN _startDate AND _endDate
         GROUP BY
             r."QueueId"
     ),

     csi_info_evaluation_pre AS ( -- Присваиваем ранг каждой записи на базе Date, чтобы выбрать последнюю оценку (rank = 1)
         SELECT
             "RequestId",
             "Score",
             RANK() OVER (PARTITION BY "RequestId" ORDER BY "Date" DESC) AS "Rank"
         FROM "EXTERNAL_EVALUATIONS"."ExternalEvaluations"
     ),

     csi_info_evaluation_result AS (
         SELECT
             "RequestId",
             "Score"
         FROM csi_info_evaluation_pre
         WHERE "Rank" = 1
     ),

     csi_info AS ( -- Подсчёт CSI
         SELECT
             r."QueueId",
             AVG(e."Score") AS "ACSI",
             SUM(e."Score") AS "Sum_CSI",
             COUNT(e."Score") AS "Count_CSI",

             COUNT(
                     DISTINCT CASE
                                  WHEN e."Score" IS NULL THEN NULL::bigint
                                  ELSE e."Score"
                 END
             ) AS "CSI_GROUP_COUNT",

             COALESCE(COUNT(e."Score"), 0::bigint) AS "TOTAL_CSI_COUNT",

             COALESCE(
                     COUNT(
                             CASE
                                 WHEN e."Score" > 4 THEN e."Score"
                                 ELSE NULL::smallint
                                 END
                     )::numeric(10, 2) / NULLIF(COUNT(e."Score"), 0)::numeric,
                     0::numeric
             ) * 100::numeric(10, 2) AS "CSAT",

             COUNT(
                     CASE
                         WHEN e."Score" > 4 THEN e."Score"
                         ELSE NULL::smallint
                         END
             ) AS "Count_CSAT",

             COALESCE(
                     COUNT(
                             CASE
                                 WHEN e."Score" < 4 THEN e."Score"
                                 ELSE NULL::smallint
                                 END
                     )::numeric(10, 2) / NULLIF(COUNT(e."Score"), 0)::numeric,
                     0::numeric
             ) * 100::numeric(10, 2) AS "CDSAT",

             COUNT(
                     CASE
                         WHEN e."Score" < 4 THEN e."Score"
                         ELSE NULL::smallint
                         END
             ) AS "Count_CDSAT"
         FROM
             csi_info_evaluation_result AS e
                 LEFT JOIN "CRPM_DATA"."Requests" AS r ON r."Id" = e."RequestId"
         WHERE
             r."SchemaId" <> 3 -- division
           AND r."TimeRegistered" BETWEEN _startDate AND _endDate
         GROUP BY r."QueueId"
     ),

     ssi_info_from_rkpi AS (
         SELECT
             "Id" AS "RequestId",
             "SSI"
         FROM
             "KPI"."REQUEST_KPI"
         WHERE
             "TimeRegistered" BETWEEN _startDate AND _endDate
     ),

     ssi_info_from_rkpi_with_queueId AS
         (
             SELECT
                 r_kpi."RequestId",
                 r."QueueId",
                 r_kpi."SSI"
             FROM
                 ssi_info_from_rkpi r_kpi
                     LEFT JOIN "CRPM_DATA"."Requests" AS r ON r."Id" = r_kpi."RequestId"
             WHERE r."SchemaId" <> 3 -- division
         ),

     ssi_info_from_eval AS (
         SELECT
             r_kpi."RequestId",
             r_kpi."QueueId",
             e."ScoreResult" AS "EvalSSI",
             r_kpi."SSI" AS "ReqSSI",
             e."PublishedAt"
         FROM
             ssi_info_from_rkpi_with_queueId AS r_kpi
                 LEFT JOIN "CALC"."V_EVALUATIONS" AS e ON r_kpi."RequestId" = (e."Context"->>'requestId')::bigint
         WHERE
             e."ScoreResult" IS NOT NULL OR r_kpi."SSI" IS NOT NULL
     ),

     ssi_info_filtered AS (
         SELECT
             "QueueId",
             "RequestId",
             (CASE
                  WHEN ("ReqSSI" IS NOT NULL AND "PublishedAt" IS NULL) THEN "ReqSSI"
                  ELSE COALESCE("EvalSSI", "ReqSSI")
                 END) AS "SSI"
         FROM
             ssi_info_from_eval
     ),

     pre_ssi_info_result AS (
         SELECT
             "QueueId",
             "RequestId",
             AVG("SSI") AS "SSI"
         FROM
             ssi_info_filtered
         GROUP BY "RequestId", "QueueId"
     ),

     ssi_info_result AS (
         SELECT
             "QueueId",
             COUNT(
                     DISTINCT CASE
                                  WHEN "SSI" IS NULL THEN NULL::bigint
                                  ELSE "RequestId"
                 END
             ) AS "SSI_GROUP_COUNT",
             AVG("SSI") AS "ASSI",
             COUNT("SSI") AS "Count_SSI",
             SUM("SSI") AS "Sum_SSI"
         FROM pre_ssi_info_result
         GROUP BY "QueueId"
     ),

     operator_root_group AS (
     	SELECT operatorgroup."Name", operatorgroup."Id", rq."Id" as "QueueId"
		FROM 
		    "CRPM_CFG"."CustomAttributes" ca
		    JOIN "CRPM_CFG"."RequestQueues" rq ON rq."Id" = ca."QueueId"
		    JOIN "AWP_INFRA"."OperatorGroups" operatorgroup ON operatorgroup."Id"::character varying::text = ca."Value"::text
		WHERE 
		    ca."Key" = 'Division'::text),
     
     res AS( -- Результирующая CTE
         SELECT
             queues."Id" AS "ID",
             queues."Title" AS "TITLE",
             queues."TimeDeleted" AS "QUEUE_TIME_DELETED",
             COALESCE(rbq."TOTAL_REQUESTS", 0::bigint) AS "TOTAL_REQUESTS",
             COALESCE(rbq."CLOSED_REQUESTS", 0::bigint) AS "CLOSED_REQUESTS",
             COALESCE(rbq."CLOSED_REQUESTS_BY_OPERATOR", 0::bigint) AS "CLOSED_REQUESTS_BY_OPERATOR",
             COALESCE(rbq."CLOSED_REQUESTS_BY_SUPERVISOR", 0::bigint) AS "CLOSED_REQUESTS_BY_SUPERVISOR",
             COALESCE(rbq."CLOSED_BY_SYSTEM", 0::bigint) AS "CLOSED_REQUESTS_BY_SYSTEM",
             COALESCE(rbq."CLOSED_BY_BOT", 0::bigint) AS "CLOSED_REQUESTS_BY_BOT",

             COALESCE(
                     rbq."CLOSED_BY_BOT"::numeric / NULLIF(rbq."CLOSED_REQUESTS"::double precision, 0::double precision)::numeric(10, 2) * 100::numeric,
                     0::numeric(10, 2)
             )::numeric(10, 2) AS "CLOSED_REQUESTS_BY_BOT_PERCENT",

             COALESCE(rbqo."OPENED_REQUESTS", 0::bigint) AS "OPENED_REQUESTS",
             COALESCE(rbqo."REQUESTS_IN_PROCESSING", 0::bigint) AS "REQUESTS_IN_PROCESSING",
             COALESCE(rbqo."ASSIGNED_TO_CHAT_BOT", 0::bigint) AS "ASSIGNED_TO_CHAT_BOT",
             COALESCE(rbqo."ASSIGNED_TO_OPERATOR", 0::bigint) AS "ASSIGNED_TO_OPERATOR",
             COALESCE(rbqo."ASSIGNED_TO_SUPERVISOR", 0::bigint) AS "ASSIGNED_TO_SUPERVISOR",

             COALESCE(
                     (
                         SELECT ri."REDIRECTED_COUNT"
                         FROM redirected_info ri
                         WHERE ri."SourceQueueId" = queues."Id"
                     ),
                     0::bigint
             ) AS "REDIRECTED",

             (
                 CASE
                     WHEN COALESCE(
                                  (
                                      SELECT ri."REDIRECTED_COUNT"
                                      FROM redirected_info ri
                                      WHERE ri."SourceQueueId" = queues."Id"
                                  ),
                                  0::bigint
                          ) = 0 THEN 0::numeric(10, 0)
                     ELSE(
                         CASE
                             WHEN rbq."TOTAL_REQUESTS" IS NULL
                                 OR rbq."TOTAL_REQUESTS" = 0 THEN 100::numeric(10, 0)
                             ELSE (
                                 CASE
                                     WHEN (
                                              (
                                                  SELECT ri."REDIRECTED_COUNT"
                                                  FROM redirected_info ri
                                                  WHERE ri."SourceQueueId" = queues."Id"
                                              )::numeric(10, 2) / rbq."TOTAL_REQUESTS"::numeric(10, 2)
                                              ) > 1 THEN 100::numeric(10, 0)
                                     ELSE (
                                         100::numeric(10, 2) * (
                                             SELECT ri."REDIRECTED_COUNT"
                                             FROM redirected_info ri
                                             WHERE ri."SourceQueueId" = queues."Id"
                                         )::numeric(10, 2) / rbq."TOTAL_REQUESTS"::numeric(10, 2)
                                         )::numeric(10, 0)
                                     END
                                 )::numeric(10, 0)
                             END
                         )::numeric(10, 0)
                     END
                 )::numeric(10, 0) AS "REDIRECTED_PERCENT",

             COALESCE(rbqo."POSTPONED", 0::bigint) AS "POSTPONED",
             COALESCE(rbq."REPEATED", 0::bigint) AS "REPEATED",
             COALESCE(rbq."FCR", 0::bigint) AS "FCR",
             COALESCE(rbq."FCR_PERCENT", 0::bigint) AS "FCR_PERCENT",
             COALESCE(total_operators_per_queue."TOTAL_OPERATORS", 0::bigint) AS "TOTAL_OPERATORS",
             COALESCE(total_operators_per_queue."ONLINE_OPERATORS", 0::bigint) AS "ONLINE_OPERATORS",
             COALESCE(total_operators_per_queue."FREE_OPERATORS", 0::bigint) AS "FREE_OPERATORS",
             COALESCE(rbq."PENDING_OPERATOR_RESPONSE", 0::bigint) AS "PENDING_OPERATOR_RESPONSE",
             COALESCE(rbq."SL"::numeric(10, 2), 0::numeric) AS "SL",

             COALESCE(
                     rbq."SL"::numeric / NULLIF(rbq."TOTAL_REQUESTS"::double precision, 0::double precision)::numeric(10, 2) * 100::numeric,
                     0::numeric(10, 2)
             )::numeric(10, 0) AS "SL_PERCENT",

             COALESCE(rbq."AHT"::numeric(10, 2), 0::numeric) AS "AHT",
             COALESCE(rbq."AWT"::numeric(10, 2), 0::numeric) AS "AWT",
             COALESCE(rbq."ASA"::numeric(15, 2), 0::numeric) AS "ASA",
             COALESCE(acwi."ACW"::numeric(10, 2), 0::numeric) AS "ACW",
             COALESCE(csii."ACSI"::numeric(10, 2), 0::numeric) AS "ACSI",
             COALESCE(ssii."ASSI"::numeric(10, 2), 0::numeric) AS "ASSI",
             COALESCE(csii."CSAT"::numeric(10, 2), 0::numeric) AS "CSAT",
             COALESCE(csii."CDSAT"::numeric(10, 2), 0::numeric) AS "CDSAT",
             COALESCE(rbq."AlarmASA", 0::double precision)::numeric(10, 2) AS "ASA_ALARM",
             COALESCE(rbq."WarningASA", 0::double precision)::numeric(10, 2) AS "ASA_WARNING",
             COALESCE(rbq."AlarmAHT", 0::double precision)::numeric(10, 2) AS "AHT_ALARM",
             COALESCE(rbq."WarningAHT", 0::double precision)::numeric(10, 2) AS "AHT_WARNING",
             COALESCE(rbq."AlarmACSI", 0::double precision)::numeric(10, 2) AS "ACSI_ALARM",
             COALESCE(rbq."WarningACSI", 0::double precision)::numeric(10, 2) AS "ACSI_WARNING",
             COALESCE(rbq."AlarmART", 0::double precision)::numeric(10, 2) AS "ART_ALARM",
             COALESCE(rbq."WarningART", 0::double precision)::numeric(10, 2) AS "ART_WARNING",
             COALESCE(rbq."AverageInQueueTime", 0::numeric) AS "AVERAGE_IN_QUEUE_TIME",
             COALESCE(rbq."LOST", 0::bigint) AS "LOST",
             COALESCE(rbq."LOST_PERCENT", 0::bigint::numeric) AS "LOST_PERCENT",
             COALESCE(rbq."VOICE_COUNT", 0::bigint) AS "VOICE_COUNT",
             COALESCE(rbq."VOICE_LOST", 0::bigint) AS "VOICE_LOST",
             COALESCE(rbq."VOICE_LOST_PERCENT", 0::bigint::numeric) AS "VOICE_LOST_PERCENT",
             COALESCE(rbq."IN_QUEUE_TIME", 0::bigint::numeric) AS "IN_QUEUE_TIME",
             COALESCE(rbq."IN_QUEUE_TIME_COUNT"::bigint, 0::bigint) AS "IN_QUEUE_TIME_COUNT",
             COALESCE(rbq."Count_HT", 0::bigint) AS "HT_COUNT",
             COALESCE(rbq."Sum_HT", 0::bigint) AS "HT_SUM",
             COALESCE(rbq."MIN_HT", 0::bigint::numeric) AS "HT_MIN",
             COALESCE(rbq."MAX_HT", 0::bigint::numeric) AS "HT_MAX",
             COALESCE(rbq."Count_WT", 0::bigint) AS "WT_COUNT",
             COALESCE(rbq."Sum_WT", 0::bigint) AS "WT_SUM",
             COALESCE(rbq."Count_SA", 0::bigint) AS "SA_COUNT",
             COALESCE(rbq."Sum_SA", 0::bigint) AS "SA_SUM",
             COALESCE(rbq."MIN_SA", 0::bigint::numeric) AS "SA_MIN",
             COALESCE(rbq."MAX_SA", 0::bigint::numeric) AS "SA_MAX",
             COALESCE(acwi."Count_CW", 0::bigint) AS "CW_COUNT",
             COALESCE(acwi."Sum_CW", 0::bigint) AS "CW_SUM",
             COALESCE(csii."Count_CSI", 0::bigint) AS "CSI_COUNT",
             COALESCE(csii."Sum_CSI"::bigint, 0::bigint) AS "CSI_SUM",
             COALESCE(csii."CSI_GROUP_COUNT", 0::bigint) AS "CSI_GROUP_COUNT",
             COALESCE(ssii."SSI_GROUP_COUNT", 0)::bigint AS "SSI_GROUP_COUNT",
             COALESCE(ssii."Count_SSI", 0)::bigint AS "SSI_COUNT",
             COALESCE(ssii."Sum_SSI", 0)::bigint AS "SSI_SUM",
             COALESCE(csii."TOTAL_CSI_COUNT", 0::bigint) AS "CSI_TOTAL_COUNT",
             COALESCE(csii."Count_CSAT", 0::bigint) AS "CSAT_COUNT",
             COALESCE(csii."Count_CDSAT", 0::bigint) AS "CDSAT_COUNT",
             COALESCE(art_req."ART"::numeric(10, 2), 0::numeric) AS "ART",
             COALESCE(art_req."sumRT", 0::bigint::numeric) AS "ART_SUM",
             COALESCE(art_req."countRT", 0::bigint::numeric) AS "ART_COUNT",
             COALESCE(art_req_voice."ART"::numeric(10, 2), 0::numeric) AS "VOICE_ART",
             COALESCE(art_req_voice."sumRT", 0::bigint::numeric) AS "VOICE_ART_SUM",
             COALESCE(art_req_voice."countRT", 0::bigint::numeric) AS "VOICE_ART_COUNT",
             COALESCE(a1rt_req."A1RT"::numeric(15, 2), 0::numeric) AS "A1RT",
             COALESCE(a1rt_req."sumRT", 0::bigint::numeric) AS "A1RT_SUM",
             COALESCE(a1rt_req."countRT", 0::bigint) AS "A1RT_COUNT",

             COALESCE(a1rt_req."MAX_1RT", 0::bigint::numeric) AS "A1RT_MAX",
             (
                 SELECT total_operators."TOTAL_OPERATORS"
                 FROM total_operators
             ) AS "TOTAL_OPERATORS_ALL",

             (
                 SELECT total_operators."ONLINE_OPERATORS"
                 FROM total_operators
             ) AS "ONLINE_OPERATORS_ALL",

             (
                 SELECT total_operators."FREE_OPERATORS"
                 FROM total_operators
             ) AS "FREE_OPERATORS_ALL",

             timezone('UTC'::text, now()::timestamp with time zone)::timestamp with time zone AS "NOW",
    		 operator_root_group."Name" AS "DIVISION_NAME",
    		 operator_root_group."Id" AS "DIVISION_ID"
         FROM
             "CALC"."CFG_RequestQueues" queues
         LEFT JOIN request_with_kpi_groupby_queueid_current rbq ON rbq."QueueId" = queues."Id"
         LEFT JOIN request_with_kpi_groupby_queueid_opened rbqo ON rbqo."QueueId" = queues."Id"
         LEFT JOIN art_req ON art_req."QueueId" = queues."Id"
         LEFT JOIN art_req_voice ON art_req_voice."QueueId" = queues."Id"
         LEFT JOIN a1rt_req ON a1rt_req."QueueId" = queues."Id"
         LEFT JOIN total_operators_per_queue ON total_operators_per_queue."QueueId" = queues."Id"
         LEFT JOIN acw_info AS acwi ON acwi."QueueId" = queues."Id"
         LEFT JOIN csi_info AS csii ON csii."QueueId" = queues."Id"
         LEFT JOIN ssi_info_result AS ssii ON ssii."QueueId" = queues."Id"
         LEFT JOIN operator_root_group ON operator_root_group."QueueId" = queues."Id"
         WHERE
            queues."IsService" = FALSE
     )

SELECT -- Всё расчитывается в рамках одной очереди
       "ID", -- Id очереди
       "TITLE", -- Название очереди
       "QUEUE_TIME_DELETED", -- Время удаление очереди (Для фильтрации)
       "TOTAL_REQUESTS", -- Всего обращений в очереди
       "CLOSED_REQUESTS", -- Закрытые Всего
       "CLOSED_REQUESTS_BY_OPERATOR", -- Закрытые Оператором
       "CLOSED_REQUESTS_BY_SUPERVISOR", -- Закрытые Супервизором
       "CLOSED_REQUESTS_BY_SYSTEM", -- Закрытые Системой
       "CLOSED_REQUESTS_BY_BOT", -- Закрытые БОТом
       "CLOSED_REQUESTS_BY_BOT_PERCENT", -- % автоматиации (Доля обращений, которые были закрыты БОТом)
       "OPENED_REQUESTS", -- Открытых обращений
       "REQUESTS_IN_PROCESSING", -- В обоаботке
       "ASSIGNED_TO_CHAT_BOT", -- Назначено на чат-бота
       "ASSIGNED_TO_OPERATOR", -- Назначено на оператора
       "ASSIGNED_TO_SUPERVISOR", -- Назначено на супервизора
       "REDIRECTED", -- Переведено по очереди
       "REDIRECTED_PERCENT", -- Переведено %
       "POSTPONED", -- Отложено
       "REPEATED", -- Повторных
       "FCR", -- 
       "FCR_PERCENT", --
       "TOTAL_OPERATORS", -- Всего операторов
       "ONLINE_OPERATORS", -- Активных операторов
       "FREE_OPERATORS", -- Свободных операторов
       "PENDING_OPERATOR_RESPONSE", -- Ожидают обработки оператора
       "SL",
       "SL_PERCENT", -- % обращений, по которым клиент получил ответ за заданное для очереди целевое время ASA, от общего количества обращений в очереди 
       "AHT",
       "AWT",
       "ASA", -- Средняя скорость ответа оператора от момента регистрации обращения / завершения обработки чат-ботом до момента первого ответа оператором
       "ACW", -- Среднее значение времени поствызовной обработки голосового вызова / видеовызова
       "ACSI", -- Средняя оценка качества обработки обращения, выставленная клиентом
       "ASSI", -- Среднее значение индекса удовлетворенности супервизора (SSI)
       "CSAT", -- Индекс удовлетворенности клиента в процентах (доля положительных оценок: CSI = 5)
       "CDSAT", -- Индекс неудовлетворенности клиента в процентах (доля отрицательных оценок: CSI = 1; 2; 3)
       "ASA_ALARM", --
       "ASA_WARNING", --
       "AHT_ALARM", --
       "AHT_WARNING", --
       "ACSI_ALARM", --
       "ACSI_WARNING", --
       "ART_ALARM", --
       "ART_WARNING", --
       "AVERAGE_IN_QUEUE_TIME", --
       "LOST", -- Потеряно
       "LOST_PERCENT", -- % Потеряно
       "VOICE_COUNT", -- Количество голосовых обращений
       "VOICE_LOST", -- Потеряно голосовых
       "VOICE_LOST_PERCENT", --
       "IN_QUEUE_TIME", --
       "IN_QUEUE_TIME_COUNT", --
       "HT_COUNT", --
       "HT_SUM", --
       "HT_MIN", --
       "HT_MAX", --
       "WT_COUNT", --
       "WT_SUM", --
       "SA_COUNT", --
       "SA_SUM", --
       "SA_MIN", --
       "SA_MAX", --
       "CW_COUNT", --
       "CW_SUM", --
       "CSI_COUNT", --
       "CSI_SUM", --
       "CSI_GROUP_COUNT", --
       "SSI_GROUP_COUNT", --
       "SSI_COUNT", --
       "SSI_SUM", --
       "CSI_TOTAL_COUNT", --
       "CSAT_COUNT", --
       "CDSAT_COUNT", --
       "ART", --
       "ART_SUM", --
       "ART_COUNT", --
       "VOICE_ART", --
       "VOICE_ART_SUM", --
       "VOICE_ART_COUNT", --
       "A1RT", --
       "A1RT_SUM", --
       "A1RT_COUNT", --
       "A1RT_MAX", --
       "TOTAL_OPERATORS_ALL", --
       "ONLINE_OPERATORS_ALL", --
       "FREE_OPERATORS_ALL", --
       "NOW", --
       "DIVISION_NAME",
       "DIVISION_ID"
FROM
    res AS r;

$function$
;


-- MIGR "F_QUEUES_KPI_INFO_V2"
CREATE OR REPLACE FUNCTION "CALC"."F_QUEUES_KPI_INFO_V2"(_startdate timestamp without time zone, _enddate timestamp without time zone)
 RETURNS TABLE("ID" smallint, "TITLE" character varying, "QUEUE_TIME_DELETED" timestamp without time zone, "TOTAL_REQUESTS" bigint, "NEW_REQUESTS" bigint, "REMAINDER" bigint, "AUTOPROCESSING" bigint, "PENDING_OPERATOR_RESPONSE" bigint, "WAITING" bigint, "CLOSED_REQUESTS" bigint, "CLOSED_REQUESTS_BY_OPERATOR" bigint, "CLOSED_REQUESTS_BY_SUPERVISOR" bigint, "CLOSED_REQUESTS_BY_BOT" bigint, "CLOSED_REQUESTS_BY_SYSTEM" bigint, "PROCESSING_BY_OPERATOR" bigint, "CLOSED_REQUESTS_BY_BOT_PERCENT" numeric, "LOST" bigint, "LOST_PERCENT" numeric, "ESCALATED_BY_DISTRIBUTION" bigint, "ESCALATED_BY_DECISION" bigint, "ADT" numeric, "MAX_DT" numeric, "ACW" numeric, "CW_COUNT" bigint, "CW_SUM" bigint, "ASA" numeric, "ASA_ALARM" numeric, "ASA_WARNING" numeric, "SA_COUNT" bigint, "SA_SUM" bigint, "SA_MIN" numeric, "SA_MAX" numeric, "AHT" numeric, "AHT_ALARM" numeric, "AHT_WARNING" numeric, "HT_COUNT" bigint, "HT_SUM" bigint, "HT_MIN" numeric, "HT_MAX" numeric, "ART" numeric, "ART_ALARM" numeric, "ART_WARNING" numeric, "ART_SUM" numeric, "ART_COUNT" numeric, "A1RT" numeric, "A1RT_SUM" numeric, "A1RT_COUNT" bigint, "A1RT_MAX" numeric, "ACSI" numeric, "ACSI_ALARM" numeric, "ACSI_WARNING" numeric, "CSI_COUNT" bigint, "CSI_SUM" bigint, "CSI_GROUP_COUNT" bigint, "CSI_TOTAL_COUNT" bigint, "ASSI" numeric, "SSI_GROUP_COUNT" bigint, "SSI_COUNT" bigint, "SSI_SUM" bigint, "CSAT" numeric, "CSAT_COUNT" bigint, "CDSAT" numeric, "CDSAT_COUNT" bigint, "REDIRECTED" bigint, "REDIRECTED_PERCENT" numeric, "SL" numeric, "SL_PERCENT" numeric, "NOW" timestamp with time zone, "MAX_QUEUE_LENGTH" numeric, "CLOSED_REQUESTS_BY_OPERATOR_LAST_HOUR" bigint, "ASA_LAST_HOUR" numeric, "SA_COUNT_LAST_HOUR" bigint, "SA_SUM_LAST_HOUR" bigint, "LOST_LAST_HOUR" numeric, "PENDING_FIRST_OPERATOR_RESPONSE" bigint, "CURRENT_MAX_SA" bigint, "ACW_ALARM" numeric, "ACW_WARNING" numeric, "QWT_ALARM" numeric, "QWT_WARNING" numeric, "TST_ALARM" numeric, "TST_WARNING" numeric, "SIT_ALARM" numeric, "SIT_WARNING" numeric, "DIVISION_NAME" text, "DIVISION_ID" uuid, "WT_COUNT" bigint, "WT_SUM" bigint, "REQUEST_LIFETIME_COUNT" bigint, "REQUEST_LIFETIME_SUM" bigint)
 LANGUAGE sql
 STABLE
AS $function$
WITH queuesnew as (SELECT rq."Id",
                          rq."Title",
                          rq."Description",
                          rq."TimeDeleted"
                   FROM "CRPM_CFG"."RequestQueues" rq
                            LEFT JOIN "CRPM_CFG"."CustomAttributes" ca ON rq."Id" = ca."QueueId" AND ca."Key" = 'Queue.IsService'
                   WHERE
                       LOWER(ca."Value") = 'false' OR ca."Value" IS NULL),
     redirected_info AS (SELECT "REQUEST_EVENT"."SourceQueueId",
                                count(
                                        DISTINCT CASE
                                                     WHEN "REQUEST_EVENT"."IsRedirected" = true
                                                         THEN "REQUEST_EVENT"."RequestId"
                                                     ELSE NULL::bigint
                                    END
                                ) AS "REDIRECTED_COUNT"
                         FROM "KPI"."REQUEST_EVENT"
                         WHERE "REQUEST_EVENT"."EventTime" BETWEEN (_STARTDATE) AND (_ENDDATE)
                         GROUP BY "REQUEST_EVENT"."SourceQueueId"),
     requests_filtered_opened AS (SELECT r."Id", r."QueueId", r."TimeClosed", r."Status", r."State", r."SchemaId"
                                  FROM "CRPM_DATA"."Requests" r
                                  WHERE r."TimeClosed" IS NULL and r."SchemaId" <> 3),

     requests_filtered_closed AS (SELECT r."Id", r."QueueId", r."TimeClosed", r."Status", r."State", r."SchemaId"
                                  FROM "CRPM_DATA"."Requests" r
                                  WHERE r."TimeClosed" BETWEEN _STARTDATE AND _ENDDATE and r."SchemaId" <> 3),

     requests_un AS (SELECT *
                     FROM requests_filtered_opened
                     UNION ALL
                     SELECT *
                     FROM requests_filtered_closed),

     kpi_request_cte AS (SELECT *
                         FROM "KPI"."REQUEST_KPI" rkpi
                         WHERE rkpi."TimeClosed" IS NULL
                         UNION ALL
                         SELECT *
                         FROM "KPI"."REQUEST_KPI" rkpi
                         WHERE rkpi."TimeClosed" BETWEEN (_STARTDATE) AND (_ENDDATE)),

     art_req_kpi AS (SELECT *
                     FROM "KPI"."OPERATOR_RESPONSE" ore
                     WHERE ore."ReactionStartDate" BETWEEN (_STARTDATE) AND (_ENDDATE)),
     acw_kpi_info AS (SELECT *
                      FROM "KPI"."OPERATOR_CALL_STATE" ocs
                      WHERE ocs."TimeCallAccepted" BETWEEN (_STARTDATE) AND (_ENDDATE)),

     operator_root_group AS (
         SELECT operatorgroup."Name", operatorgroup."Id", rq."Id" as "QueueId"
         FROM
             "CRPM_CFG"."CustomAttributes" ca
                 JOIN "CRPM_CFG"."RequestQueues" rq ON rq."Id" = ca."QueueId"
                 JOIN "AWP_INFRA"."OperatorGroups" operatorgroup ON operatorgroup."Id"::character varying::text = ca."Value"::text
         WHERE
             ca."Key" = 'Division'::text),

     res AS (SELECT queues."Id"                                                                        AS "ID",
                    queues."Title"                                                                     AS "TITLE",
                    queues."TimeDeleted"                                                               as "QUEUE_TIME_DELETED",
                    COALESCE(rbq."TOTAL_REQUESTS", 0::bigint)                                          AS "TOTAL_REQUESTS",
                    COALESCE(rbq."NEW_REQUESTS", 0::bigint)                                            AS "NEW_REQUESTS",
                    COALESCE(rbq."REMAINDER", 0::bigint)                                               AS "REMAINDER",
                    COALESCE(rbq."AUTOPROCESSING", 0::bigint)                                          AS "AUTOPROCESSING",
                    COALESCE(rbq."PENDING_OPERATOR_RESPONSE", 0::bigint)                               AS "PENDING_OPERATOR_RESPONSE",
                    COALESCE(rbq."WAITING", 0::bigint)                                                 AS "WAITING",
                    COALESCE(rbq."CLOSED_REQUESTS", 0::bigint)                                         AS "CLOSED_REQUESTS",
                    COALESCE(rbq."CLOSED_REQUESTS_BY_OPERATOR", 0::bigint)                             AS "CLOSED_REQUESTS_BY_OPERATOR",
                    COALESCE(rbq."CLOSED_REQUESTS_BY_OPERATOR_LAST_HOUR", 0::bigint)                   AS "CLOSED_REQUESTS_BY_OPERATOR_LAST_HOUR",
                    COALESCE(rbq."CLOSED_REQUESTS_BY_SUPERVISOR", 0::bigint)                           AS "CLOSED_REQUESTS_BY_SUPERVISOR",
                    COALESCE(rbq."CLOSED_BY_BOT", 0::bigint)                                           AS "CLOSED_REQUESTS_BY_BOT",
                    COALESCE(rbq."CLOSED_BY_SYSTEM", 0::bigint)                                        AS "CLOSED_REQUESTS_BY_SYSTEM",
                    COALESCE(rbq."PROCESSING_BY_OPERATOR", 0::bigint)                                  AS "PROCESSING_BY_OPERATOR",
                    COALESCE(
                            rbq."CLOSED_BY_BOT"::numeric / NULLIF(rbq."CLOSED_REQUESTS"::double precision,
                                                                  0::double precision)::numeric(10, 2) *
                            100::numeric,
                            0::numeric(10, 2)
                    )::numeric(10, 2)                                                              AS "CLOSED_REQUESTS_BY_BOT_PERCENT",
                    COALESCE(rbq."LOST", 0::bigint)                                                    AS "LOST",
                    COALESCE(rbq."LOST_LAST_HOUR"::numeric, 0::numeric)                                AS "LOST_LAST_HOUR",
                    COALESCE(rbq."LOST_PERCENT"::numeric, 0::numeric)                                  AS "LOST_PERCENT",
                    COALESCE(rbq."AverageInQueueTime", 0::numeric)                                     AS "ADT",
                    COALESCE(rbq."MaxInQueueTime", 0::numeric)                                         AS "MAX_DT",
                    COALESCE(rbq."ACW"::numeric(10, 2), 0::numeric)                                    AS "ACW",
                    COALESCE(rbq."Count_CW", 0::bigint)                                                AS "CW_COUNT",
                    COALESCE(rbq."Sum_CW", 0::bigint)                                                  AS "CW_SUM",
                    COALESCE(rbq."ASA"::numeric(10, 2), 0::numeric)                                    AS "ASA",
                    COALESCE(rbq."ASA_LAST_HOUR"::numeric(10, 2), 0::numeric)                          AS "ASA_LAST_HOUR",
                    COALESCE(rbq."Count_SA", 0::bigint)                                                AS "SA_COUNT",
                    COALESCE(rbq."Sum_SA", 0::bigint)                                                  AS "SA_SUM",
                    COALESCE(rbq."SA_SUM_LAST_HOUR", 0::bigint)                                        AS "SA_SUM_LAST_HOUR",
                    COALESCE(rbq."SA_COUNT_LAST_HOUR", 0::bigint)                                      AS "SA_COUNT_LAST_HOUR",
                    COALESCE(rbq."MIN_SA", 0::bigint::numeric)                                         AS "SA_MIN",
                    COALESCE(rbq."MAX_SA", 0::bigint::numeric)                                         AS "SA_MAX",
                    COALESCE(rbq."AHT"::numeric(10, 2), 0::numeric)                                    AS "AHT",
                    COALESCE(rbq."Count_HT", 0::bigint)                                                AS "HT_COUNT",
                    COALESCE(rbq."Sum_HT", 0::bigint)                                                  AS "HT_SUM",
                    COALESCE(rbq."MIN_HT", 0::bigint::numeric)                                         AS "HT_MIN",
                    COALESCE(rbq."MAX_HT", 0::bigint::numeric)                                         AS "HT_MAX",
                    COALESCE(art_req."ART"::numeric(15, 2), 0::numeric)                                AS "ART",
                    COALESCE(art_req."sumRT", 0::bigint::numeric)                                      AS "ART_SUM",
                    COALESCE(art_req."countRT", 0::bigint::numeric)                                    AS "ART_COUNT",
                    COALESCE(art_req."A1RT"::numeric(15, 2), 0::numeric)                               AS "A1RT",
                    COALESCE(art_req."sum1RT", 0::bigint::numeric)                                     AS "A1RT_SUM",
                    COALESCE(art_req."count1RT", 0::bigint)                                            AS "A1RT_COUNT",
                    COALESCE(art_req."MAX_1RT", 0::bigint::numeric)                                    AS "A1RT_MAX",
                    COALESCE(rbq."ACSI"::numeric(10, 2), 0::numeric)                                   AS "ACSI",
                    COALESCE(rbq."Count_CSI"::bigint, 0::bigint)                                       AS "CSI_COUNT",
                    COALESCE(rbq."Sum_CSI"::bigint, 0::bigint)                                         AS "CSI_SUM",
                    COALESCE(rbq."CSI_GROUP_COUNT", 0::bigint)                                         AS "CSI_GROUP_COUNT",
                    COALESCE(rbq."TOTAL_CSI_COUNT", 0::bigint)                                         AS "CSI_TOTAL_COUNT",
                    COALESCE(rbq."ASSI"::numeric(10, 2), 0::numeric)                                   AS "ASSI",
                    COALESCE(rbq."SSI_GROUP_COUNT", 0)::bigint                                         AS "SSI_GROUP_COUNT",
                    COALESCE(rbq."Count_SSI", 0)::bigint                                               AS "SSI_COUNT",
                    COALESCE(rbq."Sum_SSI", 0)::bigint                                                 AS "SSI_SUM",
                    COALESCE(rbq."CSAT"::numeric(10, 2), 0::numeric)                                   AS "CSAT",
                    COALESCE(rbq."Count_CSAT", 0::bigint)                                              AS "CSAT_COUNT",
                    COALESCE(rbq."CDSAT"::numeric(10, 2), 0::numeric)                                  AS "CDSAT",
                    COALESCE(rbq."Count_CDSAT", 0::bigint)                                             AS "CDSAT_COUNT",
                    COALESCE(
                            (SELECT ri."REDIRECTED_COUNT"
                             FROM redirected_info ri
                             WHERE ri."SourceQueueId" = queues."Id"),
                            0::bigint
                    )                                                                                  AS "REDIRECTED",
                    COALESCE(rbq."Count_RequestLifeTime", 0::bigint)                                   AS "REQUEST_LIFETIME_COUNT",
                    COALESCE(rbq."Sum_RequestLifeTime", 0::bigint)                                     AS "REQUEST_LIFETIME_SUM",
                    COALESCE(rbq."Count_WT", 0::bigint)                                                AS "WT_COUNT",
                    COALESCE(rbq."Sum_WT", 0::bigint)                                                  AS "WT_SUM",
                    (
                        CASE
                            WHEN COALESCE(
                                         (SELECT ri."REDIRECTED_COUNT"
                                          FROM redirected_info ri
                                          WHERE ri."SourceQueueId" = queues."Id"), 0::bigint) = 0 THEN 0::numeric(10, 0)
                            ELSE (
                                CASE
                                    WHEN rbq."TOTAL_REQUESTS" IS NULL
                                        OR rbq."TOTAL_REQUESTS" = 0 THEN 100::numeric(10, 0)
                                    ELSE (
                                        CASE
                                            WHEN (
                                                     (SELECT ri."REDIRECTED_COUNT"
                                                      FROM redirected_info ri
                                                      WHERE ri."SourceQueueId" = queues."Id")::numeric(10, 2) /
                                                     rbq."TOTAL_REQUESTS"::numeric(10, 2)
                                                     ) > 1 THEN 100::numeric(10, 0)
                                            ELSE (
                                                100::numeric(10, 2) * (SELECT ri."REDIRECTED_COUNT"
                                                                       FROM redirected_info ri
                                                                       WHERE ri."SourceQueueId" = queues."Id")::numeric(10, 2) /
                                                rbq."TOTAL_REQUESTS"::numeric(10, 2)
                                                )::numeric(10, 0)
                                            END
                                        )::numeric(10, 0)
                                    END
                                )::numeric(10, 0)
                            END
                        )::numeric(10, 0)                                                              AS "REDIRECTED_PERCENT",
                    COALESCE(rbq."SL"::numeric(10, 2), 0::numeric)                                     AS "SL",
                    COALESCE(
                            rbq."SL"::numeric /
                            NULLIF(rbq."TOTAL_REQUESTS"::double precision, 0::double precision)::numeric(10, 2) *
                            100::numeric,
                            0::numeric(10, 2)
                    )::numeric(10, 0)                                                              AS "SL_PERCENT",
                    timezone('UTC'::text, now()::timestamp with time zone)::timestamp with time zone AS "NOW",
                    COALESCE(rbq."ESCALATED_BY_DISTRIBUTION", 0::bigint)                               AS "ESCALATED_BY_DISTRIBUTION",
                    COALESCE(rbq."ESCALATED_BY_DECISION", 0::bigint)                                   AS "ESCALATED_BY_DECISION",
                    COALESCE(rbq."PENDING_FIRST_OPERATOR_RESPONSE", 0::bigint)                         AS "PENDING_FIRST_OPERATOR_RESPONSE",
                    COALESCE(rbq."CURRENT_MAX_SA"::bigint, 0::bigint)                                  AS "CURRENT_MAX_SA",
                    alarms_queue."ACW_ALARM"::numeric,
                    alarms_queue."ACW_WARNING"::numeric,

                    alarms_queue."ART_ALARM"::numeric,
                    alarms_queue."ART_WARNING"::numeric,

                    alarms_queue."ACSI_ALARM"::numeric,
                    alarms_queue."ACSI_WARNING"::numeric,

                    alarms_queue."ASA_ALARM"::numeric,
                    alarms_queue."ASA_WARNING"::numeric,

                    alarms_queue."AHT_ALARM"::numeric,
                    alarms_queue."AHT_WARNING"::numeric,

                    alarms_queue."QWT_ALARM"::numeric,
                    alarms_queue."QWT_WARNING"::numeric,

                    alarms_queue."TST_ALARM"::numeric,
                    alarms_queue."TST_WARNING"::numeric,

                    alarms_queue."SIT_ALARM"::numeric,
                    alarms_queue."SIT_WARNING"::numeric,
                    alarms_queue."MAX_QUEUE_LENGTH"::numeric,

                    operator_root_group."Name" AS "DIVISION_NAME",
                    operator_root_group."Id" AS "DIVISION_ID"
             FROM queuesnew AS queues
                      LEFT JOIN LATERAL (
                 SELECT count(1) AS     "TOTAL_REQUESTS",
                        count(1) FILTER (
                            WHERE
                            r_kpi."TimeRegistered" >=
                            _STARTDATE
                            )    AS     "NEW_REQUESTS",
                        count(1) FILTER (
                            WHERE
                            r_kpi."TimeRegistered" < _STARTDATE
                            )    AS     "REMAINDER",
                        count(1) FILTER (
                            WHERE
                            r."TimeClosed" IS NOT NULL
                            )    AS     "CLOSED_REQUESTS",
                        count(1) FILTER (
                            WHERE
                            r."TimeClosed" IS NOT NULL AND
                            r_kpi."ClosedByUserType" = 1 -- Operator
                            )    AS     "CLOSED_REQUESTS_BY_OPERATOR",
                        count(1) FILTER (
                            WHERE
                            r."TimeClosed" IS NOT NULL
                                AND r_kpi."ClosedByUserType" = 1 /*Operator*/
                                AND r_kpi."TimeClosed" >=
                                    _ENDDATE -
                                    '1 hour '::interval
                            ) AS "CLOSED_REQUESTS_BY_OPERATOR_LAST_HOUR",
                        count(1) FILTER (
                            WHERE
                            r."TimeClosed" IS NOT NULL AND
                            r_kpi."ClosedByUserType" = 2 -- Supervisor
                            ) AS "CLOSED_REQUESTS_BY_SUPERVISOR",
                        count(1) FILTER (
                            WHERE
                            r."TimeClosed" IS NOT NULL
                                AND
                            (
                                (
                                    r_kpi."ClosedByUserType" = 4 -- System
                                        OR r_kpi."ClosedByUserType" = 0 -- Unknown
                                    )
                                    OR
                                (
                                    r_kpi."ClosedByUserType" IS NULL
                                        AND r_kpi."TimeBotWorkEnded" IS NULL
                                    )
                                )
                            ) AS "CLOSED_BY_SYSTEM",
                        count(1) FILTER (
                            WHERE
                            r."TimeClosed" IS NOT NULL
                                AND
                            (
                                r_kpi."ClosedByUserType" = 3 -- Bot
                                    OR
                                (
                                    r_kpi."ClosedByUserType" IS NULL
                                        AND r_kpi."TimeBotWorkEnded" IS NOT NULL
                                    )
                                )
                            ) AS "CLOSED_BY_BOT",
                        count(1) FILTER (
                            WHERE r."Status" IN (
                                (SELECT rss."Id"
                                 FROM "CRPM_CFG"."RequestStateStatuses" rss
                                 WHERE rss."Code"::text IN (
                                                            'OPERATOR.WAIT'::text,
                                                            'CLAIM.OPERATOR.WAIT'::text)))
                            ) AS "PENDING_OPERATOR_RESPONSE",
                        count(1) FILTER (
                            WHERE r."Status" IN (
                                (SELECT rss."Id"
                                 FROM "CRPM_CFG"."RequestStateStatuses" rss
                                 WHERE rss."Code"::text IN (
                                                            'OPERATOR.WAIT'::text,
                                                            'CLAIM.OPERATOR.WAIT'::text))) AND r_kpi."SA" = 0
                            ) AS "PENDING_FIRST_OPERATOR_RESPONSE",
                        avg(r_kpi."HT") FILTER ( WHERE r_kpi."HT" <> 0 ) AS "AHT",
                        count(r_kpi."HT") FILTER ( WHERE r_kpi."HT" <> 0 ) AS "Count_HT",
                        sum(r_kpi."HT") AS "Sum_HT",
                        round(
                                COALESCE(
                                        min(
                                                CASE
                                                    WHEN r_kpi."HT" <> 0 THEN r_kpi."HT"
                                                    ELSE NULL::integer
                                                    END
                                        )::numeric(10, 0),
                                        0::numeric(10, 0)
                                )
                        ) AS "MIN_HT",
                        round(max(r_kpi."HT")::numeric(10, 0)) AS "MAX_HT",
                        avg(r_kpi."SA") FILTER (WHERE r."TimeClosed" IS NOT NULL AND
                                                      (r_kpi."TimeOperatorFirstResponse" IS NULL OR
                                                       r_kpi."TimeOperatorFirstResponse" BETWEEN _STARTDATE AND _ENDDATE)) AS "ASA",
                        avg(r_kpi."SA") FILTER (
                            WHERE r_kpi."TimeRegistered" >=
                                  _ENDDATE -
                                  '1 hour'::interval AND (r."TimeClosed" IS NOT NULL AND (r_kpi."TimeOperatorFirstResponse" IS NULL OR r_kpi."TimeOperatorFirstResponse" BETWEEN _STARTDATE AND _ENDDATE))) AS "ASA_LAST_HOUR",
                        sum(r_kpi."SA")  FILTER (
                            WHERE r_kpi."TimeRegistered" >= _ENDDATE - '1 hour'::interval AND (r."TimeClosed" IS NOT NULL AND (r_kpi."TimeOperatorFirstResponse" IS NULL OR r_kpi."TimeOperatorFirstResponse" BETWEEN _STARTDATE AND _ENDDATE))
                            ) AS "SA_SUM_LAST_HOUR",
                        count(r_kpi."SA")  FILTER (
                            WHERE r_kpi."TimeRegistered" >= _ENDDATE - '1 hour'::interval AND (r."TimeClosed" IS NOT NULL AND (r_kpi."TimeOperatorFirstResponse" IS NULL OR r_kpi."TimeOperatorFirstResponse" BETWEEN _STARTDATE AND _ENDDATE))
                            ) AS "SA_COUNT_LAST_HOUR",
                        count(r_kpi."SA") FILTER (WHERE r."TimeClosed" IS NOT NULL AND (r_kpi."TimeOperatorFirstResponse" IS NULL OR r_kpi."TimeOperatorFirstResponse" BETWEEN _STARTDATE AND _ENDDATE)) AS "Count_SA",
                        sum(r_kpi."SA") FILTER (WHERE r."TimeClosed" IS NOT NULL AND (r_kpi."TimeOperatorFirstResponse" IS NULL OR r_kpi."TimeOperatorFirstResponse" BETWEEN _STARTDATE AND _ENDDATE)) AS "Sum_SA",
                        round((min(CASE WHEN r_kpi."SA" <> 0 THEN r_kpi."SA" ELSE NULL::integer END) FILTER (WHERE r."TimeClosed" IS NOT NULL AND (r_kpi."TimeOperatorFirstResponse" IS NULL OR r_kpi."TimeOperatorFirstResponse" BETWEEN _STARTDATE AND _ENDDATE))) ::double precision)::numeric(10, 0) AS "MIN_SA",
                        round((max(r_kpi."SA") FILTER (WHERE r."TimeClosed" IS NOT NULL AND (r_kpi."TimeOperatorFirstResponse" IS NULL OR r_kpi."TimeOperatorFirstResponse" BETWEEN _STARTDATE AND _ENDDATE)))::double precision)::numeric(10, 0) AS "MAX_SA",
                        MAX( -- Считаем только "тикающий SA". Для отсальных возвращаем 0
                                CASE
                                    WHEN
                                        r_kpi."TimeOperatorFirstResponse" IS NULL
                                            AND r."TimeClosed" IS NULL
                                            AND r_kpi."TimeCreated" IS NOT NULL
                                        THEN
                                        CASE
                                            WHEN r."Status" IN (SELECT rss."Id" FROM "CRPM_CFG"."RequestStateStatuses" rss WHERE rss."Code"::text IN ('CHATBOT'::text, 'CHATBOT.WAIT'::text, 'AUTOPROCESSING'::text)) THEN 0
                                            WHEN r_kpi."TimeBotWorkEnded" IS NOT NULL THEN date_part('epoch'::text, timezone('utc'::text, now())::timestamp with time zone - r_kpi."TimeBotWorkEnded"::timestamp with time zone)
                                            ELSE
                                                date_part('epoch'::text, timezone('utc'::text, now())::timestamp with time zone - r_kpi."TimeCreated"::timestamp with time zone)
                                            END
                                    ELSE 0
                                    END
                        )::bigint AS "CURRENT_MAX_SA",
                        avg( -- Глянуть
                                date_part(
                                        'epoch'::text,
                                        COALESCE(
                                                r_kpi."TimeOperatorFirstDistribution"::timestamp with time zone,
                                                CASE
                                                    WHEN r."TimeClosed" IS NULL THEN timezone('utc'::text, now())::timestamp with time zone
                                                    ELSE r."TimeClosed"::timestamp with time zone
                                                    END
                                        ) - COALESCE(
                                                r_kpi."TimeBotWorkEnded"::timestamp with time zone,
                                                r_kpi."TimeCreated"::timestamp with time zone
                                            )
                                )
                        )::numeric(20, 0) AS "AverageInQueueTime",
                        max( -- Глянуть
                                date_part(
                                        'epoch'::text,
                                        COALESCE(
                                                r_kpi."TimeOperatorFirstDistribution"::timestamp with time zone,
                                                CASE
                                                    WHEN r."TimeClosed" IS NULL THEN timezone('utc'::text, now())::timestamp with time zone
                                                    ELSE r."TimeClosed"::timestamp with time zone
                                                    END
                                        ) - COALESCE(
                                                r_kpi."TimeBotWorkEnded"::timestamp with time zone,
                                                r_kpi."TimeCreated"::timestamp with time zone
                                            )
                                )
                        )::numeric(20, 0) AS "MaxInQueueTime",
                        count(r_kpi."TimeLost") AS "LOST",
                        count(r_kpi."TimeLost") FILTER (
                            WHERE r_kpi."TimeLost" >= _ENDDATE - '1 hour'::interval
                            ) AS "LOST_LAST_HOUR",
                        count(r_kpi."TimeLost")::double precision / NULLIF(
                                count(r_kpi."Id")::double precision,
                                0::double precision
                                                                    ) * 100::double precision::numeric(10, 0) AS "LOST_PERCENT",
                        count(1) FILTER (
                            WHERE r."Status" IN (
                                SELECT rss."Id"
                                FROM "CRPM_CFG"."RequestStateStatuses" rss
                                WHERE rss."Code"::text = ANY (
                                    ARRAY ['OPERATOR.WORK'::character varying::text, 'OPERATOR.PAUSE'::character varying::text,'CLAIM.OPERATOR.WORK'::character varying::text,'CLAIM.OPERATOR.PAUSE'::character varying::text ]
                                    ))
                            ) AS "PROCESSING_BY_OPERATOR",
                        count(1) FILTER (
                            WHERE r."Status" IN (
                                SELECT rss."Id"
                                FROM "CRPM_CFG"."RequestStateStatuses" rss
                                WHERE rss."Code"::text = ANY (
                                    ARRAY ['OPERATOR.POSTPONE'::character varying::text, 'DIVISION.WAIT'::character varying::text, 'OPERATOR.WAIT.CLIENT'::character varying::text, 'SUPERVISOR'::character varying::text]
                                    ))
                            ) AS "WAITING",
                        count(1) FILTER (
                            WHERE r."State" IS NULL OR r."Status" IN
                                                       (
                                                           SELECT rss."Id"
                                                           FROM "CRPM_CFG"."RequestStateStatuses" rss
                                                           WHERE rss."Code"::text =ANY (
                                                               ARRAY [
                                                                   'AUTOPROCESSING'::character varying::text,
                                                                   'THEMES.WAIT'::character varying::text,
                                                                   'PVOO.SEND'::character varying::text,
                                                                   'REROUTING'::character varying::text,
                                                                   'CHATBOT'::character varying::text,
                                                                   'CHATBOT.WAIT'::character varying::text,
                                                                   'THEMES'::character varying::text,
                                                                   'SCRIPTBOT.WAIT'::character varying::text,
                                                                   'SCRIPTBOT.PROCESSING'::character varying::text]
                                                               )
                                                       )
                            ) AS "AUTOPROCESSING",
                        COUNT(1) FILTER (
                            WHERE
                            r_kpi."SA" IS NOT NULL
                                AND r_kpi."SA"::double precision <= (SELECT queueasa."AlarmASA" FROM "CALC"."CFG_Queue_ASA" AS queueasa WHERE queueasa."Id" = r."QueueId")
                            ) AS "SL",
                        COUNT(1) FILTER (
                            WHERE
                            bulk_requests_view."RequestOverdue" = 1
                            ) AS "ESCALATED_BY_DISTRIBUTION",
                        COUNT(1) FILTER (
                            WHERE
                            bulk_requests_view."RequestDecisionOverdue" = 1
                            ) AS "ESCALATED_BY_DECISION",
                        AVG(ocs."ACW") AS "ACW",
                        COUNT(ocs."ACW") AS "Count_CW",
                        SUM(ocs."ACW") AS "Sum_CW",
                        AVG(rkpi."Score") AS "ACSI",
                        SUM(rkpi."Score"::bigint) AS "Sum_CSI",
                        COUNT(rkpi."Score"::bigint) AS "Count_CSI",
                        COUNT(
                                DISTINCT CASE
                                             WHEN rkpi."Score" IS NULL THEN NULL::bigint
                                             ELSE rkpi."Score"
                            END
                        ) AS "CSI_GROUP_COUNT",
                        COALESCE(COUNT(rkpi."Score"), 0::bigint) AS "TOTAL_CSI_COUNT",
                        COALESCE(
                                COUNT(
                                        CASE
                                            WHEN rkpi."Score" > 4 THEN rkpi."Score"
                                            ELSE NULL::smallint
                                            END
                                )::numeric(10, 2) / NULLIF(COUNT(rkpi."Score"), 0)::numeric,
                                0::numeric
                        ) * 100::numeric(10, 2) AS "CSAT",
                        COUNT(
                                CASE
                                    WHEN rkpi."Score" > 4 THEN rkpi."Score"
                                    ELSE NULL::smallint
                                    END
                        ) AS "Count_CSAT",
                        COALESCE(
                                COUNT(
                                        CASE
                                            WHEN rkpi."Score" < 4 THEN rkpi."Score"
                                            ELSE NULL::smallint
                                            END
                                )::numeric(10, 2) / NULLIF(COUNT(rkpi."Score"), 0)::numeric,
                                0::numeric
                        ) * 100::numeric(10, 2) AS "CDSAT",
                        COUNT(
                                CASE
                                    WHEN rkpi."Score" < 4 THEN rkpi."Score"
                                    ELSE NULL::smallint
                                    END
                        ) AS "Count_CDSAT",
                        (SELECT
                             COUNT(
                                     DISTINCT CASE
                                                  WHEN psir."SSI" IS NULL THEN NULL::bigint
                                                  ELSE psir."RequestId"
                                 END
                             )) AS "SSI_GROUP_COUNT",
                        AVG(psir."SSI") AS "ASSI",
                        COUNT(psir."SSI") AS "Count_SSI",
                        SUM(psir."SSI") AS "Sum_SSI",
                        count(r."Id") FILTER (WHERE r."TimeClosed" IS NOT NULL) AS "Count_RequestLifeTime",
                        sum(
                        date_part('epoch',
                                  CASE
                                      WHEN r_kpi."TimeClosed" IS NULL THEN timezone('utc'::text, now())::timestamp with time zone
                                      ELSE r_kpi."TimeClosed"::timestamp with time zone
                                      END - r_kpi."TimeCreated"::timestamp with time zone
                        )
                           ) FILTER (WHERE r_kpi."TimeClosed" IS NOT NULL) AS "Sum_RequestLifeTime",
                        count(r_kpi."WT") FILTER ( WHERE r_kpi."WT" <> 0 ) AS "Count_WT",
                        sum(r_kpi."WT") AS "Sum_WT"
                 FROM
                     requests_un r
                         LEFT JOIN LATERAL (SELECT * FROM kpi_request_cte r_k WHERE r."Id" = r_k."Id") r_kpi
                                   ON TRUE
                         LEFT JOIN LATERAL (SELECT * FROM "CALC"."BULK_REQUESTSVIEW" brv WHERE r."Id" = brv."Id") bulk_requests_view ON TRUE
                         LEFT JOIN LATERAL (SELECT * FROM acw_kpi_info WHERE acw_kpi_info."RequestId" = r."Id") ocs ON TRUE
                         LEFT JOIN LATERAL (SELECT ee."Score"
                                            FROM "EXTERNAL_EVALUATIONS"."ExternalEvaluations" ee
                                            WHERE ee."RequestId" = r."Id" AND r."SchemaId" <> 3 AND ee."OperatorId" IS NOT NULL
                                            ORDER BY ee."Date" DESC LIMIT 1) rkpi ON TRUE
                         LEFT JOIN LATERAL (SELECT
                                                (CASE
                                                     WHEN (r_kpi."SSI" IS NOT NULL AND "PublishedAt" IS NULL) THEN r_kpi."SSI"
                                                     ELSE COALESCE (e."ScoreResult", r_kpi."SSI")
                                                    END) AS "SSI",
                                                r."Id" as "RequestId"
                                            FROM "CALC"."V_EVALUATIONS" AS e 
											WHERE r."Id" = (e."Context" ->> 'requestId')::bigint 
												AND (e."ScoreResult" IS NOT NULL OR r_kpi."SSI" IS NOT NULL)) psir ON TRUE
                 WHERE
                     r."QueueId" = queues."Id"
                 ) rbq
                                ON TRUE
                      LEFT JOIN LATERAL (
                 SELECT
                     sum(ore."RT") AS "sumRT",
                     COUNT(ore."RT") AS "countRT",
                     sum(ore."RT") / COUNT(ore."RT") AS "ART",
                     AVG(ore."RT") AS "A1RT",
                     SUM(ore."RT") AS "sum1RT",
                     MAX(ore."RT")::numeric (10, 0) AS "MAX_1RT",
                     COUNT(DISTINCT ore."RequestId") AS "count1RT"
                 FROM art_req_kpi ore
                 WHERE ore."QueueId" = queues."Id"
                 ) art_req ON TRUE
                      LEFT JOIN LATERAL (
                 select
                     max(
                             CASE
                                 WHEN "CustomAttributes"."Key" = 'Queue.ThresholdValues.AlarmASA'::text 
                                 THEN NULLIF(COALESCE("CustomAttributes"."Value", '0'::character varying), '')::double precision
                                 ELSE NULL::double precision
                                 END) AS "ASA_ALARM",
                     max(
                             CASE
                                 WHEN "CustomAttributes"."Key" = 'Queue.ThresholdValues.WarningASA'::text 
                                 THEN NULLIF(COALESCE("CustomAttributes"."Value", '0'::character varying), '')::double precision
                                 ELSE NULL::double precision
                                 END) AS "ASA_WARNING",
                     max(
                             CASE
                                 WHEN "CustomAttributes"."Key" = 'Queue.ThresholdValues.AlarmAHT'::text 
                                 THEN NULLIF(COALESCE("CustomAttributes"."Value", '0'::character varying), '')::double precision
                                 ELSE NULL::double precision
                                 END) AS "AHT_ALARM",
                     max(
                             CASE
                                 WHEN "CustomAttributes"."Key" = 'Queue.ThresholdValues.WarningAHT'::text 
                                 THEN NULLIF(COALESCE("CustomAttributes"."Value", '0'::character varying), '')::double precision
                                 ELSE NULL::double precision
                                 END) AS "AHT_WARNING",
                     max(
                             CASE
                                 WHEN "CustomAttributes"."Key" = 'Queue.ThresholdValues.AlarmACSI'::text 
                                 THEN NULLIF(COALESCE("CustomAttributes"."Value", '0'::character varying), '')::double precision
                                 ELSE NULL::double precision
                                 END) AS "ACSI_ALARM",
                     max(
                             CASE
                                 WHEN "CustomAttributes"."Key" = 'Queue.ThresholdValues.WarningACSI'::text 
                                 THEN NULLIF(COALESCE("CustomAttributes"."Value", '0'::character varying), '')::double precision
                                 ELSE NULL::double precision
                                 END) AS "ACSI_WARNING",
                     max(
                             CASE
                                 WHEN "CustomAttributes"."Key" = 'Queue.ThresholdValues.AlarmClientResponseTimeout'::text 
                                 THEN NULLIF(COALESCE("CustomAttributes"."Value", '0'::character varying), '')::double precision
                                 ELSE NULL::double precision
                                 END) AS "ART_ALARM",
                     max(
                             CASE
                                 WHEN "CustomAttributes"."Key" = 'Queue.ThresholdValues.WarningClientResponseTimeout'::text 
                                 THEN NULLIF(COALESCE("CustomAttributes"."Value", '0'::character varying), '')::double precision
                                 ELSE NULL::double precision
                                 END) AS "ART_WARNING",
                     COALESCE(max(
                                      CASE
                                          WHEN "CustomAttributes"."Key" = 'Queue.ThresholdValues.MaxQueueLength'::text 
                                          THEN NULLIF(COALESCE("CustomAttributes"."Value", '0'::character varying), '')::double precision
                                          ELSE NULL::double precision
                                          END), 0::double precision)::numeric(10, 2)  AS "MAX_QUEUE_LENGTH",
                     max(
                             CASE
                                 WHEN "CustomAttributes"."Key" = 'Queue.ThresholdValues.AcwTime'::text 
                                 THEN NULLIF(COALESCE("CustomAttributes"."Value", '0'::character varying), '')::double precision
                                 ELSE NULL::double precision
                                 END) AS "ACW_ALARM",
                     max(
                             CASE
                                 WHEN "CustomAttributes"."Key" = 'Queue.ThresholdValues.WarningAcwTime'::text 
                                 THEN NULLIF(COALESCE("CustomAttributes"."Value", '0'::character varying), '')::double precision
                                 ELSE NULL::double precision
                                 END) AS "ACW_WARNING",
                     max(
                             CASE
                                 WHEN "CustomAttributes"."Key" = 'Queue.ThresholdValues.AlarmQWT'::text 
                                 THEN NULLIF(COALESCE("CustomAttributes"."Value", '0'::character varying), '')::double precision
                                 ELSE NULL::double precision
                                 END) AS "QWT_ALARM",
                     max(
                             CASE
                                 WHEN "CustomAttributes"."Key" = 'Queue.ThresholdValues.WarningQWT'::text 
                                 THEN NULLIF(COALESCE("CustomAttributes"."Value", '0'::character varying), '')::double precision
                                 ELSE NULL::double precision
                                 END) AS "QWT_WARNING",
                     max(
                             CASE
                                 WHEN "CustomAttributes"."Key" = 'Queue.ThresholdValues.AlarmCP'::text 
                                 THEN NULLIF(COALESCE("CustomAttributes"."Value", '0'::character varying), '')::double precision
                                 ELSE NULL::double precision
                                 END) AS "TST_ALARM",
                     max(
                             CASE
                                 WHEN "CustomAttributes"."Key" = 'Queue.ThresholdValues.WarningCP'::text 
                                 THEN NULLIF(COALESCE("CustomAttributes"."Value", '0'::character varying), '')::double precision
                                 ELSE NULL::double precision
                                 END) AS "TST_WARNING",
                     max(
                             CASE
                                 WHEN "CustomAttributes"."Key" = 'Queue.ThresholdValues.AlarmSessionInactivityTimer'::text 
                                 THEN NULLIF(COALESCE("CustomAttributes"."Value", '0'::character varying), '')::double precision
                                 ELSE NULL::double precision
                                 END) AS "SIT_ALARM",
                     max(
                             CASE
                                 WHEN "CustomAttributes"."Key" = 'Queue.ThresholdValues.WarningSessionInactivityTimer'::text 
                                 THEN NULLIF(COALESCE("CustomAttributes"."Value", '0'::character varying), '')::double precision
                                 ELSE NULL::double precision
                                 END) AS "SIT_WARNING"
                 FROM "CRPM_CFG"."CustomAttributes"
                 WHERE "CustomAttributes"."QueueId" = queues."Id" and "CustomAttributes"."Key" = ANY (ARRAY['Queue.ThresholdValues.AlarmASA'::text, 'Queue.ThresholdValues.WarningASA'::text, 'Queue.ThresholdValues.AlarmAHT'::text, 'Queue.ThresholdValues.WarningAHT'::text, 'Queue.ThresholdValues.AlarmACSI'::text, 'Queue.ThresholdValues.WarningACSI'::text, 'Queue.ThresholdValues.AlarmClientResponseTimeout'::text, 'Queue.ThresholdValues.WarningClientResponseTimeout'::text, 'Queue.ThresholdValues.MaxQueueLength'::text, 'Queue.ThresholdValues.AcwTime'::text, 'Queue.ThresholdValues.WarningAcwTime'::text, 'Queue.ThresholdValues.AlarmQWT'::text, 'Queue.ThresholdValues.WarningQWT'::text, 'Queue.ThresholdValues.AlarmCP'::text, 'Queue.ThresholdValues.WarningCP'::text, 'Queue.ThresholdValues.AlarmSessionInactivityTimer'::text, 'Queue.ThresholdValues.WarningSessionInactivityTimer'::text])
                 ) alarms_queue ON TRUE
                      LEFT JOIN operator_root_group ON operator_root_group."QueueId" = queues."Id"
     )
SELECT "ID",                                    -- Id очереди
       "TITLE",                                 -- Название очереди
       "QUEUE_TIME_DELETED",                    -- Время удаление очереди (Для фильтрации)
       "TOTAL_REQUESTS",                        -- Всего
       "NEW_REQUESTS",                          -- Новые
       "REMAINDER",                             -- Перешедшие остатком
       "AUTOPROCESSING",                        -- Автообработка
       "PENDING_OPERATOR_RESPONSE",             -- Ожидают обработки оператора
       "WAITING",                               -- Ожидание
       "CLOSED_REQUESTS",                       -- Закрытые Всего
       "CLOSED_REQUESTS_BY_OPERATOR",           -- Закрытые Оператором
       "CLOSED_REQUESTS_BY_SUPERVISOR",         -- Закрытые Супервизором
       "CLOSED_REQUESTS_BY_BOT",                -- Закрытые БОТом
       "CLOSED_REQUESTS_BY_SYSTEM",             -- Закрытые Системой
       "PROCESSING_BY_OPERATOR",                -- Обрабатываются оператором
       "CLOSED_REQUESTS_BY_BOT_PERCENT",        -- % автоматиации (Доля обращений, которые были закрыты БОТом)
       "LOST",                                  -- Потеряно
       "LOST_PERCENT",                          -- LCR
       "ESCALATED_BY_DISTRIBUTION",             -- Просрочено по распределению (Кол-во обращений очереди, которые были просрочены по сроку распределения)
       "ESCALATED_BY_DECISION",                 -- Просрочено по решению (Кол-во обращений очереди, которые были просрочены по сроку решения)
       "ADT",                                   -- ADT
       "MAX_DT",                                -- Max DT
       "ACW",                                   -- ACW
       "CW_COUNT",
       "CW_SUM",
       "ASA",                                   -- ASA
       "ASA_ALARM",
       "ASA_WARNING",
       "SA_COUNT",
       "SA_SUM",
       "SA_MIN",
       "SA_MAX",
       "AHT",                                   -- AHT
       "AHT_ALARM",
       "AHT_WARNING",
       "HT_COUNT",
       "HT_SUM",
       "HT_MIN",
       "HT_MAX",
       "ART",                                   -- ART
       "ART_ALARM",
       "ART_WARNING",
       "ART_SUM",
       "ART_COUNT",
       "A1RT",                                  -- A1RT
       "A1RT_SUM",
       "A1RT_COUNT",
       "A1RT_MAX",
       "ACSI",                                  -- ACSI
       "ACSI_ALARM",
       "ACSI_WARNING",
       "CSI_COUNT",
       "CSI_SUM",
       "CSI_GROUP_COUNT",
       "CSI_TOTAL_COUNT",
       "ASSI",                                  -- ASSI
       "SSI_GROUP_COUNT",
       "SSI_COUNT",
       "SSI_SUM",
       "CSAT",                                  -- CSAT
       "CSAT_COUNT",
       "CDSAT",                                 -- CDSAT
       "CDSAT_COUNT",
       "REDIRECTED",                            -- Переведено по очереди
       "REDIRECTED_PERCENT",                    -- Переведено %
       "SL",
       "SL_PERCENT",                            -- % обращений, по которым клиент получил ответ за заданное для очереди целевое время ASA, от общего количества обращений в очереди 
       "NOW",
       "MAX_QUEUE_LENGTH",
       "CLOSED_REQUESTS_BY_OPERATOR_LAST_HOUR", -- Закрыто за _ENDDATE - 1ч
       "ASA_LAST_HOUR",
       "SA_COUNT_LAST_HOUR",
       "SA_SUM_LAST_HOUR",
       "LOST_LAST_HOUR",                        --  Потеряно за _ENDDATE - 1ч
       "PENDING_FIRST_OPERATOR_RESPONSE",       -- Ожидает первого ответа оператора

       "CURRENT_MAX_SA",
       "ACW_ALARM",
       "ACW_WARNING",
       "QWT_ALARM",
       "QWT_WARNING",
       "TST_ALARM",
       "TST_WARNING",
       "SIT_ALARM",
       "SIT_WARNING",
       "DIVISION_NAME",
       "DIVISION_ID",
       "WT_COUNT",
       "WT_SUM",
       "REQUEST_LIFETIME_COUNT",
       "REQUEST_LIFETIME_SUM"
FROM res AS r;



$function$
;


INSERT INTO "CALC"."__CalcContextMigrations" ("MigrationId", "ProductVersion") VALUES ('20250324114728_ExtEval_RequestId', '9.0.1');