#!/bin/bash

echo -e "[$(date '+%d.%m.%Y %T.%3N')] Create service account user mappings" && tput sgr0

#includes
. /.config.sh || exit 1

#Export database password
export PGPASSWORD=${DBPASSWORD}

#Temp settings group=false for RMQ applications

psql -h {transform:DbServer} -p {transform:DbServerPort} -U {transform:DbUser} -d {transform:DbName} -c "UPDATE \"CRED_MAN\".\"APPLICATIONS\" SET \"IS_GROUP\"=false WHERE \"NAME\"='rabbit';" || exit 1
echo -e "[$(date '+%d.%m.%Y %T.%3N')] Change rabbit application done" && tput sgr0
psql -h {transform:DbServer} -p {transform:DbServerPort} -U {transform:DbUser} -d {transform:DbName} -c "UPDATE \"CRED_MAN\".\"APPLICATIONS\" SET \"IS_GROUP\"=false WHERE \"NAME\"='rabbitDmz';" || exit 1
echo -e "[$(date '+%d.%m.%Y %T.%3N')] Change rabbitDmz application done" && tput sgr0

#Sleep
sleep 10

#Data init
# Заполняем для учётки service-account-product.service - d8009ea8-4408-43b3-b9ae-0a10e8a467f2
# Получить токен из KeyCloack
echo -e "[$(date '+%d.%m.%Y %T.%3N')] Get token from URL 'https://{transform:kcUrl}/realms/{transform:kcRealm}/protocol/openid-connect/token'." && tput sgr0

auth_token=$(curl -L 'https://{transform:kcUrl}/realms/{transform:kcRealm}/protocol/openid-connect/token' -H 'Content-Type: application/x-www-form-urlencoded' -d 'client_id=product.service' -d 'grant_type=client_credentials' -d 'client_secret=TMFVYLhrwwBQbYmGjK3zt53EYPzMiSha' | grep -Po '"access_token": *"\K(?:\\"|[^"])*') || exit 1
echo -e "[$(date '+%d.%m.%Y %T.%3N')] Get token operation complete" && tput sgr0

# Для учётки service-account-product.service устанавливаем креды на приложения
curl -X 'POST' 'https://{transform:AppHostname}/credman/utilization/CredentialsManaging/SetCredentials?applicationName=CALC_DB' -H 'Content-Type: application/json' -H "Authorization: Bearer ${auth_token}" -d '{"Password": "{transform:DbPassword}"}' || exit 1
echo -e "[$(date '+%d.%m.%Y %T.%3N')] Set service account credentials for application CALC_DB complete" && tput sgr0
curl -X 'POST' 'https://{transform:AppHostname}/credman/utilization/CredentialsManaging/SetCredentials?applicationName=KPI_DB' -H 'Content-Type: application/json' -H "Authorization: Bearer ${auth_token}" -d '{"Password": "{transform:DbPassword}"}' || exit 1
echo -e "[$(date '+%d.%m.%Y %T.%3N')] Set service account credentials for application KPI_DB complete" && tput sgr0
curl -X 'POST' 'https://{transform:AppHostname}/credman/utilization/CredentialsManaging/SetCredentials?applicationName=PSA_DB' -H 'Content-Type: application/json' -H "Authorization: Bearer ${auth_token}" -d '{"Password": "{transform:DbPassword}"}' || exit 1
echo -e "[$(date '+%d.%m.%Y %T.%3N')] Set service account credentials for application PSA_DB complete" && tput sgr0
curl -X 'POST' 'https://{transform:AppHostname}/credman/utilization/CredentialsManaging/SetCredentials?applicationName=PSA_PG' -H 'Content-Type: application/json' -H "Authorization: Bearer ${auth_token}" -d '{"Password": "{transform:DbPassword}"}' || exit 1
echo -e "[$(date '+%d.%m.%Y %T.%3N')] Set service account credentials for application PSA_PG complete" && tput sgr0
#Service & users
curl -X 'POST' 'https://{transform:AppHostname}/credman/utilization/CredentialsManaging/SetCredentials?applicationName=rabbit' -H 'Content-Type: application/json' -H "Authorization: Bearer ${auth_token}" -d '{"Password": "{transform:rabbitPassword}"}' || exit 1
echo -e "[$(date '+%d.%m.%Y %T.%3N')] Set service account credentials for application rabbit complete" && tput sgr0
curl -X 'POST' 'https://{transform:AppHostname}/credman/utilization/CredentialsManaging/SetCredentials?applicationName=rabbitDmz' -H 'Content-Type: application/json' -H "Authorization: Bearer ${auth_token}" -d '{"Password": "{transform:rabbitDmzPassword}"}' || exit 1
echo -e "[$(date '+%d.%m.%Y %T.%3N')] Set service account credentials for application rabbitDmz complete" && tput sgr0

# Добавляем креды для следующих групп
##
# Группа админов:
# product-admins - ec8b41c1-c261-4422-80b5-9c52a8d312d0
# Группа супервизоров:
# product-supervisor - dc2535c6-bbed-4814-b2b4-c7d2a237c500
# Группа юзеров:
# product-users - acf981c2-6add-4160-9f97-610aad738cb9
#
# Группа админов:
# product-admins - ec8b41c1-c261-4422-80b5-9c52a8d312d0
# Для приложения rabbit

 curl -X 'POST' 'https://{transform:AppHostname}/credman/managing/ApplicationAdministration/SetUserCredentials?applicationName=rabbit&identityId=ec8b41c1-c261-4422-80b5-9c52a8d312d0' -H 'accept: */*' -H 'Content-Type: application/json' \
  -H "Authorization: Bearer ${auth_token}" \
  -d '{ "Password": "{transform:rabbitPassword}" }' || exit 1
echo -e "[$(date '+%d.%m.%Y %T.%3N')] Set admin group credentials for application rabbit complete" && tput sgr0

# Группа админов:
# product-admins - ec8b41c1-c261-4422-80b5-9c52a8d312d0
# Для приложения rabbitDmz

 curl -X 'POST' 'https://{transform:AppHostname}/credman/managing/ApplicationAdministration/SetUserCredentials?applicationName=rabbitDmz&identityId=ec8b41c1-c261-4422-80b5-9c52a8d312d0' -H 'accept: */*' -H 'Content-Type: application/json' \
  -H "Authorization: Bearer ${auth_token}" \
  -d '{ "Password": "{transform:rabbitDmzPassword}" }' || exit 1
echo -e "[$(date '+%d.%m.%Y %T.%3N')] Set admin group credentials for application rabbitDmz complete" && tput sgr0

# Группа супервизоров:
# product-supervisor - dc2535c6-bbed-4814-b2b4-c7d2a237c500
# Для приложения rabbit

 curl -X 'POST' 'https://{transform:AppHostname}/credman/managing/ApplicationAdministration/SetUserCredentials?applicationName=rabbit&identityId=dc2535c6-bbed-4814-b2b4-c7d2a237c500' -H 'accept: */*' -H 'Content-Type: application/json' \
  -H "Authorization: Bearer ${auth_token}" \
  -d '{ "Password": "{transform:rabbitPassword}" }' || exit 1
echo -e "[$(date '+%d.%m.%Y %T.%3N')] Set supervisor group credentials for application rabbit complete" && tput sgr0
# Группа супервизоров:
# product-supervisor - dc2535c6-bbed-4814-b2b4-c7d2a237c500
# Для приложения rabbitDmz

 curl -X 'POST' 'https://{transform:AppHostname}/credman/managing/ApplicationAdministration/SetUserCredentials?applicationName=rabbitDmz&identityId=dc2535c6-bbed-4814-b2b4-c7d2a237c500' -H 'accept: */*' -H 'Content-Type: application/json' \
  -H "Authorization: Bearer ${auth_token}" \
  -d '{ "Password": "{transform:rabbitDmzPassword}" }' || exit 1
echo -e "[$(date '+%d.%m.%Y %T.%3N')] Set supervisor group credentials for application rabbitDmz complete" && tput sgr0
# Группа юзеров:
# product-users - acf981c2-6add-4160-9f97-610aad738cb9
# Для приложения rabbit

 curl -X 'POST' 'https://{transform:AppHostname}/credman/managing/ApplicationAdministration/SetUserCredentials?applicationName=rabbit&identityId=acf981c2-6add-4160-9f97-610aad738cb9' -H 'accept: */*' -H 'Content-Type: application/json' \
  -H "Authorization: Bearer ${auth_token}" \
  -d '{ "Password": "{transform:rabbitPassword}" }'
echo -e "[$(date '+%d.%m.%Y %T.%3N')] Set user group credentials for application rabbit complete" && tput sgr0
# Группа юзеров:
# product-users - acf981c2-6add-4160-9f97-610aad738cb9
# Для приложения rabbitDmz

 curl -X 'POST' 'https://{transform:AppHostname}/credman/managing/ApplicationAdministration/SetUserCredentials?applicationName=rabbitDmz&identityId=acf981c2-6add-4160-9f97-610aad738cb9' -H 'accept: */*' -H 'Content-Type: application/json' \
  -H "Authorization: Bearer ${auth_token}" \
  -d '{ "Password": "{transform:rabbitDmzPassword}" }' || exit 1
echo -e "[$(date '+%d.%m.%Y %T.%3N')] Set user group credentials for application rabbitDmz complete" && tput sgr0
#Revoke temp settings group=false for RMQ applications

psql -h {transform:DbServer} -p {transform:DbServerPort} -U {transform:DbUser} -d {transform:DbName} -c "UPDATE \"CRED_MAN\".\"APPLICATIONS\" SET \"IS_GROUP\"=true WHERE \"NAME\"='rabbit';" || exit 1
echo -e "[$(date '+%d.%m.%Y %T.%3N')] Change rabbit application done" && tput sgr0
psql -h {transform:DbServer} -p {transform:DbServerPort} -U {transform:DbUser} -d {transform:DbName} -c "UPDATE \"CRED_MAN\".\"APPLICATIONS\" SET \"IS_GROUP\"=true WHERE \"NAME\"='rabbitDmz';" || exit 1
echo -e "[$(date '+%d.%m.%Y %T.%3N')] Change rabbitDmz application done" && tput sgr0

echo -e " Инициализация модуля Credential Manager успешно выполнена" && tput sgr0
exit 1