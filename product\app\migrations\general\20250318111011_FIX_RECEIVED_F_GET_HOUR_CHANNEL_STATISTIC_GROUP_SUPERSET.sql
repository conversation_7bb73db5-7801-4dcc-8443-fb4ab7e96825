DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "CALC"."__CalcContextMigrations" WHERE "MigrationId" = '20250318111011_FIX_RECEIVED_F_GET_HOUR_CHANNEL_STATISTIC_GROUP_SUPERSET') THEN
	CREATE OR REPLACE FUNCTION "CALC"."F_GET_HOUR_CHANNEL_STATISTIC_GROUP_SUPERSET"(
	_group_name character varying DEFAULT 'Все'::character varying,
	_startdate timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
	_enddate timestamp without time zone DEFAULT ((CURRENT_TIMESTAMP + '23:00:00'::interval))::date)
    RETURNS TABLE("DateTime" timestamp without time zone, "Total_Channel" character varying, "Total_Received" bigint, "Total_Lost" bigint, "Total_Skipped" bigint, "Total_Accepted" bigint, "Total_AHT" bigint, "Total_ASA" bigint) 
    LANGUAGE 'sql'
    COST 100
    VOLATILE PARALLEL UNSAFE
    ROWS 1000

AS $BODY$
WITH
    channels_dict AS(
        Select
            'Видео' AS "Key",
            'CHANNEL.VIDEOCHAT' AS "Value"
        UNION
        Select
            'Голос' AS "Key",
            'CHANNEL.VOICE' AS "Value"
        UNION
        Select
            'Чаты' AS "Key",
            'CHANNEL.CHAT, CHANNEL.FACEBOOK, CHANNEL.IMESSAGE, CHANNEL.INFOBIP.WHATSAPP, CHANNEL.MFMS.WHATSAPP, CHANNEL.MF.WHATSAPP, CHANNEL.SMS, CHANNEL.TELEGRAM, CHANNEL.VIBER, CHANNEL.VK, CHANNEL.VKPUBLIC' AS "Value"
        UNION
        Select
            'Прочее' AS "Key",
            'CHANNEL.DEFAULT, CHANNEL.EXCHANGE, CHANNEL.FEEDBACKFORM, ' AS "Value"
        UNION
        Select
            'Все' AS "Key",
            (Select string_agg(DISTINCT(ch."Channel"), ', ') FROM "CALC"."V_CHANNELS_INFO" ch) AS "Value"
    ),current_channels AS (
    Select channels_dict."Value" FROM channels_dict WHERE channels_dict."Key" = _group_name -- переменная поступает в функцию
	
),date_hour AS(
    Select
		date_trunc('hour', start)::timestamp AS "DateTime"
    FROM (Select generate_series(_startdate, (_enddate - '1 hour'::interval), '1 hour') AS start) x
	
),channels AS(
    Select ch."Channel" FROM "CALC"."V_CHANNELS_INFO" ch GROUP BY  ch."Channel"
	
),stub AS(
    Select * FROM date_hour
                      CROSS JOIN channels
    ORDER BY date_hour."DateTime", channels."Channel"
	
),received AS (
    Select
		  date_trunc('hour', r."TimeCreated" AT TIME ZONE 'UTC')::timestamp AS "DateTime"
         ,r."Channel"::character varying
         ,COUNT(r_kpi."Id") as "Received" -- поступило по каналу за час
         ,SUM(r_kpi."HT") as "HT_SUM"
         ,COUNT(r_kpi."HT") as "HT_COUNT"
         ,SUM(r_kpi."SA") as "SA_SUM"
         ,COUNT(r_kpi."SA") as "SA_COUNT"
    FROM "CRPM_DATA"."Requests" r
             LEFT JOIN "KPI"."REQUEST_KPI" r_kpi ON r."Id" = r_kpi."Id"
    WHERE r."TimeCreated" BETWEEN _startdate AND (_enddate - '1 hour'::interval)
    GROUP BY date_trunc('hour', r."TimeCreated" AT TIME ZONE 'UTC'), r."Channel" -- разрез одного часа
	
),accepted AS (
    Select
	     date_trunc('hour', r_kpi."TimeOperatorFirstDistribution" AT TIME ZONE 'UTC')::timestamp AS "DateTime"
         ,r."Channel"::character varying
         ,COUNT(r_kpi."TimeOperatorFirstDistribution") as "Accepted" -- принято по каналу за час
    FROM "CRPM_DATA"."Requests" r
             LEFT JOIN "KPI"."REQUEST_KPI" r_kpi ON r."Id" = r_kpi."Id"
    WHERE r_kpi."TimeOperatorFirstDistribution" BETWEEN _startdate AND (_enddate - '1 hour'::interval)
    GROUP BY date_trunc('hour', r_kpi."TimeOperatorFirstDistribution" AT TIME ZONE 'UTC'), r."Channel" -- разрез одного часа
	
),lost AS (
    Select
		  date_trunc('hour', r_kpi."TimeLost" AT TIME ZONE 'UTC')::timestamp AS "DateTime"
         ,r."Channel"::character varying
         ,COUNT(r_kpi."TimeLost") as "Lost" -- потеряно по каналу за час
    FROM "CRPM_DATA"."Requests" r
             LEFT JOIN "KPI"."REQUEST_KPI" r_kpi ON r."Id" = r_kpi."Id"
    WHERE r_kpi."TimeLost" BETWEEN _startdate AND (_enddate - '1 hour'::interval)
    GROUP BY date_trunc('hour', r_kpi."TimeLost" AT TIME ZONE 'UTC'), r."Channel" -- разрез одного часа
	
),skipped AS (
    Select
	     date_trunc('hour', callState."TimeCallMissed" AT TIME ZONE 'UTC')::timestamp AS "DateTime"
         ,r."Channel"::character varying
         ,COUNT(callState."TimeCallMissed") as "Skipped" -- пропущено по каналу за час
    FROM "CRPM_DATA"."Requests" r
             LEFT JOIN "KPI"."OPERATOR_CALL_STATE" callState ON callState."RequestId" = r."Id"
    WHERE callState."TimeCallMissed" BETWEEN _startdate AND (_enddate - '1 hour'::interval)
    GROUP BY date_trunc('hour', callState."TimeCallMissed" AT TIME ZONE 'UTC'), r."Channel" -- разрез одного часа
	
),real_data AS (
    Select
          COALESCE(received."DateTime", accepted."DateTime", lost."DateTime", skipped."DateTime") AS "DateTime"
         ,COALESCE(received."Channel", accepted."Channel", lost."Channel", skipped."Channel") AS "Channel"
         ,COALESCE(received."Received", 0) AS "Received"
         ,COALESCE(accepted."Accepted", 0) AS "Accepted"
         ,COALESCE(lost."Lost", 0) AS "Lost"
         ,COALESCE(skipped."Skipped", 0) AS "Skipped"
         ,received."HT_SUM" AS "HT_SUM"
         ,received."HT_COUNT" AS "HT_COUNT"
         ,received."SA_SUM" AS "SA_SUM"
         ,received."SA_COUNT" AS "SA_COUNT"
    FROM received
             FULL JOIN accepted ON received."DateTime" = accepted."DateTime" AND received."Channel" = accepted."Channel"
             FULL JOIN lost ON received."DateTime" = lost."DateTime" AND received."Channel" = lost."Channel"
             FULL JOIN skipped ON received."DateTime" = skipped."DateTime" AND received."Channel" = skipped."Channel"
			 
),real_data_with_stub AS (
    Select
		  stub."DateTime" AS "DateTime"
         ,stub."Channel" AS "Channel"
         ,COALESCE(real_data."Received", 0) AS "Received"
         ,COALESCE(real_data."Lost", 0) AS "Lost"
         ,COALESCE(real_data."Skipped", 0) AS "Skipped"
         ,COALESCE(real_data."Accepted", 0) AS "Accepted"
         ,COALESCE(real_data."HT_SUM" / (CASE WHEN real_data."HT_COUNT" IS NOT NULL AND real_data."HT_COUNT" > 0 THEN real_data."HT_COUNT" ELSE NULL::bigint END), 0)::bigint AS "AHT" -- AHT за час по конкретному каналу
         ,COALESCE(real_data."SA_SUM" / (CASE WHEN real_data."SA_COUNT" IS NOT NULL AND real_data."SA_COUNT" > 0 THEN real_data."SA_COUNT" ELSE NULL::bigint END), 0)::bigint AS "ASA"
         ,COALESCE(real_data."HT_SUM", 0) AS "HT_SUM"
         ,COALESCE(real_data."HT_COUNT", 0) AS "HT_COUNT"
         ,COALESCE(real_data."SA_SUM", 0) AS "SA_SUM"
         ,COALESCE(real_data."SA_COUNT", 0) AS "SA_COUNT"
    FROM stub
             LEFT JOIN real_data ON real_data."DateTime" = stub."DateTime" AND real_data."Channel" = stub."Channel"
    ORDER BY stub."DateTime", stub."Channel"
	
),real_data_with_stub_for_channel AS (
    Select * FROM real_data_with_stub
    WHERE (Select COUNT(*) FROM current_channels WHERE current_channels."Value" like '%' || real_data_with_stub."Channel" || '%') > 0
	
),hour_group AS (
    Select
		  real_data_with_stub_for_channel."DateTime"
         ,'CHANNEL.ALL' AS "Channel"
         ,SUM(real_data_with_stub_for_channel."Received")::bigint as "Received" -- поступило всего за час
         ,SUM(real_data_with_stub_for_channel."Lost")::bigint as "Lost" -- потеряно всего за час
         ,SUM(real_data_with_stub_for_channel."Skipped")::bigint as "Skipped" -- пропущено всего за час
         ,SUM(real_data_with_stub_for_channel."Accepted")::bigint as "Accepted" -- принято всего за час
         ,COALESCE((SUM(real_data_with_stub_for_channel."HT_SUM") / (CASE WHEN SUM(real_data_with_stub_for_channel."HT_COUNT") > 0 THEN SUM(real_data_with_stub_for_channel."HT_COUNT") ELSE NULL::bigint END)),0)::bigint as "AHT" -- AHT за час по всем каналам
         ,COALESCE((SUM(real_data_with_stub_for_channel."SA_SUM") /  (CASE WHEN SUM(real_data_with_stub_for_channel."SA_COUNT")  > 0 THEN  SUM(real_data_with_stub_for_channel."SA_COUNT") ELSE NULL::bigint END)),0)::bigint as "ASA" -- "ASA" за час по всем каналам
         ,SUM(real_data_with_stub_for_channel."HT_SUM") AS "HT_SUM"
         ,SUM(real_data_with_stub_for_channel."HT_COUNT") AS "HT_COUNT"
         ,SUM(real_data_with_stub_for_channel."SA_SUM") AS "SA_SUM"
         ,SUM(real_data_with_stub_for_channel."SA_COUNT") AS "SA_COUNT"
    from real_data_with_stub_for_channel
    group by real_data_with_stub_for_channel."DateTime"
)

--Select * FROM hour_group
   ,hour_group_total AS (
    Select *
    FROM real_data_with_stub_for_channel
    UNION Select * FROM hour_group
)

--Select * FROM hour_group_total ORDER BY hour_group_total."Date", hour_group_total."Hour"
   ,total AS (
    Select * FROM hour_group_total ORDER BY hour_group_total."Channel"
)

--Select * FROM total
Select
	  total."DateTime"
     ,total."Channel" AS "Hour_Total_Channel"
     ,total."Received" AS "Hour_Total_Received"
     ,total."Lost" AS "Hour_Total_Lost"
     ,total."Skipped" AS "Hour_Total_Skipped"
     ,total."Accepted" AS "Hour_Total_Accepted"
     ,total."AHT" AS "Hour_Total_AHT"
     ,total."ASA" AS "Hour_Total_ASA"
FROM total
WHERE total."Channel" = 'CHANNEL.ALL'
ORDER BY total."DateTime"

$BODY$;

ALTER FUNCTION "CALC"."F_GET_HOUR_CHANNEL_STATISTIC_GROUP_SUPERSET"(character varying, timestamp without time zone, timestamp without time zone)
    OWNER TO postgres;

COMMENT ON FUNCTION "CALC"."F_GET_HOUR_CHANNEL_STATISTIC_GROUP_SUPERSET"(character varying, timestamp without time zone, timestamp without time zone)
    IS 'Функция для подсчета статистики обращений по часам по группе каналов за заданный промежуток дат для суперсета';

    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "CALC"."__CalcContextMigrations" WHERE "MigrationId" = '20250318111011_FIX_RECEIVED_F_GET_HOUR_CHANNEL_STATISTIC_GROUP_SUPERSET') THEN
    INSERT INTO "CALC"."__CalcContextMigrations" ("MigrationId", "ProductVersion")
    VALUES ('20250318111011_FIX_RECEIVED_F_GET_HOUR_CHANNEL_STATISTIC_GROUP_SUPERSET', '9.0.1');
    END IF;
END $EF$;
